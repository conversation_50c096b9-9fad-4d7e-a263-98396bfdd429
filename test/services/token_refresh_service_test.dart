import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:albalad_operator_app/shared/services/token_refresh_service.dart';
import 'package:albalad_operator_app/features/authentication/services/auth_services.dart';
import 'package:albalad_operator_app/shared/helper/secure_storage_helper.dart';
import 'package:dio/dio.dart';
import 'dart:convert';

// Generate mocks
@GenerateMocks([AuthServices, SecureStorageHelper])
import 'token_refresh_service_test.mocks.dart';

void main() {
  group('TokenRefreshService', () {
    late TokenRefreshService tokenRefreshService;
    late MockAuthServices mockAuthServices;
    late MockSecureStorageHelper mockSecureStorageHelper;

    setUp(() {
      tokenRefreshService = TokenRefreshService();
      mockAuthServices = MockAuthServices();
      mockSecureStorageHelper = MockSecureStorageHelper();
    });

    test('should refresh token successfully', () async {
      // Arrange
      const refreshToken = 'test_refresh_token';
      const newAccessToken = 'new_access_token';
      
      final userData = {
        'token': {
          'access_token': 'old_access_token',
          'refresh_token': refreshToken,
          'expires_in': '3600'
        }
      };

      final refreshResponse = Response(
        requestOptions: RequestOptions(path: ''),
        statusCode: 200,
        data: {
          'result': 'success',
          'token': {
            'access_token': newAccessToken,
            'refresh_token': 'new_refresh_token',
            'expires_in': '3600'
          }
        },
      );

      // Mock secure storage to return user data
      when(mockSecureStorageHelper.getData('user'))
          .thenAnswer((_) async => jsonEncode(userData));

      // Mock auth service to return successful refresh response
      when(mockAuthServices.refreshToken(refreshToken))
          .thenAnswer((_) async => refreshResponse);

      // Mock secure storage save
      when(mockSecureStorageHelper.saveData(any, any))
          .thenAnswer((_) async => true);

      // Act
      final result = await tokenRefreshService.refreshAccessToken();

      // Assert
      expect(result, equals(newAccessToken));
      verify(mockAuthServices.refreshToken(refreshToken)).called(1);
      verify(mockSecureStorageHelper.saveData('user', any)).called(1);
    });

    test('should return null when refresh token is missing', () async {
      // Arrange
      final userData = {
        'token': {
          'access_token': 'old_access_token',
          'expires_in': '3600'
          // No refresh_token
        }
      };

      when(mockSecureStorageHelper.getData('user'))
          .thenAnswer((_) async => jsonEncode(userData));

      // Act
      final result = await tokenRefreshService.refreshAccessToken();

      // Assert
      expect(result, isNull);
      verifyNever(mockAuthServices.refreshToken(any));
    });

    test('should return null when user data is not found', () async {
      // Arrange
      when(mockSecureStorageHelper.getData('user'))
          .thenAnswer((_) async => null);

      // Act
      final result = await tokenRefreshService.refreshAccessToken();

      // Assert
      expect(result, isNull);
      verifyNever(mockAuthServices.refreshToken(any));
    });

    test('should handle refresh API failure', () async {
      // Arrange
      const refreshToken = 'test_refresh_token';
      
      final userData = {
        'token': {
          'access_token': 'old_access_token',
          'refresh_token': refreshToken,
          'expires_in': '3600'
        }
      };

      final refreshResponse = Response(
        requestOptions: RequestOptions(path: ''),
        statusCode: 400,
        data: {
          'result': 'failure',
          'message': 'Invalid refresh token'
        },
      );

      when(mockSecureStorageHelper.getData('user'))
          .thenAnswer((_) async => jsonEncode(userData));

      when(mockAuthServices.refreshToken(refreshToken))
          .thenAnswer((_) async => refreshResponse);

      // Act
      final result = await tokenRefreshService.refreshAccessToken();

      // Assert
      expect(result, isNull);
      verify(mockAuthServices.refreshToken(refreshToken)).called(1);
      verifyNever(mockSecureStorageHelper.saveData(any, any));
    });
  });
}
