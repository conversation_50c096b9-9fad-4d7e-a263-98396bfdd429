import 'package:albalad_operator_app/features/profile/provider/profile_provider.dart';
import 'package:albalad_operator_app/providers/global_providers.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/widgets/notification_button.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:skeletonizer/skeletonizer.dart';

class HomeAppBar extends ConsumerWidget implements PreferredSizeWidget {
  const HomeAppBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final profileAsync = ref.watch(profileProvider);
    return PreferredSize(
      preferredSize: Size.fromHeight(85.h), // Set your custom height here
      child: AppBar(
        backgroundColor:
            ColorConstants.backgroundColor, // Light beige background
        elevation: 0,
        flexibleSpace: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Gap(16.w), // Spacing to the left
                profileAsync.when(
                  data: (profile) {
                    return InkWell(
                      onTap: () {
                        ref.read(masterProvider.notifier).state = 3;
                      },
                      child: ClipOval(
                        child: CachedNetworkImage(
                          imageUrl: profile?.profilePictureUrl ?? 'https://',
                          height: 40.h,
                          width: 40.w,
                          fit: BoxFit.cover,
                          errorWidget: (context, url, error) {
                            return CircleAvatar(
                              backgroundImage: AssetImage(
                                'assets/icons/profile_picture.png',
                              ), // Replace with your image path
                              radius: 20.w,
                            );
                          },
                        ),
                      ),
                    );
                  },
                  loading: () => Skeletonizer(
                    enabled: true,
                    child: CircleAvatar(
                      backgroundImage: AssetImage(
                        'assets/icons/profile_picture.png',
                      ), // Replace with your image path
                      radius: 20.w,
                    ),
                  ),
                  error: (err, stack) => const SizedBox(),
                ),
                Gap(12.w),
                Expanded(
                  child: profileAsync.when(
                    data: (profile) {
                      return HomeAppBarTitle(
                        name: profile?.name,
                        designation: profile?.designation,
                      );
                    },
                    loading: () => const Skeletonizer(
                      enabled: true,
                      child: HomeAppBarTitle(
                        name: 'Loading Name',
                        designation: 'Designation',
                      ),
                    ),
                    error: (err, stack) => const SizedBox(),
                  ),
                ),
                Gap(20.w),
                const NotificationButton(),
                Gap(16.w), // Spacing to the right
              ],
            ),
            Gap(22.h),
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(85.h);
}

class HomeAppBarTitle extends StatelessWidget {
  final String? name;
  final String? designation;
  const HomeAppBarTitle({this.name, this.designation, super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          name ?? '',
          style: TextStyles.ts16w600c44322D,
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
        Text(
          designation ?? '',
          style: TextStyles.ts12w400c606060,
        )
      ],
    );
  }
}
