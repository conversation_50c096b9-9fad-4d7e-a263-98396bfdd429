import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class SummaryCard extends StatelessWidget {
  final String title;
  final int count;
  final String icon;
  final void Function()? onTap;
  const SummaryCard({
    required this.icon,
    required this.title,
    required this.count,
    this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color: ColorConstants.colorF8F8F8,
            borderRadius: BorderRadius.circular(10.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.02),
                offset: const Offset(0, 0),
                blurRadius: 5.7,
                spreadRadius: 0,
              ),
            ],
          ),
          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: ColorConstants.color94684E.withValues(alpha: 0.09),
                  borderRadius: BorderRadius.circular(6.r),
                ),
                width: 40.w,
                height: 38.h,
                alignment: Alignment.center,
                child: SvgPicture.asset(
                  icon.asIconSvg(),
                  width: 24.w,
                  height: 24.h,
                ),
              ),
              Gap(10.h),
              Text(
                title,
                style: TextStyles.ts12w400c4C4C4C,
              ),
              const Spacer(),
              Align(
                alignment: isRTL ? Alignment.bottomLeft : Alignment.bottomRight,
                child: Text(
                  count.toString().padLeft(2, '0'),
                  style: TextStyles.ts16w700c94684E,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
