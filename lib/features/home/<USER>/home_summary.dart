class HomeSummary {
  String? status;
  String? message;
  int? parkingCount;
  int? valetParkingCount;
  int? violationCount;

  HomeSummary({
    this.status,
    this.message,
    this.parkingCount,
    this.valetParkingCount,
    this.violationCount,
  });

  HomeSummary.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    parkingCount = json['parking_count'];
    valetParkingCount = json['valet_parking_count'];
    violationCount = json['violation_count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['status'] = status;
    data['message'] = message;
    data['parking_count'] = parkingCount;
    data['valet_parking_count'] = valetParkingCount;
    data['violation_count'] = violationCount;
    return data;
  }
}
