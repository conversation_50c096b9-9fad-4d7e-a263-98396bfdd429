import 'package:albalad_operator_app/features/home/<USER>/home_provider.dart';
import 'package:albalad_operator_app/features/home/<USER>/home_app_bar.dart';
import 'package:albalad_operator_app/features/home/<USER>/home_search_field.dart';
import 'package:albalad_operator_app/features/home/<USER>/home_summary_slides.dart';
import 'package:albalad_operator_app/features/home/<USER>/quick_actions.dart';
import 'package:albalad_operator_app/features/home/<USER>/shift_card.dart';
import 'package:albalad_operator_app/features/notifications/providers/notification_provider.dart';
import 'package:albalad_operator_app/features/number_plate_scanner/view/number_plate_scanner.dart';
import 'package:albalad_operator_app/features/profile/provider/profile_provider.dart';
import 'package:albalad_operator_app/features/search/provider/search_provider.dart';
import 'package:albalad_operator_app/features/search/view/search_screen.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/helper/app_lifecycle_observer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class HomeScreen extends HookConsumerWidget {
  static const route = '/home';
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (ref.exists(profileProvider)) {
          ref.invalidate(profileProvider);
        }
      });

      final observer = AppLifecycleObserver(
        onStateChanged: (state) {
          if (state == AppLifecycleState.resumed) {
            ref.invalidate(profileProvider);
            ref.invalidate(homeSummaryProvider);
            ref.invalidate(notificationCountProvider);
          }
        },
      );

      // Add observer to WidgetsBinding
      WidgetsBinding.instance.addObserver(observer);

      return () {
        WidgetsBinding.instance.removeObserver(observer);
      };
    }, []);
    return Scaffold(
      appBar: const HomeAppBar(),
      body: RefreshIndicator(
        onRefresh: () {
          ref.invalidate(profileProvider);
          ref.invalidate(homeSummaryProvider);
          ref.invalidate(notificationCountProvider);
          return Future.value();
        },
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 25.h),
          children: [
            HomeSearchField(
              hintText: tr(context, 'searchVehicleNumber'),
              readOnly: true,
              onPress: () {
                final notifier = ref.read(searchNotifierProvider.notifier);
                notifier.reset();
                final arguments = SearchScreen();
                Navigator.pushNamed(context, SearchScreen.route,
                    arguments: arguments);
              },
              onTapSuffix: () {
                final arguments = NumberPlateScanner(previousRoute: route);
                Navigator.of(context).pushNamed(
                  NumberPlateScanner.route,
                  arguments: arguments,
                );
              },
            ),
            Gap(25.h),
            const ShiftCard(),
            Gap(25.h),
            const HomeSummarySlides(),
            Gap(25.h),
            const QuickActions(),
          ],
        ),
      ),
    );
  }
}
