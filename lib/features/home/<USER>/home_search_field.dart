import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class HomeSearchField extends StatelessWidget {
  final TextEditingController? controller;
  final String? error;
  final String? hintText;
  final void Function()? onTapPrefix;
  final void Function()? onTapSuffix;
  final void Function()? onPress;
  final bool suffixIconVisible;
  final void Function(String)? onChanged;
  final bool readOnly;
  final bool autofocus;

  const HomeSearchField({
    super.key,
    this.controller,
    this.error,
    this.hintText,
    this.onTapPrefix,
    this.onTapSuffix,
    this.onPress,
    this.suffixIconVisible = true,
    this.onChanged,
    this.readOnly = false,
    this.autofocus = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(boxShadow: [
        BoxShadow(
          offset: Offset(0, 0),
          color: Color.fromRGBO(0, 0, 0, 0.1),
          blurRadius: 20,
          spreadRadius: 4,
        )
      ]),
      child: TextFormField(
        autofocus: autofocus,
        controller: controller,
        style: TextStyles.ts14w600c44322D,
        onTap: onPress,
        onChanged: onChanged,
        readOnly: readOnly,
        onTapOutside: (_) => FocusManager.instance.primaryFocus?.unfocus(),
        decoration: InputDecoration(
          errorText: error,
          fillColor: Colors.white,
          filled: true,
          border: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.white,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(25.r),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.white,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(25.r),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.white,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(25.r),
          ),
          counterText: '',
          hintText: hintText,
          hintStyle: TextStyles.ts14w500cC6C6C6,
          prefixIcon: InkWell(
            onTap: onTapPrefix,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Gap(15.w),
                Image.asset(
                  "search".asIconPng(),
                  scale: 2,
                ),
                Gap(5.w),
              ],
            ),
          ),
          suffixIcon: !suffixIconVisible
              ? const SizedBox()
              : InkWell(
                  onTap: onTapSuffix,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        height: 20.h,
                        child: const VerticalDivider(
                          width: 0,
                          thickness: 1,
                          color: ColorConstants.colorE9E9E9,
                        ),
                      ),
                      Gap(10.w),
                      Image.asset(
                        "qr-code".asIconPng(),
                        scale: 2,
                      ),
                      Gap(5.w),
                    ],
                  ),
                ),
        ),
      ),
    );
  }
}
