import 'package:albalad_operator_app/features/add_vehicle/view/add_vehicle_screen.dart';
import 'package:albalad_operator_app/features/home/<USER>/quick_action_card.dart';
import 'package:albalad_operator_app/features/profile/provider/profile_provider.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/clamp_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/tow_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/valet/view/valet_operations_screen.dart';
import 'package:albalad_operator_app/features/violation/view/violations_and_notices_screen.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class QuickActions extends ConsumerWidget {
  final bool showTitle;
  const QuickActions({this.showTitle = true, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final profileAsync = ref.watch(profileProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) ...[
          Text(
            tr(context, 'quickActions'),
            style: TextStyles.ts18w600c181818,
          ),
          Gap(14.h),
        ],
        profileAsync.when(
          data: (data) {
            final permissions = data?.permission ?? [];
            return Wrap(
              spacing: 10.w,
              runSpacing: 10.h,
              children: [
                QuickActionCard(
                  icon: 'message-text',
                  title: tr(context, 'violationsAndNotices'),
                  onTap: () => Navigator.pushNamed(
                    context,
                    ViolationsAndNoticesScreen.route,
                  ),
                ),
                if (permissions.contains('clamp'))
                  QuickActionCard(
                    icon: 'lock',
                    title: tr(context, 'clampVehicle'),
                    onTap: () => Navigator.pushNamed(
                        context, ClampVehicleListingScreen.route,
                        arguments: ClampVehicleListingScreen()),
                  ),
                if (permissions.contains('tow'))
                  QuickActionCard(
                    icon: 'car',
                    title: tr(context, 'towVehicle'),
                    onTap: () => Navigator.pushNamed(
                      context,
                      TowVehicleListingScreen.route,
                      arguments: TowVehicleListingScreen(),
                    ),
                  ),
                if (permissions.contains('valet'))
                  QuickActionCard(
                    icon: 'valet',
                    title: tr(context, 'valetOperation'),
                    onTap: () => Navigator.pushNamed(
                      context,
                      ValetOperationsScreen.route,
                    ),
                  ),
                QuickActionCard(
                  icon: 'driving',
                  title: tr(context, 'addVehicle'),
                  onTap: () => Navigator.pushNamed(
                    context,
                    AddVehicleScreen.route,
                  ),
                ),
              ],
            );
          },
          error: (error, stackTrace) => const SizedBox(),
          loading: () => const SizedBox(),
        ),
      ],
    );
  }
}
