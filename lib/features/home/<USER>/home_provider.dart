import 'package:albalad_operator_app/features/home/<USER>/home_summary.dart';
import 'package:albalad_operator_app/features/home/<USER>/home_services.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

final homeSummaryProvider =
    FutureProvider.autoDispose<HomeSummary>((ref) async {
  final response = await HomeServices().fetchHomePage();
  return HomeSummary.fromJson(response.data);
});
