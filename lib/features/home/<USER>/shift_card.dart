import 'package:albalad_operator_app/features/home/<USER>/gradient_dashed_line.dart';
import 'package:albalad_operator_app/features/profile/provider/profile_provider.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:easy_date_formatter/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart' as intl;
import 'package:skeletonizer/skeletonizer.dart';

class ShiftCard extends ConsumerWidget {
  const ShiftCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final profileAsync = ref.watch(profileProvider);
    return profileAsync.when(
      data: (profile) {
        String date = DateFormatter.formatDateTime(
            dateTime: DateTime.now(), outputFormat: 'dd MMM, yyyy');
        final location = profile?.assignedLocation?.firstOrNull;
        String? shiftStartTime = profile?.assignedShiftTime?.startTime;
        String? shiftEndTime = profile?.assignedShiftTime?.endTime;
        return _ShiftCard(
          startTime: shiftStartTime ?? '-',
          endTime: shiftEndTime ?? '-',
          date: date,
          shiftName: profile?.assignedShiftTime?.name,
          locatioName: location?.name,
        );
      },
      loading: () => const Skeletonizer(
        enabled: true,
        child: _ShiftCard(
          startTime: '10:00 AM',
          endTime: '11:00 AM',
          date: 'Nov 18, 2024',
          shiftName: 'Shift Name',
          locatioName: 'Location Name',
          key: Key('skeleton'),
        ),
      ),
      error: (err, stack) => const SizedBox(),
    );
  }

  String formatShiftTime(String originalTime) {
    try {
      // Parse the original time into a DateTime object
      intl.DateFormat inputFormat = intl.DateFormat("h a");
      DateTime time = inputFormat.parse(
        originalTime,
      );

      // Convert the DateTime object to the desired format
      intl.DateFormat outputFormat = intl.DateFormat("h:mm a");
      String formattedTime = outputFormat.format(time);
      return formattedTime;
    } catch (e) {
      return originalTime;
    }
  }
}

class _ShiftCard extends StatelessWidget {
  final String? startTime;
  final String? endTime;
  final String? date;
  final String? shiftName;
  final String? locatioName;
  const _ShiftCard({
    this.startTime,
    this.endTime,
    this.date,
    this.shiftName,
    this.locatioName,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          width: 1.w,
          color: ColorConstants.colorEAEAEA,
        ),
      ),
      padding: EdgeInsets.fromLTRB(14.w, 14.h, 14.w, 12.h),
      child: Column(
        children: [
          Row(
            spacing: 40.w,
            children: [
              Text(
                startTime ?? '',
                style: TextStyles.ts14w700c1E1E1E,
                textDirection: TextDirection.ltr,
              ),
              const Expanded(child: GradientDashedLineWithCircles()),
              Text(
                endTime ?? '',
                style: TextStyles.ts14w700c1E1E1E,
                textDirection: TextDirection.ltr,
              ),
            ],
          ),
          Row(
            children: [
              Text(
                date ?? '',
                style: TextStyles.ts10w400c878686,
              ),
              Expanded(
                child: Center(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: ColorConstants.colorF1EFE9,
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: 9.w,
                      vertical: 4.h,
                    ),
                    child: Text(
                      shiftName ?? tr(context, 'notAssigned'),
                      style: TextStyles.ts10w400c1E1E1E,
                    ),
                  ),
                ),
              ),
              Text(
                date ?? '',
                style: TextStyles.ts10w400c878686,
              ),
            ],
          ),
          Gap(18.h),
          Divider(
            color: ColorConstants.colorF4F4F4,
            height: 0,
            thickness: 1.w,
          ),
          Gap(10.h),
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: ColorConstants.colorF1EFE9,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                width: 35.w,
                height: 35.h,
                alignment: Alignment.center,
                child: SvgPicture.asset(
                  'location'.asIconSvg(),
                  height: 18.h,
                  width: 18.w,
                ),
              ),
              Gap(10.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tr(context, 'location'),
                      style: TextStyles.ts10w400cA1A09B,
                    ),
                    Text(
                      locatioName ?? tr(context, 'notAssigned'),
                      style: TextStyles.ts14w600c181818,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
