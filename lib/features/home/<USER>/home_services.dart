import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:dio/dio.dart';

class HomeServices {
  final Dio dio = DioClient().dio;
  Future<Response> fetchHomePage() async {
    final response = await dio.get(
      ApiConstants.homePage,
      options: Options(headers: await ApiConstants.authHeaders()),
    );
    return response;
  }
}
