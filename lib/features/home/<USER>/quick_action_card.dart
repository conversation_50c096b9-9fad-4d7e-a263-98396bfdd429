import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class QuickActionCard extends StatelessWidget {
  final String icon;
  final String title;
  final void Function()? onTap;
  const QuickActionCard(
      {required this.icon, required this.title, this.onTap, super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 0.5.sw - 21.w,
        decoration: BoxDecoration(
          color: ColorConstants.colorF8F8F8,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 0),
              blurRadius: 5.7,
              color: Colors.black.withValues(alpha: 0.02),
              spreadRadius: 0,
            ),
          ],
        ),
        padding: EdgeInsets.fromLTRB(20.w, 20.h, 16.w, 14.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                color: ColorConstants.color94684E.withValues(alpha: 0.09),
                borderRadius: BorderRadius.circular(6.r),
              ),
              width: 40.w,
              height: 38.h,
              alignment: Alignment.center,
              child: SvgPicture.asset(
                icon.asIconSvg(),
                width: 24.w,
                height: 24.h,
              ),
            ),
            Gap(15.h),
            Text(
              title,
              style: TextStyles.ts14w600c353535,
            ),
          ],
        ),
      ),
    );
  }
}
