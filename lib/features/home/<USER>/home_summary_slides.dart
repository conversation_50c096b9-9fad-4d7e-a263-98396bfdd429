import 'package:albalad_operator_app/features/current_parked_vehicles/provider/current_parked_providers.dart';
import 'package:albalad_operator_app/features/current_parked_vehicles/view/current_parked_listing_screen.dart';
import 'package:albalad_operator_app/features/home/<USER>/home_summary.dart';
import 'package:albalad_operator_app/features/home/<USER>/home_provider.dart';
import 'package:albalad_operator_app/features/home/<USER>/summary_card.dart';
import 'package:albalad_operator_app/features/valet/providers/current_valet_vehicles_provider.dart';
import 'package:albalad_operator_app/features/valet/view/current_valet_vehicles_screen.dart';
import 'package:albalad_operator_app/features/violation/provider/current_violation_listing_provider.dart';
import 'package:albalad_operator_app/features/violation/view/current_violations_screen.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:skeletonizer/skeletonizer.dart';

class HomeSummarySlides extends ConsumerWidget {
  const HomeSummarySlides({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<HomeSummary> summary = ref.watch(homeSummaryProvider);
    return switch (summary) {
      AsyncData(:final value) => SummarySlides(
          parkingCount: value.parkingCount,
          violationCount: value.violationCount,
          valetCount: value.valetParkingCount,
        ),
      AsyncError() => const SizedBox(),
      _ => const Skeletonizer(enabled: true, child: SummarySlides()),
    };
  }
}

class SummarySlides extends ConsumerWidget {
  final int? parkingCount;
  final int? violationCount;
  final int? valetCount;
  const SummarySlides(
      {this.parkingCount, this.violationCount, this.valetCount, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return IntrinsicHeight(
      child: Row(
        spacing: 9.w,
        children: [
          SummaryCard(
            onTap: () {
              final notifier = ref.read(parkedVehicleNotifierProvider.notifier);
              notifier.reset();
              Navigator.pushNamed(context, CurrentParkedListingScreen.route);
            },
            icon: 'car',
            title: tr(context, 'currentParkedVehicles'),
            count: parkingCount ?? 0,
          ),
          SummaryCard(
            onTap: () {
              final notifier =
                  ref.read(currentVioaltionListingNotifierProvider.notifier);
              notifier.reset();
              Navigator.pushNamed(context, CurrentViolationsScreen.route);
            },
            icon: 'danger',
            title: tr(context, 'currentViolations'),
            count: violationCount ?? 0,
          ),
          SummaryCard(
            onTap: () {
              final notifier =
                  ref.read(currentValetVehiclesNotifierProvider.notifier);
              notifier.reset();
              Navigator.pushNamed(context, CurrentValetVehiclesScreen.route);
            },
            icon: 'valet',
            title: tr(context, 'currentValetVehicles'),
            count: valetCount ?? 0,
          ),
        ],
      ),
    );
  }
}
