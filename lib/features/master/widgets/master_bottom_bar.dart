import 'package:albalad_operator_app/features/profile/provider/profile_provider.dart';
import 'package:albalad_operator_app/providers/global_providers.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class MasterBottomBar extends ConsumerWidget {
  const MasterBottomBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    List<BottomBarItem> items = getBottomBarItems();
    return Container(
      height: 66.h,
      margin: EdgeInsets.fromLTRB(16.w, 0, 16.w, 27.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(35.h),
        color: ColorConstants.primaryColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, -10),
            blurRadius: 78,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: items.map(
          (e) {
            int currentIndex = items.indexOf(e);
            return Expanded(
              child: InkWell(
                onTap: () {
                  if (currentIndex == 3) {
                    ref.invalidate(profileProvider);
                  }
                  ref.read(masterProvider.notifier).state = currentIndex;
                },
                child: Center(
                  child: Consumer(
                    builder: (context, ref, _) {
                      int index = ref.watch(masterProvider);
                      if (index == currentIndex) {
                        return SvgPicture.asset(
                          e.activeIcon.asIconSvg(),
                          height: 24.h,
                          width: 24.w,
                        );
                      }

                      return SvgPicture.asset(
                        e.inactiveIcon.asIconSvg(),
                        height: 24.h,
                        width: 24.w,
                      );
                    },
                  ),
                ),
              ),
            );
          },
        ).toList(),
      ),
    );
  }

  List<BottomBarItem> getBottomBarItems() {
    return [
      BottomBarItem(
          activeIcon: 'home-active', inactiveIcon: 'home', label: 'Home'),
      BottomBarItem(
          activeIcon: 'services-active',
          inactiveIcon: 'services',
          label: 'Services'),
      BottomBarItem(
          activeIcon: 'notification-bing-active',
          inactiveIcon: 'notification-bing',
          label: 'Notifications'),
      BottomBarItem(
          activeIcon: 'user-active', inactiveIcon: 'user', label: 'Profile'),
    ];
  }
}

class BottomBarItem {
  String activeIcon;
  String inactiveIcon;
  String label;
  BottomBarItem({
    required this.activeIcon,
    required this.inactiveIcon,
    required this.label,
  });
}
