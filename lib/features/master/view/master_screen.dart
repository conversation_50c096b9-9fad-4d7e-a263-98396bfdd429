import 'dart:io';

import 'package:albalad_operator_app/features/clamp_violation/view/clamp_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/clamped_details_screen.dart';
import 'package:albalad_operator_app/features/home/<USER>/home_screen.dart';
import 'package:albalad_operator_app/features/master/widgets/master_bottom_bar.dart';
import 'package:albalad_operator_app/features/notifications/providers/notification_provider.dart';
import 'package:albalad_operator_app/features/notifications/view/notification_screen.dart';
import 'package:albalad_operator_app/features/profile/view/my_profile_screen.dart';
import 'package:albalad_operator_app/features/services/view/services_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/tow_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/towed_details_screen.dart';
import 'package:albalad_operator_app/features/valet/view/generate_valet_ticket_screen.dart';
import 'package:albalad_operator_app/features/valet/view/valet_operations_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/providers/global_providers.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class MasterScreen extends ConsumerStatefulWidget {
  static const route = '/master';
  const MasterScreen({super.key});

  @override
  ConsumerState<MasterScreen> createState() => _MasterScreenState();
}

class _MasterScreenState extends ConsumerState<MasterScreen> {
  DateTime? _lastPressedAt;

  @override
  void initState() {
    super.initState();
    _firebaseMessagingInit();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) return;
        final now = DateTime.now();
        if (_lastPressedAt == null ||
            now.difference(_lastPressedAt!) > const Duration(seconds: 2)) {
          _lastPressedAt = now;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(tr(context, 'press_back_again_to_close')),
              duration: Duration(seconds: 2),
            ),
          );
          return;
        }
        exit(0);
      },
      child: Scaffold(
        body: Consumer(
          builder: (context, ref, _) {
            int index = ref.watch(masterProvider);
            return IndexedStack(
              index: index,
              children: const [
                HomeScreen(),
                ServicesScreen(),
                NotificationScreen(canPop: false),
                MyProfileScreen(),
              ],
            );
          },
        ),
        bottomNavigationBar: const MasterBottomBar(),
      ),
    );
  }

  //--------------------------------------------Push Notifications------------------------------------------------//

  _firebaseMessagingInit() {
    //if the app in terminated state
    getFirebaseMessages();

    //if the app is open state
    FirebaseMessaging.onMessage.listen(_handleMessage);
    //if the app in background state

    FirebaseMessaging.onMessageOpenedApp.listen(
      (RemoteMessage message) => _handleMessageData(message.data),
    );
  }

  getFirebaseMessages() async {
    RemoteMessage? initialMsg =
        await FirebaseMessaging.instance.getInitialMessage();
    if (initialMsg != null) {
      _firebaseMessagingBackgroundHandler(initialMsg);
    }
  }

  Future<void> _firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {
    await Firebase.initializeApp();
    _handleMessageData(message.data);
  }

  void _handleMessage(RemoteMessage message) async {
    ref.invalidate(notificationCountProvider);
    sendLocalNotification(message);
  }

  sendLocalNotification(RemoteMessage message) async {
    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();
    AndroidInitializationSettings initializationSettingsAndroid =
        const AndroidInitializationSettings('@mipmap/ic_launcher');
    // final DarwinInitializationSettings initializationSettingsDarwin =
    // DarwinInitializationSettings(
    //     onDidReceiveLocalNotification: (id, title, body, payload) =>
    //         _handleMessageData(message.data));
    final InitializationSettings initializationSettings =
        InitializationSettings(
            android: initializationSettingsAndroid,
            iOS: const DarwinInitializationSettings());
    // _recentChatProvider.getRecentCommunications();
    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (details) =>
          _handleMessageData(message.data),
    );

    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'high_importance_channel', // id
      'High Importance Notifications', // title description
      importance: Importance.max,
    );
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
    RemoteNotification notification = message.notification!;

    AndroidNotificationDetails? androidNotificationDetails;
    if (Platform.isAndroid) {
      AndroidNotification android = message.notification!.android!;
      androidNotificationDetails = AndroidNotificationDetails(
          channel.id, channel.name,
          icon: android.smallIcon);
    }

    // If `onMessage` is triggered with a notification, construct our own
    // local notification to show to users using the created channel.
    flutterLocalNotificationsPlugin.show(
      notification.hashCode,
      notification.title,
      notification.body,
      NotificationDetails(android: androidNotificationDetails),
    );
  }

  // Future<void> _handleMessageWhileNotificationComing(
  Future<void> _handleMessageData(Map<String, dynamic> data) async {
    String action = data['action'] ?? '';
    String actionUid = data['action_id'] ?? '';
    switch (action) {
      case 'valet_settled_operator':
      case 'assign_valet':
      case 'vehicle_request_operator':
        final arguments = GenerateValetTicketScreen(
            previousRoute: MasterScreen.route, valetUid: actionUid);
        Navigator.pushNamed(
          context,
          GenerateValetTicketScreen.route,
          arguments: arguments,
        );
        break;
      case 'valet_booking_operator':
        Navigator.pushNamed(context, ValetOperationsScreen.route);
        break;
      case 'clamping_assigned_operator':
        final arguments = ClampVehicleListingScreen(tabIndex: 0);
        Navigator.pushNamed(context, ClampVehicleListingScreen.route,
            arguments: arguments);
        break;
      case 'clamped_vehicle_operator':
      case 'towed_vehicle_operator':
      case 'parking_extension_operator_alert':
        final arguments = VehicleDetailsScreen(
          uid: actionUid,
        );
        Navigator.pushNamed(
          context,
          VehicleDetailsScreen.route,
          arguments: arguments,
        );
        break;
      case 'clamp_violation_settled_operator':
        final arguments = ClampedDetailsScreen(
            uid: actionUid, previousRoute: MasterScreen.route);
        Navigator.pushNamed(
          context,
          ClampedDetailsScreen.route,
          arguments: arguments,
        );
        break;
      case 'towing_assigned_operator':
        final arguments = TowVehicleListingScreen(tabIndex: 0);
        Navigator.pushNamed(context, TowVehicleListingScreen.route,
            arguments: arguments);
        break;
      case 'tow_violation_settled_operator':
        final arguments = TowedDetailsScreen(
            uid: actionUid, previousRoute: MasterScreen.route);
        Navigator.pushNamed(context, TowedDetailsScreen.route,
            arguments: arguments);
        break;
      case 'user_profile_update':
        Navigator.pushNamed(context, MyProfileScreen.route);
        break;
    }
  }
}
