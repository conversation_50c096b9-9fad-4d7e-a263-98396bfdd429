import 'package:albalad_operator_app/features/home/<USER>/quick_actions.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ServicesScreen extends StatelessWidget {
  const ServicesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(tr(context, 'services')),
      ),
      body: Padding(
        padding: EdgeInsets.fromLTRB(16.w, 30.h, 16.w, 0),
        child: const QuickActions(
          showTitle: false,
        ),
      ),
    );
  }
}
