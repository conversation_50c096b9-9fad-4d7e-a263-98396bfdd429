import 'dart:io';

import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/services/fcm_services.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:encrypt/encrypt.dart' as aes;
import 'package:firebase_messaging/firebase_messaging.dart';

class AuthServices {
  final Dio dio = DioClient().dio;

  Future<String?> fetchEncryptionKey() async {
    try {
      final response = await dio.get(
        ApiConstants.encryptionSecretKey,
      );
      if (response.statusCode == 200) {
        final json = response.data;
        if (json['result'] == 'success') {
          return json['secret_key'];
        }
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionError ||
          e.type == DioExceptionType.connectionTimeout) {
        throw appLocalization.translate('networkError');
      }
    }
    return null;
  }

  Future<Response> signIn({
    required String email,
    required String password,
    required String language,
  }) async {
    // Fetch the encryption key asynchronously, which will be used to encrypt the data.
    final secretKey = await fetchEncryptionKey();
    final normalizedKey = normalizeKey(secretKey.toString());

    // Convert the fetched key to a format suitable for AES encryption.
    // final key = aes.Key.fromUtf8(secretKey.toString());
    final key = aes.Key.fromUtf8(normalizedKey);

    // Generate an Initialization Vector (IV) with a fixed length of 16 bytes.
    // Note: AES in ECB mode does not use an IV; this is just a placeholder.
    final iv = aes.IV.fromSecureRandom(16);

    // Create an instance of the AES encrypter with the specified key and encryption mode (ECB in this case).
    final encrypter = aes.Encrypter(aes.AES(key, mode: aes.AESMode.ecb));

    // Encrypt the password using the encrypter and the provided IV.
    // Note: ECB mode does not require an IV, so it is effectively ignored here.
    final encryptedPassword = encrypter.encrypt(password, iv: iv);

    final deviceInfo = await getDeviceInfo(language);
    Map<String, dynamic> data = {
      "email": email,
      "password": encryptedPassword.base64,
      "secret_key": normalizedKey,
      "encryption": "True",
      "language": language
    };

    data.addAll(deviceInfo);

    return await dio.post(
      ApiConstants.signin,
      options: Options(headers: await ApiConstants.headers()),
      data: data,
    );
  }

  String normalizeKey(String key) {
    const int keyLength = 16; // 16 for AES-128, 24 for AES-192, 32 for AES-256
    if (key.length > keyLength) {
      return key.substring(0, keyLength);
    } else if (key.length < keyLength) {
      return key.padRight(keyLength, '0');
    }
    return key;
  }

  Future<Map<String, dynamic>> getDeviceInfo(String language) async {
    Map<String, dynamic> infoMap = {};

    // Create an instance of DeviceInfoPlugin to retrieve device-specific information.
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    // Create an instance of FirebaseMessaging to fetch the device's Firebase push notification token.
    FirebaseMessaging messaging = FirebaseMessaging.instance;

    // Check if the platform is Android.
    if (Platform.isAndroid) {
      // Retrieve Android-specific device information.
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;

      // Add the device ID (unique identifier) to the request body.
      infoMap['primary'] = androidInfo.id;

      // Specify the platform type as Android.
      infoMap['platform'] = 'android';

      // Add the device model information (e.g., "Pixel 5").
      infoMap['model'] = androidInfo.model;

      // Add the manufacturer information (e.g., "Google").
      infoMap['manufacturer'] = androidInfo.manufacturer;
    } else {
      // Retrieve iOS-specific device information.
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;

      // Add the unique identifier for the iOS device to the request body.
      infoMap['primary'] = iosInfo.identifierForVendor;

      // Specify the platform type as iOS.
      infoMap['platform'] = 'ios';

      // Add the device model information (e.g., "iPhone14,3").
      infoMap['model'] = iosInfo.utsname.machine;

      // Specify the manufacturer as "Apple" for iOS devices.
      infoMap['manufacturer'] = 'apple';
    }

    // Retrieve the Firebase Cloud Messaging (FCM) token for the device and add it to the request body.
    infoMap['token'] = await messaging.getToken();

    if (language == 'en') {
      FcmServices.subscribeEnglishTopic();
    } else {
      FcmServices.subscribeArabicTopic();
    }

    return infoMap;
  }

  Future<Response?> forgotPassword({required String email}) async {
    try {
      final response = await dio.post(
        ApiConstants.forgotPassword,
        data: {"email": email},
      );
      return response;
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionError ||
          e.type == DioExceptionType.connectionTimeout) {
        throw appLocalization.translate('networkError');
      }
    }
    return null;
  }

  Future<Response?> verifyOTP({
    required String verificationURL,
    required String otp,
    required String language,
    bool isTwoFactorAuth = false,
  }) async {
    // Initialize an empty Map to hold the request body data.
    Map<String, dynamic> requestBody = {'otp': otp};
    if (isTwoFactorAuth) {
      final deviceInfo = await getDeviceInfo(language);
      requestBody['language'] = language;
      requestBody.addAll(deviceInfo);
    }
    try {
      final response = await dio.post(
        verificationURL,
        data: requestBody,
      );

      return response;
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionError ||
          e.type == DioExceptionType.connectionTimeout) {
        throw appLocalization.translate('networkError');
      }
    }
    return null;
  }

  Future<Response> resendOTP({required String resendOtpURL}) async {
    return await dio.post(
      resendOtpURL,
    );
  }

  Future<Response> resetPassword({
    required String apiURL,
    required String newPassword,
    required String confirmPassword,
  }) async {
    // Fetch the encryption key asynchronously, which will be used to encrypt the data.
    final secretKey = await fetchEncryptionKey();

    // Convert the fetched key to a format suitable for AES encryption.
    final key = aes.Key.fromUtf8(secretKey.toString());

    // Generate an Initialization Vector (IV) with a fixed length of 16 bytes.
    // Note: AES in ECB mode does not use an IV; this is just a placeholder.
    final iv = aes.IV.fromLength(16);

    // Create an instance of the AES encrypter with the specified key and encryption mode (ECB in this case).
    final encrypter = aes.Encrypter(aes.AES(key, mode: aes.AESMode.ecb));

    // Encrypt the password using the encrypter and the provided IV.
    // Note: ECB mode does not require an IV, so it is effectively ignored here.
    final encryptedNewPassword = encrypter.encrypt(newPassword, iv: iv);
    final encryptedConfirmPassword = encrypter.encrypt(confirmPassword, iv: iv);

    return await dio.post(
      apiURL,
      data: {
        'new_password': encryptedNewPassword.base64,
        'confirm_password': encryptedConfirmPassword.base64,
        "secret_key": secretKey,
        "encryption": "True",
      },
    );
  }

  Future<void> signout() async {
    await dio.post(
      ApiConstants.signout,
      data: await getDeviceInfo('en'),
      options: Options(headers: await ApiConstants.authHeaders()),
    );
  }

  Future<void> updateLanguage(String language) async {
    await dio.post(
      ApiConstants.profileUpdate,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
      data: {
        'language': language == 'en' ? 1 : 2,
      },
    );
  }
}
