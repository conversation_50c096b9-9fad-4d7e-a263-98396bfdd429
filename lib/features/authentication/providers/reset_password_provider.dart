import 'dart:io';

import 'package:albalad_operator_app/features/authentication/providers/reset_password_state.dart';
import 'package:albalad_operator_app/features/authentication/services/auth_services.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'reset_password_provider.g.dart';

@riverpod
class ResetPasswordNotifier extends _$ResetPasswordNotifier {
  @override
  ResetPasswordState build() => ResetPasswordInitial();

  Future<void> resetPassword({
    required String apiURL,
    required String newPassword,
    required String confirmPassword,
  }) async {
    state = ResetPasswordLoading();
    try {
      final response = await AuthServices().resetPassword(
        apiURL: apiURL,
        newPassword: newPassword,
        confirmPassword: confirmPassword,
      );
      if (response.statusCode == 200) {
        final json = response.data;
        if (json['result'] == 'success') {
          state = ResetPasswordSuccess(json['message']);
        } else {
          state = ResetPasswordError(message: json['message']);
          return;
        }
      }
      if (response.statusCode == 400) {
        final json = response.data;
        Map<String, dynamic> errors = json['errors'] ?? {};
        if (errors.isNotEmpty) {
          state = ResetPasswordError(message: errors.values.first.toString());
          return;
        }
      }
      state = ResetPasswordError(message: response.statusMessage);
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionError ||
          e.type == DioExceptionType.connectionTimeout) {
        state = ResetPasswordError(
            message: appLocalization.translate('networkError'));
      }
    } on SocketException {
      state = ResetPasswordError(
          message: appLocalization.translate('networkError'));
    } catch (e) {
      state = ResetPasswordError(message: e.toString());
    }
  }
}
