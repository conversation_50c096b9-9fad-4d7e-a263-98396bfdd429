abstract class OtpVerificationState {}

class OtpVerificationInitial extends OtpVerificationState {}

class OtpVerificationLoading extends OtpVerificationState {}

class OtpVerificationSuccess extends OtpVerificationState {
  final String? passwordURL;
  OtpVerificationSuccess(this.passwordURL);
}

class OtpVerificationError extends OtpVerificationState {
  final String? message;

  OtpVerificationError({this.message});
}

class OtpVerificationResendCode extends OtpVerificationState {
  final String message; // Example: "OTP code resent successfully"
  OtpVerificationResendCode(this.message);
}
