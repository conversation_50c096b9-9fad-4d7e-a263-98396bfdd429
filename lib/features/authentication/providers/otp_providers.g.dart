// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'otp_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$otpTimerNotifierHash() => r'9f5e4ccf531247a2845fc1bfc3e1165ebb3c57bb';

/// See also [OtpTimerNotifier].
@ProviderFor(OtpTimerNotifier)
final otpTimerNotifierProvider =
    AutoDisposeNotifierProvider<OtpTimerNotifier, int>.internal(
  OtpTimerNotifier.new,
  name: r'otpTimerNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$otpTimerNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OtpTimerNotifier = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
