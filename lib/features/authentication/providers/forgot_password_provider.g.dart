// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'forgot_password_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$forgotPasswordNotifierHash() =>
    r'aee4faa39559f758fd06dfebf605a622b65020cd';

/// See also [ForgotPasswordNotifier].
@ProviderFor(ForgotPasswordNotifier)
final forgotPasswordNotifierProvider = AutoDisposeNotifierProvider<
    ForgotPasswordNotifier, ForgotPasswordState>.internal(
  ForgotPasswordNotifier.new,
  name: r'forgotPasswordNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$forgotPasswordNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ForgotPasswordNotifier = AutoDisposeNotifier<ForgotPasswordState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
