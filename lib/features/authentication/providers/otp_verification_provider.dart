import 'dart:convert';
import 'dart:io';

import 'package:albalad_operator_app/features/authentication/providers/forgot_password_provider.dart';
import 'package:albalad_operator_app/features/authentication/providers/otp_verification_state.dart';
import 'package:albalad_operator_app/features/authentication/services/auth_services.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/helper/secure_storage_helper.dart';
import 'package:albalad_operator_app/shared/models/logged_in_user.dart';
import 'package:albalad_operator_app/providers/global_providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'otp_verification_provider.g.dart';

@riverpod
class OtpVerificationNotifier extends _$OtpVerificationNotifier {
  @override
  OtpVerificationState build() {
    return OtpVerificationInitial();
  }

  Future<void> verifyOtp(
      {required String otp, bool isTwoFactorAuth = false}) async {
    state = OtpVerificationLoading();

    if (otp.length != 4) {
      state = OtpVerificationError(
          message: appLocalization.translate('pleaseEnterAValidOTP'));
      return;
    }

    try {
      final resp = ref.read(otpApiDetailsProvider);
      final verificationURL = resp?.otpVerificationurl ?? '';
      String language = ref.watch(localeProvider).languageCode;
      final response = await AuthServices().verifyOTP(
        verificationURL: verificationURL,
        otp: otp,
        language: language,
        isTwoFactorAuth: isTwoFactorAuth,
      );

      if (response?.statusCode == 200 &&
          response?.data['result'] == "success") {
        String? forgotPasswordURL = response?.data['forgot_password_url'];
        if (isTwoFactorAuth) {
          LoggedInUser.fromJson(response?.data);
          SecureStorageHelper.instance
              .saveData('user', jsonEncode(response?.data));
        }
        state = OtpVerificationSuccess(forgotPasswordURL);
      } else if (response?.statusCode == 400 &&
          response?.data['result'] == "failure") {
        if (response?.data['message'] != null) {
          state = OtpVerificationError(message: response?.data['message']);
        } else {
          Map<String, dynamic> errors = response?.data['errors'];
          if (errors.isNotEmpty) {
            state = OtpVerificationError(message: errors.values.first);
          }
        }
      } else {
        state = OtpVerificationError(
            message: appLocalization.translate('invalidOtpPleaseTryAgain'));
      }
    } on SocketException {
      state = OtpVerificationError(
          message: appLocalization.translate('networkError'));
    } catch (e) {
      state = OtpVerificationError(message: e.toString());
    }
  }

  Future<bool> resendOTP() async {
    try {
      final resp = ref.read(otpApiDetailsProvider);
      final resendOtpURL = resp?.resendOtp ?? '';
      final response =
          await AuthServices().resendOTP(resendOtpURL: resendOtpURL);
      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          state = OtpVerificationResendCode(response.data['message']);
          return true;
        } else {
          state = OtpVerificationError(message: response.data['message']);
        }
      } else {
        state = OtpVerificationError(
            message: appLocalization.translate('somethingWentWrong'));
      }
      return false;
    } on SocketException {
      state = OtpVerificationError(
          message: appLocalization.translate('networkError'));
      return false;
    } catch (e) {
      state = OtpVerificationError(
          message: appLocalization.translate('anErrorOccurredPleaseTryAgain'));
      return false;
    }
  }
}
