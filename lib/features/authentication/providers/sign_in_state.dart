abstract class SignInState {}

class SignInInitial extends SignInState {}

class SignInLoading extends SignInState {}

class SignInSuccess extends SignInState {
  final String message;

  SignInSuccess(this.message);
}

class SignInError extends SignInState {
  final String? message;
  final String? username;
  final String? password;

  SignInError({this.message, this.username, this.password});
}

class SignInForgotPassword extends SignInState {}

class SignInTwoFactorAuth extends SignInState {}
