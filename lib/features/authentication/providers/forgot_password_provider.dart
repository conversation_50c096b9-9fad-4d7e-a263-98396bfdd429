import 'dart:io';

import 'package:albalad_operator_app/features/authentication/models/forgot_password_response.dart';
import 'package:albalad_operator_app/features/authentication/providers/forgot_password_state.dart';
import 'package:albalad_operator_app/features/authentication/services/auth_services.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'forgot_password_provider.g.dart';

@riverpod
class ForgotPasswordNotifier extends _$ForgotPasswordNotifier {
  @override
  ForgotPasswordState build() {
    return ForgotPasswordInitial();
  }

  Future<void> forgotPassword({required String email}) async {
    state = ForgotPasswordLoading();
    try {
      final response = await AuthServices().forgotPassword(email: email);
      if (response?.statusCode == 200) {
        if (response?.data['result'] == 'success') {
          ref.read(otpApiDetailsProvider.notifier).state =
              OtpApiDetails.fromJson(response?.data);
          state = ForgotPasswordSuccess();
          return;
        }
      }
      if (response?.statusCode == 400) {
        if (response?.data['result'] == 'failure') {
          Map<String, dynamic> errors = response?.data['errors'];
          state = ForgotPasswordError(message: errors.values.firstOrNull ?? '');
          return;
        }
      }
      state = ForgotPasswordError(
          message: appLocalization.translate('somethingWentWrong'));
    } on SocketException {
      state = ForgotPasswordError(
          message: appLocalization.translate('networkError'));
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = ForgotPasswordError(
          message: appLocalization.translate('networkError'),
        );
        return;
      }
      state = ForgotPasswordError(message: e.toString());
    }
  }
}

final otpApiDetailsProvider = StateProvider<OtpApiDetails?>((ref) => null);
