import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'otp_providers.g.dart';

@riverpod
class OtpTimerNotifier extends _$OtpTimerNotifier {
  @override
  int build() {
    return 60;
  }

  late Timer _timer;

  void startTimer(int duration) {
    // Set initial time
    state = duration;

    // Create and start the timer
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state > 0) {
        state--;
      } else {
        _timer.cancel();
      }
    });
  }

  // Method to reset the timer if needed
  void resetTimer(int duration) {
    _timer.cancel();
    state = duration;
    startTimer(duration);
  }
}
