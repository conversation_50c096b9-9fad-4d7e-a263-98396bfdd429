// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reset_password_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$resetPasswordNotifierHash() =>
    r'ea2c3e3857994f4ff53d632e175ede64b2ac6fe0';

/// See also [ResetPasswordNotifier].
@ProviderFor(ResetPasswordNotifier)
final resetPasswordNotifierProvider = AutoDisposeNotifierProvider<
    ResetPasswordNotifier, ResetPasswordState>.internal(
  ResetPasswordNotifier.new,
  name: r'resetPasswordNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$resetPasswordNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ResetPasswordNotifier = AutoDisposeNotifier<ResetPasswordState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
