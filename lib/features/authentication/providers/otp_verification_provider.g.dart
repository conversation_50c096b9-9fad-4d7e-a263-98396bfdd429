// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'otp_verification_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$otpVerificationNotifierHash() =>
    r'cfb24edb20e6b68020ad452bf2617a4df1e63d77';

/// See also [OtpVerificationNotifier].
@ProviderFor(OtpVerificationNotifier)
final otpVerificationNotifierProvider = AutoDisposeNotifierProvider<
    OtpVerificationNotifier, OtpVerificationState>.internal(
  OtpVerificationNotifier.new,
  name: r'otpVerificationNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$otpVerificationNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OtpVerificationNotifier = AutoDisposeNotifier<OtpVerificationState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
