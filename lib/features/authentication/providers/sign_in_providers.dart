import 'dart:convert';
import 'dart:io';

import 'package:albalad_operator_app/features/authentication/models/forgot_password_response.dart';
import 'package:albalad_operator_app/features/authentication/providers/forgot_password_provider.dart';
import 'package:albalad_operator_app/features/authentication/providers/sign_in_state.dart';
import 'package:albalad_operator_app/features/authentication/services/auth_services.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/helper/secure_storage_helper.dart';
import 'package:albalad_operator_app/shared/models/logged_in_user.dart';
import 'package:albalad_operator_app/providers/global_providers.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'sign_in_providers.g.dart';

@riverpod
class SignInNotifier extends _$SignInNotifier {
  @override
  SignInState build() {
    return SignInInitial();
  }

  Future<void> signIn({required String email, required String password}) async {
    state = SignInLoading();
    try {
      String language = ref.watch(localeProvider).languageCode;
      final response = await AuthServices().signIn(
        email: email,
        password: password,
        language: language,
      );
      if (kDebugMode) {
        print(response.statusCode);
        print(response.data);
      }
      if (response.statusCode == 200) {
        Map<String, dynamic> data = response.data;
        if (data.containsKey('result')) {
          if (response.data['result'] == 'failure') {
            state = SignInError(message: response.data['message']);
            return;
          } else if (response.data['result'] == 'success') {
            LoggedInUser.fromJson(response.data);
            SecureStorageHelper.instance
                .saveData('user', jsonEncode(response.data));
            state = SignInSuccess("Success");
            return;
          }
        }
        if (data.containsKey('two_factor_auth_enabled') &&
            data['two_factor_auth_enabled'] == true) {
          ref.read(otpApiDetailsProvider.notifier).state =
              OtpApiDetails.fromJson(response.data);
          state = SignInTwoFactorAuth();
          return;
        }
      }
      if (response.statusCode == 400) {
        if (response.data['result'] == 'failure') {
          Map<String, dynamic> errors = response.data['errors'];
          if (errors.containsKey('username')) {
            state = SignInError(username: errors['username']);
            return;
          } else if (errors.containsKey('password')) {
            state = SignInError(password: errors['password']);
            return;
          } else {
            state = SignInError(message: errors.values.firstOrNull ?? '');
            return;
          }
        }
      }
      state =
          SignInError(message: appLocalization.translate('somethingWentWrong'));
    } on SocketException {
      state = SignInError(message: appLocalization.translate('networkError'));
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = SignInError(
          message: appLocalization.translate('networkError'),
        );
        return;
      }
      state = SignInError(message: e.toString());
    }
  }

  validateForForgotPassword({required String email}) {
    state = SignInInitial();
    if (email.isEmpty) {
      state = SignInError(
          username: appLocalization.translate('pleaseEnterYourEmail'));
    } else if (!email.isValidEmail()) {
      state = SignInError(
          username: appLocalization.translate('pleaseEnterAValidEmail'));
    } else {
      state = SignInForgotPassword();
    }
  }
}
