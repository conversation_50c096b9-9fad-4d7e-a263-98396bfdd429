class OtpApiDetails {
  String? result;
  int? otp;
  String? otpVerificationurl;
  String? resendOtp;
  int? otpExpire;

  OtpApiDetails({
    this.result,
    this.otp,
    this.otpVerificationurl,
    this.resendOtp,
    this.otpExpire,
  });

  OtpApiDetails.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    otp = json['otp'];
    otpVerificationurl =
        json['otp_verificationurl'] ?? json['otp_verification_url'];
    resendOtp = json['resend_otp'];
    otpExpire = json['otp_expire'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['result'] = result;
    data['otp'] = otp;
    data['otp_verificationurl'] = otpVerificationurl;
    data['resend_otp'] = resendOtp;
    data['otp_expire'] = otpExpire;
    return data;
  }
}
