import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class ContactMethodTile extends StatelessWidget {
  final String title;
  final String subtitle;
  final VoidCallback onTap;
  final bool isSelected;
  const ContactMethodTile({
    super.key,
    required this.title,
    required this.subtitle,
    required this.onTap,
    required this.isSelected,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: ColorConstants.colorD4D6DD, width: 0.5),
        ),
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            AnimatedContainer(
              height: 16.h,
              width: 16.h,
              duration: const Duration(milliseconds: 600),
              decoration: BoxDecoration(
                color: isSelected
                    ? ColorConstants.colorCCB5A7
                    : ColorConstants.colorFFFFFF,
                shape: BoxShape.circle,
                border: Border.all(
                  color: ColorConstants.colorF1EFE9,
                  width: isSelected ? 1 : 1.5,
                ),
              ),
            ),
            Gap(12.w),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: TextStyles.ts14w600c44322D),
                Gap(2.h),
                Text(subtitle, style: TextStyles.ts12w400c505050),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
