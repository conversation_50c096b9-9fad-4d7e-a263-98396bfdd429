import 'dart:io';

import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/widgets/elevated_secondary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class ContactAdminDialog extends StatelessWidget {
  final String phoneNumber;
  const ContactAdminDialog({required this.phoneNumber, super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: true,
      child: Padding(
        padding: EdgeInsets.fromLTRB(
            16.w, 50.h, 16.w, Platform.isAndroid ? 30.h : 0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              tr(context, 'contactAdmin'),
              style: TextStyles.ts30w700c44322D,
            ),
            Gap(5.h),
            Text(
              tr(context, 'contactAdminDescription'),
              style: TextStyles.ts14w500c959595,
              textAlign: TextAlign.center,
            ),
            Gap(50.h),
            Row(
              spacing: 10.w,
              children: [
                Expanded(
                  child: ElevatedSecondaryButton(
                    onPressed: () => Navigator.pop(context),
                    title: tr(context, 'cancel'),
                  ),
                ),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async => await makePhoneCall(phoneNumber),
                    child: Text(tr(context, 'contact')),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
