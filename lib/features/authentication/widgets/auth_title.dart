import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import '../../../shared/constants/text_styles.dart';

class AuthTitle extends StatelessWidget {
  final String title;
  final String subtitle;
  const AuthTitle({super.key, required this.subtitle, required this.title});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyles.ts30w700c44322D,
        ),
        Gap(5.h),
        Text(
          subtitle,
          style: TextStyles.ts14w500c959595,
        ),
      ],
    );
  }
}
