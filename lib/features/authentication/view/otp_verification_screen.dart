import 'package:albalad_operator_app/features/authentication/providers/otp_providers.dart';
import 'package:albalad_operator_app/features/authentication/providers/otp_verification_provider.dart';
import 'package:albalad_operator_app/features/authentication/providers/otp_verification_state.dart';
import 'package:albalad_operator_app/features/authentication/view/create_new_password_screen.dart';
import 'package:albalad_operator_app/features/master/view/master_screen.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_back_button.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:pinput/pinput.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../widgets/auth_title.dart';

class OtpVerificationScreen extends StatefulHookConsumerWidget {
  static const route = "verify_code_screen";
  final bool isTwoFactorAuth;
  const OtpVerificationScreen({this.isTwoFactorAuth = false, super.key});

  @override
  ConsumerState<OtpVerificationScreen> createState() =>
      _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends ConsumerState<OtpVerificationScreen> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        ref.read(otpTimerNotifierProvider.notifier).startTimer(60);
      },
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final otpController = useTextEditingController();
    final enableButton = useState(false);

    ref.listen(
      otpVerificationNotifierProvider,
      (previous, next) {
        if (next is OtpVerificationResendCode) {
          SnackBar snackBar = SnackBar(content: Text(next.message));
          ScaffoldMessenger.of(context).showSnackBar(snackBar);
          ref.read(otpTimerNotifierProvider.notifier).resetTimer(60);
          otpController.clear();
        }
        if (next is OtpVerificationSuccess) {
          if (widget.isTwoFactorAuth) {
            Navigator.pushNamedAndRemoveUntil(
                context, MasterScreen.route, (route) => false);
          } else {
            final args =
                CreateNewPasswordScreen(passwordURL: next.passwordURL ?? '');
            Navigator.pushReplacementNamed(
              context,
              CreateNewPasswordScreen.route,
              arguments: args,
            );
          }
        }
        // if (next is OtpVerificationError) {
        //   DialogHelper.showErrorDialog(
        //     context: context,
        //     message: next.message ?? '',
        //   );
        // }
      },
    );

    final state = ref.watch(otpVerificationNotifierProvider);

    return Scaffold(
      appBar: AppBar(leading: const CustomBackButton()),
      body: ListView(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        children: [
          Gap(60.h),
          AuthTitle(
            subtitle: tr(context, 'enter_the_four_digit_code_description'),
            title: tr(context, 'enter_the_otp'),
          ),
          Gap(57.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 75.w),
            child: Pinput(
              autofocus: true,
              controller: otpController,
              length: 4,
              onChanged: (value) => enableButton.value = value.length == 4,
              onTapOutside: (_) =>
                  FocusManager.instance.primaryFocus?.unfocus(),
              defaultPinTheme: PinTheme(
                textStyle: TextStyle(
                  fontSize: 34.sp,
                  fontWeight: FontWeight.w600,
                  color: ColorConstants.primaryColor,
                ),
              ),
              preFilledWidget: Container(
                height: 20.h,
                width: 20.w,
                decoration: BoxDecoration(
                  color: ColorConstants.primaryColor.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
              ),
              forceErrorState: true,
              errorText: state is OtpVerificationError ? state.message : null,
              errorBuilder: (errorText, pin) {
                return Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: Center(
                    child: Text(
                      errorText ?? '',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.red,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          Gap(12.h),
          Consumer(
            builder: (context, ref, child) {
              int secondsLeft = ref.watch(otpTimerNotifierProvider);
              return Center(
                child: Opacity(
                  opacity: secondsLeft > 0 ? 1 : 0,
                  child: Text(
                    '00:${secondsLeft.toString().padLeft(2, '0')}',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: ColorConstants.colorAF9F71,
                    ),
                  ),
                ),
              );
            },
          ),
          Gap(60.h),
          if (state is OtpVerificationLoading)
            const Center(
              child: CustomGradientSpinner(),
            )
          else
            ElevatedButton(
              onPressed: !enableButton.value
                  ? null
                  : () {
                      final provider =
                          ref.read(otpVerificationNotifierProvider.notifier);
                      provider.verifyOtp(
                        otp: otpController.text,
                        isTwoFactorAuth: widget.isTwoFactorAuth,
                      );
                    },
              child: Text(tr(context, 'verify')),
            ),
          Gap(20.h),
          Consumer(
            builder: (context, ref, child) {
              int secondsLeft = ref.watch(otpTimerNotifierProvider);
              return Opacity(
                opacity: secondsLeft > 0 ? 0 : 1,
                child: Text.rich(
                  textAlign: TextAlign.center,
                  TextSpan(
                    text: tr(context, 'did_not_receive_the_otp'),
                    style: TextStyles.ts14w500c505050,
                    children: [
                      TextSpan(
                        text: ' ',
                      ),
                      TextSpan(
                        text: tr(context, 'resend_otp'),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () async {
                            final provider = ref
                                .read(otpVerificationNotifierProvider.notifier);
                            EasyLoading.show();
                            await provider.resendOTP();
                            EasyLoading.dismiss();
                          },
                        style: TextStyles.ts14w600c44322D,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
