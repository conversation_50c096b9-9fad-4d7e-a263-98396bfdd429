import 'package:albalad_operator_app/features/authentication/providers/reset_password_provider.dart';
import 'package:albalad_operator_app/features/authentication/providers/reset_password_state.dart';
import 'package:albalad_operator_app/features/authentication/view/new_password_success_screen.dart';
import 'package:albalad_operator_app/features/authentication/view/sign_in_screen.dart';
import 'package:albalad_operator_app/features/authentication/widgets/auth_title.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_back_button.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/password_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CreateNewPasswordScreen extends ConsumerStatefulWidget {
  static const route = '/create_new_password_screen';
  final String passwordURL;
  const CreateNewPasswordScreen({required this.passwordURL, super.key});

  @override
  ConsumerState<CreateNewPasswordScreen> createState() =>
      _CreateNewPasswordScreenState();
}

class _CreateNewPasswordScreenState
    extends ConsumerState<CreateNewPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(
      resetPasswordNotifierProvider,
      (previous, next) {
        if (next is ResetPasswordSuccess) {
          Navigator.pushNamedAndRemoveUntil(
            context,
            NewPasswordSuccessScreen.route,
            ModalRoute.withName(SignInScreen.route),
          );
        }
      },
    );
    final state = ref.watch(resetPasswordNotifierProvider);
    return Scaffold(
      appBar: AppBar(leading: const CustomBackButton()),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              Gap(60.h),
              AuthTitle(
                title: tr(context, 'create_new_password'),
                subtitle: tr(context, 'passwordReset'),
              ),
              Gap(25.h),
              PasswordField(
                controller: _passwordController,
                labelText: tr(context, 'new_password_star'),
                errorText: state is ResetPasswordError ? state.message : null,
                validator: (password) {
                  if (password == null || password.isEmpty) {
                    return tr(context, 'pleaseEnterYourPassword');
                  }
                  return validatePassword(password, context);
                },
              ),
              Gap(26.h),
              PasswordField(
                controller: _confirmPasswordController,
                labelText: tr(context, 'confirm_password'),
                errorText: state is ResetPasswordError ? state.message : null,
                validator: (password) {
                  if (password == null || password.isEmpty) {
                    return tr(context, 'pleaseEnterYourPassword');
                  }
                  if (_passwordController.text != password) {
                    return tr(context, 'passwordsDontMatch');
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.only(bottom: 30.0.h, left: 16.w, right: 16.w),
        child: state is ResetPasswordLoading
            ? const SizedBox(
                height: kBottomNavigationBarHeight,
                child: Center(child: CustomGradientSpinner()),
              )
            : ElevatedButton(
                child: Text(tr(context, 'reset_password')),
                onPressed: () {
                  if (_formKey.currentState?.validate() == true) {
                    final provider =
                        ref.read(resetPasswordNotifierProvider.notifier);
                    provider.resetPassword(
                      apiURL: widget.passwordURL,
                      newPassword: _passwordController.text,
                      confirmPassword: _confirmPasswordController.text,
                    );
                  }
                },
              ),
      ),
    );
  }

  String? validatePassword(String password, BuildContext context) {
    if (password.length < 8) {
      return tr(context, 'passwordMustbeAtLeast8CharactersLong');
    }
    if (!RegExp(r'[A-Z]').hasMatch(password)) {
      return tr(context, 'passwordMustHaveAtLeastOneUppercase');
    }
    if (!RegExp(r'[a-z]').hasMatch(password)) {
      return tr(context, 'passwordMustHaveAtLeastOneLowercase');
    }
    if (!RegExp(r'\d').hasMatch(password)) {
      return tr(context, 'passwordMustHaveAtLeastOneNumber');
    }
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) {
      return tr(context, 'passwordMustHaveAtLeastOneSpecialCharacter');
    }
    return null;
  }
}
