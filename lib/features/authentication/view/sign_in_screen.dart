import 'package:albalad_operator_app/features/authentication/providers/sign_in_providers.dart';
import 'package:albalad_operator_app/features/authentication/providers/sign_in_state.dart';
import 'package:albalad_operator_app/features/authentication/view/forgot_password_screen.dart';
import 'package:albalad_operator_app/features/authentication/view/otp_verification_screen.dart';
import 'package:albalad_operator_app/features/authentication/widgets/auth_title.dart';
import 'package:albalad_operator_app/features/master/view/master_screen.dart';
import 'package:albalad_operator_app/features/notifications/providers/notification_provider.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/helper/dialog_helper.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/custom_text_form_field.dart';
import 'package:albalad_operator_app/shared/widgets/language_button.dart';
import 'package:albalad_operator_app/shared/widgets/password_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SignInScreen extends HookConsumerWidget {
  static const route = '/sign_in';
  const SignInScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();

    ref.listen(
      signInNotifierProvider,
      (previous, next) {
        if (next is SignInSuccess) {
          ref.invalidate(notificationCountProvider);
          Navigator.pushNamedAndRemoveUntil(
              context, MasterScreen.route, (route) => false);
        }
        if (next is SignInError && next.message != null) {
          DialogHelper.showErrorDialog(
              context: context, message: next.message ?? '');
        }
        if (next is SignInForgotPassword) {
          final args = ForgotPasswordScreen(email: emailController.text);
          Navigator.pushNamed(
            context,
            ForgotPasswordScreen.route,
            arguments: args,
          );
        }
        if (next is SignInTwoFactorAuth) {
          Navigator.pushNamed(
            context,
            OtpVerificationScreen.route,
            arguments: true,
          );
        }
      },
    );

    final state = ref.watch(signInNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        actions: [const LanguageButton(), Gap(16.w)],
      ),
      body: Form(
        key: formKey,
        child: ListView(
          padding: EdgeInsets.fromLTRB(16.w, 50.h, 16.w, 0),
          children: [
            AuthTitle(
              subtitle: tr(context, 'signInDescription'),
              title: tr(context, 'signIn'),
            ),
            Gap(25.h),
            CustomTextFormField(
              controller: emailController,
              labelText: tr(context, 'usernameOrEmail'),
              hintText: 'Ex. <EMAIL>',
              errorText: state is SignInError && state.username != null
                  ? state.username
                  : null,
              keyboardType: TextInputType.emailAddress,
              validator: (email) {
                if (email == null || email.isEmpty) {
                  return tr(context, 'pleaseEnterYourEmail');
                }
                if (!email.isValidEmail()) {
                  return tr(context, 'pleaseEnterAValidEmail');
                }
                return null;
              },
            ),
            Gap(20.h),
            PasswordField(
              labelText: tr(context, 'password'),
              controller: passwordController,
              errorText: state is SignInError ? state.password : null,
              validator: (password) {
                if (password == null || password.isEmpty) {
                  return tr(context, 'pleaseEnterYourPassword');
                }
                if (password.length < 8) {
                  return tr(context, 'pleaseEnterAValidPassword');
                }
                return null;
              },
            ),
            Gap(10.h),
            Align(
              alignment: Alignment.centerLeft,
              child: TextButton(
                onPressed: () {
                  final provider = ref.read(signInNotifierProvider.notifier);
                  provider.validateForForgotPassword(
                    email: emailController.text,
                  );
                  formKey.currentState?.reset();
                },
                style: TextButton.styleFrom(
                  foregroundColor: ColorConstants.primaryColor,
                  textStyle: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                child: Text(tr(context, 'forgotPasswordLink')),
              ),
            ),
            Gap(10.h),
            if (state is SignInLoading)
              const Center(child: CustomGradientSpinner())
            else
              ElevatedButton(
                onPressed: () {
                  if (formKey.currentState?.validate() == true) {
                    final provider = ref.read(signInNotifierProvider.notifier);
                    provider.signIn(
                      email: emailController.text,
                      password: passwordController.text,
                    );
                  }
                },
                child: Text(tr(context, 'signIn')),
              ),
          ],
        ),
      ),
    );
  }
}
