import 'package:albalad_operator_app/features/authentication/enum/contact_method_enum.dart';
import 'package:albalad_operator_app/features/authentication/providers/forgot_password_provider.dart';
import 'package:albalad_operator_app/features/authentication/providers/forgot_password_state.dart';
import 'package:albalad_operator_app/features/authentication/widgets/auth_title.dart';
import 'package:albalad_operator_app/features/authentication/widgets/contact_admin_dialog.dart';
import 'package:albalad_operator_app/features/authentication/widgets/contact_method_tile.dart';
import 'package:albalad_operator_app/features/support_center/models/contact_admin.dart';
import 'package:albalad_operator_app/features/support_center/providers.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/helper/dialog_helper.dart';
import 'package:albalad_operator_app/shared/widgets/custom_back_button.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'otp_verification_screen.dart';

class ForgotPasswordScreen extends HookConsumerWidget {
  static const route = '/forgot_password_screen.dart';
  final String email;
  const ForgotPasswordScreen({required this.email, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selected = useState(ContactMethodEnum.email);
    final adminNumber = useState<String?>(null);

    bool hasNavigated = false;

    ref.listen(
      forgotPasswordNotifierProvider,
      (previous, next) {
        if (next is ForgotPasswordSuccess && !hasNavigated) {
          hasNavigated = true;
          Navigator.pushNamed(
            context,
            OtpVerificationScreen.route,
            arguments: false,
          ).then(
            (value) {
              hasNavigated = false;
            },
          );
        }
        if (next is ForgotPasswordError) {
          DialogHelper.showErrorDialog(
            context: context,
            message: next.message!,
          );
        }
      },
    );

    final state = ref.watch(forgotPasswordNotifierProvider);

    final AsyncValue<ContactAdmin?> contact = ref.watch(contactAdminProvider);

    return Scaffold(
      appBar: AppBar(
        leading: const CustomBackButton(),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Gap(60.h),
            AuthTitle(
              subtitle: tr(context, 'forgot_password_description'),
              title: tr(context, 'forgotPassword'),
            ),
            Gap(25.h),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ContactMethodTile(
                  title: tr(context, 'via_email'),
                  subtitle: obfuscateEmail(email),
                  isSelected: selected.value == ContactMethodEnum.email,
                  onTap: () => selected.value = ContactMethodEnum.email,
                ),
                Gap(10.h),
                switch (contact) {
                  AsyncData(:final value) => ContactMethodTile(
                      title: tr(context, 'contact_admin'),
                      subtitle: maskMobileNumber('${value?.text}'),
                      isSelected: selected.value == ContactMethodEnum.admin,
                      onTap: () {
                        selected.value = ContactMethodEnum.admin;
                        adminNumber.value = value?.text;
                      },
                    ),
                  AsyncError() => const SizedBox(),
                  _ => const Center(
                      child: CustomGradientSpinner(),
                    )
                },
              ],
            ),
            const Spacer(),
            if (state is ForgotPasswordLoading)
              const Center(
                child: CustomGradientSpinner(),
              )
            else
              ElevatedButton(
                onPressed: () => onSubmitted(
                  context: context,
                  selected: selected.value,
                  ref: ref,
                  adminNumber: adminNumber.value,
                ),
                child: Text(
                  selected.value == ContactMethodEnum.email
                      ? tr(context, 'get_otp')
                      : tr(context, 'contact'),
                ),
              ),
            Gap(44.h),
          ],
        ),
      ),
    );
  }

  onSubmitted(
      {required BuildContext context,
      required ContactMethodEnum selected,
      required WidgetRef ref,
      String? adminNumber}) async {
    if (selected == ContactMethodEnum.admin) {
      showModalBottomSheet(
        context: context,
        builder: (context) => ContactAdminDialog(
          phoneNumber: adminNumber ?? '',
        ),
      );
    } else {
      final provider = ref.read(forgotPasswordNotifierProvider.notifier);
      provider.forgotPassword(email: email);
    }
  }
}
