import 'package:albalad_operator_app/features/authentication/view/sign_in_screen.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class NewPasswordSuccessScreen extends StatelessWidget {
  static const route = '/new_password_success_screen';
  const NewPasswordSuccessScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset('success'.asIconSvg()),
            Text(
              tr(context, 'create_new_password'),
              style: TextStyles.ts30w700c44322D,
            ),
            Gap(6.h),
            Text(
              tr(context, 'create_new_password_description'),
              textAlign: TextAlign.center,
              style: TextStyles.ts14w500c959595,
            )
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.only(bottom: 30.0.h, left: 16.w, right: 16.w),
        child: ElevatedButton(
          onPressed: () => Navigator.popUntil(
            context,
            ModalRoute.withName(SignInScreen.route),
          ),
          child: Text(tr(context, 'back_to_sign_in')),
        ),
      ),
    );
  }
}
