import 'package:albalad_operator_app/shared/models/vehicle_image.dart';

class ClampedDetails {
  String? uid;
  String? clampId;
  String? status;
  String? violationUid;
  String? vehicleNumberPlate;
  String? vehicleName;
  String? vehicleType;
  List<VehicleImage>? vehicleImage;
  int? makeYear;
  String? numberPlateType;
  String? clampedDateTime;
  String? violationDateTime;
  bool? isSettled;
  String? clampedBy;
  String? description;
  bool? coorparateVehicle;
  List<ClampingImage>? beforeImages;
  List<ClampingImage>? afterImages;

  ClampedDetails({
    this.uid,
    this.clampId,
    this.status,
    this.violationUid,
    this.vehicleNumberPlate,
    this.vehicleName,
    this.vehicleType,
    this.vehicleImage,
    this.makeYear,
    this.numberPlateType,
    this.clampedDateTime,
    this.violationDateTime,
    this.isSettled,
    this.clampedBy,
    this.description,
    this.coorparateVehicle,
    this.beforeImages,
    this.afterImages,
  });

  ClampedDetails.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    clampId = json['clamp_id'];
    status = json['status'];
    violationUid = json['violation_uid'];
    vehicleNumberPlate = json['vehicle_number_plate'];
    vehicleName = json['vehicle_name'];
    vehicleType = json['vehicle_type'];
    if (json['vehicle_image'] != null) {
      vehicleImage = <VehicleImage>[];
      json['vehicle_image'].forEach((v) {
        vehicleImage!.add(VehicleImage.fromJson(v));
      });
    }
    makeYear = int.tryParse(json['make_year'].toString());
    numberPlateType = json['number_plate_type'];
    clampedDateTime = json['clamped_date_time'];
    violationDateTime = json['violation_date_time'];
    isSettled = json['is_settled'];
    clampedBy = json['clamped_by'];
    description = json['description'];
    coorparateVehicle = json['coorparate_vehicle'];
    if (json['before_images'] != null) {
      beforeImages = <ClampingImage>[];
      json['before_images'].forEach((v) {
        beforeImages!.add(ClampingImage.fromJson(v));
      });
    }
    if (json['after_images'] != null) {
      afterImages = <ClampingImage>[];
      json['after_images'].forEach((v) {
        afterImages!.add(ClampingImage.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['clamp_id'] = clampId;
    data['status'] = status;
    data['violation_uid'] = violationUid;
    data['vehicle_number_plate'] = vehicleNumberPlate;
    data['vehicle_name'] = vehicleName;
    data['vehicle_type'] = vehicleType;
    if (vehicleImage != null) {
      data['vehicle_image'] = vehicleImage!.map((v) => v.toJson()).toList();
    }
    data['make_year'] = makeYear;
    data['number_plate_type'] = numberPlateType;
    data['clamped_date_time'] = clampedDateTime;
    data['violation_date_time'] = violationDateTime;
    data['is_settled'] = isSettled;
    data['clamped_by'] = clampedBy;
    data['description'] = description;
    data['coorparate_vehicle'] = coorparateVehicle;
    if (beforeImages != null) {
      data['before_images'] = beforeImages!.map((v) => v.toJson()).toList();
    }
    if (afterImages != null) {
      data['after_images'] = afterImages!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ClampingImage {
  String? uid;
  String? image;

  ClampingImage({this.uid, this.image});

  ClampingImage.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    image = json['image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['image'] = image;
    return data;
  }
}
