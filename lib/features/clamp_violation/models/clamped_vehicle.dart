import 'package:albalad_operator_app/shared/models/vehicle_image.dart';

class ClampedVehicle {
  String? uid;
  String? clampId;
  String? status;
  String? violationUid;
  String? vehicleNumberPlate;
  String? vehicleName;
  String? vehicleType;
  List<VehicleImage>? vehicleImage;
  int? makeYear;
  String? numberPlateType;
  String? clampedDateTime;

  ClampedVehicle({
    this.uid,
    this.clampId,
    this.status,
    this.violationUid,
    this.vehicleNumberPlate,
    this.vehicleName,
    this.vehicleType,
    this.vehicleImage,
    this.makeYear,
    this.numberPlateType,
    this.clampedDateTime,
  });

  ClampedVehicle.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    clampId = json['clamp_id'];
    status = json['status'];
    violationUid = json['violation_uid'];
    vehicleNumberPlate = json['vehicle_number_plate'];
    vehicleName = json['vehicle_name'];
    vehicleType = json['vehicle_type'];
    if (json['vehicle_image'] != null) {
      vehicleImage = <VehicleImage>[];
      json['vehicle_image'].forEach((v) {
        vehicleImage!.add(VehicleImage.fromJson(v));
      });
    }
    makeYear = int.tryParse(json['make_year'].toString());
    numberPlateType = json['number_plate_type'];
    clampedDateTime = json['clamped_date_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['clamp_id'] = clampId;
    data['status'] = status;
    data['violation_uid'] = violationUid;
    data['vehicle_number_plate'] = vehicleNumberPlate;
    data['vehicle_name'] = vehicleName;
    data['vehicle_type'] = vehicleType;
    if (vehicleImage != null) {
      data['vehicle_image'] = vehicleImage!.map((v) => v.toJson()).toList();
    }
    data['make_year'] = makeYear;
    data['number_plate_type'] = numberPlateType;
    data['clamped_date_time'] = clampedDateTime;
    return data;
  }
}
