import 'package:albalad_operator_app/features/clamp_violation/provider/clamping_request_list_provider.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/clamp_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/clamp_violation/widgets/clamp_request_card.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/no_clamping_request_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ClampRequestListing extends ConsumerWidget {
  final String previousRoute;
  const ClampRequestListing({required this.previousRoute, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(clampingRequestListNotifierProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          tr(context, 'new_clamping_requests'),
          style: TextStyles.ts18w600c353535,
        ),
        Gap(7.h),
        if (state.items.isEmpty && !state.hasMore) ...[
          Gap(0.1.sh),
          const NoClampingRequestWidget(),
        ] else if (state.items.isEmpty && state.isLoading) ...[
          const SkeletonParkingViolations(),
        ] else
          ListView.separated(
            shrinkWrap: true,
            physics: const PageScrollPhysics(),
            itemBuilder: (context, index) {
              if (index == state.items.length) {
                return const Center(child: CustomGradientSpinner());
              }
              final item = state.items[index];
              return ClampRequestCard(
                clampRequest: item,
                previousRoute: previousRoute,
              );
            },
            separatorBuilder: (context, index) => SizedBox(height: 24.h),
            itemCount: state.items.length + (state.hasMore ? 1 : 0),
          )
      ],
    );
  }
}
