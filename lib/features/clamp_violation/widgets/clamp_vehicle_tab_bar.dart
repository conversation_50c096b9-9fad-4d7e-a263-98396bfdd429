import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ClampVehicleTabBar extends StatelessWidget {
  final int value;
  final ValueChanged<int> onChanged;
  const ClampVehicleTabBar(
      {required this.value, required this.onChanged, super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 10.w,
      children: [
        tr(context, 'requests'),
        tr(context, 'clamped'),
        tr(context, 'vehicles'),
        tr(context, 'assigned')
      ].asMap().entries.map((entry) {
        int index = entry.key; // Index of the item
        String text = entry.value; // Corresponding text

        return InkWell(
          onTap: () => onChanged(index), // Call onChanged when tapped,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: ColorConstants.colorE1DDD2.withValues(alpha: 0.5),
              ),
              borderRadius: BorderRadius.circular(20.r),
              color: value == index ? ColorConstants.primaryColor : null,
            ),
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 7.h),
            child: Text(
              text, // Display index if needed
              style: value == index
                  ? TextStyles.ts12w600cE1DDD2
                  : TextStyles.ts12w600c94684E,
            ),
          ),
        );
      }).toList(),
    );
  }
}
