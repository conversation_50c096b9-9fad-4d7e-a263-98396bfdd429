import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SkeletonClampedDetails extends StatelessWidget {
  const SkeletonClampedDetails({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: ListView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(
                color: ColorConstants.colorEAEAEA,
                width: 1.w,
              ),
            ),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text.rich(
                      TextSpan(
                        text: tr(context, 'id'),
                        style: TextStyles.ts12w400c4D4D4D,
                        children: [
                          TextSpan(
                            text: ' 2586547',
                            style: TextStyles.ts12w700c353535,
                          ),
                        ],
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color: ColorConstants.colorFFF7E2,
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      padding:
                          EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.h),
                      child: Text(
                        'Not Settled',
                        style: TextStyles.ts12w600cE8B020,
                      ),
                    ),
                  ],
                ),
                Gap(15.h),
                Divider(
                  height: 0,
                  thickness: 1.h,
                  color: ColorConstants.colorF1F1F1,
                ),
                Gap(15.h),
                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: ColorConstants.colorF1EFE9,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      height: 35.h,
                      width: 35.w,
                      child: Image.asset('car'.asImagePng()),
                    ),
                    Gap(10.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('7403 RUA', style: TextStyles.ts14w600c181818),
                        Text('Toyota Camry', style: TextStyles.ts10w400cA1A09B),
                      ],
                    ),
                  ],
                ),
                Gap(16.h),
                Row(
                  spacing: 8.w,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20.r),
                        border: Border.all(
                          color: ColorConstants.colorE1DDD2,
                          width: 0.5.w,
                        ),
                      ),
                      padding:
                          EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.h),
                      child: Text(
                        '2002',
                        style: TextStyles.ts12w600c94684E,
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20.r),
                        border: Border.all(
                          color: ColorConstants.colorE1DDD2,
                          width: 0.5.w,
                        ),
                      ),
                      padding:
                          EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.h),
                      child: Text(
                        'Sedan',
                        style: TextStyles.ts12w600c94684E,
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20.r),
                        border: Border.all(
                          color: ColorConstants.colorE1DDD2,
                          width: 0.5.w,
                        ),
                      ),
                      padding:
                          EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.h),
                      child: Text(
                        'Private',
                        style: TextStyles.ts12w600c94684E,
                      ),
                    ),
                  ],
                ),
                Gap(16.h),
                Divider(
                  height: 0,
                  thickness: 1.h,
                  color: ColorConstants.colorF1F1F1,
                ),
                Gap(16.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      tr(context, 'clamped_date_and_time'),
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                    Row(
                      children: [
                        SvgPicture.asset(
                          'clock'.asIconSvg(),
                          height: 14.h,
                          width: 14.w,
                        ),
                        Gap(5.w),
                        Text(
                          '20-11-2024, 12:00:00',
                          style: TextStyles.ts12w400c4D4D4D,
                        ),
                      ],
                    ),
                  ],
                ),
                Gap(10.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      tr(context, 'clamped_by'),
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                    Text(
                      'Suhaib Shawish',
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                  ],
                ),
              ],
            ),
          ),
          Gap(20.h),
          Text(
            tr(context, 'violation_description'),
            style: TextStyles.ts18w600c353535,
          ),
          Gap(6.h),
          Text(
            'The car is parked incorrectly, with its wheels crossing the parking lines, taking up space and affecting nearby spots.',
            style: TextStyles.ts14w600c959595,
          ),
          Gap(24.h),
          Text(
            tr(context, 'violation_images'),
            style: TextStyles.ts18w600c353535,
          ),
          Gap(6.h),
          Row(
            children: [
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16.r),
                  child: Image.asset(
                    'violation-sample'.asImagePng(),
                    height: 153.58.h,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Gap(11.w),
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16.r),
                  child: Image.asset(
                    'violation-sample'.asImagePng(),
                    height: 153.58.h,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
