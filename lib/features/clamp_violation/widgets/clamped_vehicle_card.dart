import 'package:albalad_operator_app/features/clamp_violation/models/clamped_vehicle.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/clamped_details_screen.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/status_builder.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';

class ClampedVehicleCard extends StatelessWidget {
  final ClampedVehicle clamped;
  final String previousRoute;
  const ClampedVehicleCard({
    required this.clamped,
    required this.previousRoute,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final vehicleImage = clamped.vehicleImage?.firstOrNull?.image;
    return InkWell(
      onTap: () {
        final arguments = ClampedDetailsScreen(
          uid: clamped.uid ?? '',
          previousRoute: previousRoute,
        );
        Navigator.of(context).pushNamed(
          ClampedDetailsScreen.route,
          arguments: arguments,
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              offset: const Offset(0, 0),
              blurRadius: 20,
              spreadRadius: 0,
            ),
          ],
        ),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text.rich(
                      TextSpan(
                        text: tr(context, 'id'),
                        style: TextStyles.ts12w400c4D4D4D,
                        children: [
                          TextSpan(
                            text: ' ${clamped.clampId}',
                            style: TextStyles.ts12w700c353535,
                          ),
                        ],
                      ),
                    ),
                    const Spacer(),
                    StatusBuilder(
                      status: clamped.status,
                    ),
                  ],
                ),
                Gap(10.h),
                Divider(
                  color: ColorConstants.colorF1F1F1,
                  thickness: 1.h,
                  height: 0,
                ),
                Gap(16.h),
                Row(
                  children: [
                    Container(
                      height: 35.h,
                      width: 35.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                        color: ColorConstants.colorF1EFE9,
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8.r),
                        child: CachedNetworkImage(
                          imageUrl: vehicleImage ?? 'https://',
                          memCacheWidth: 200,
                          fit: BoxFit.fill,
                          errorWidget: (context, url, error) =>
                              Image.asset('car'.asImagePng()),
                        ),
                      ),
                    ),
                    Gap(10.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          clamped.vehicleNumberPlate ?? '',
                          style: TextStyles.ts14w600c181818,
                        ),
                        Text(
                          clamped.vehicleName ?? '',
                          style: TextStyles.ts10w400cA1A09B,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            Gap(16.h),
            Divider(
              color: ColorConstants.colorF1F1F1,
              thickness: 1.h,
              height: 0,
            ),
            Gap(10.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tr(context, 'clamped_date_and_time'),
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                    Gap(5.h),
                    Row(
                      children: [
                        SvgPicture.asset(
                          'clock'.asIconSvg(),
                          height: 14.h,
                          width: 14.w,
                        ),
                        Gap(5.w),
                        Text(
                          clamped.clampedDateTime ?? '',
                          style: TextStyles.ts12w400c4D4D4D,
                          textDirection: TextDirection.ltr,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
