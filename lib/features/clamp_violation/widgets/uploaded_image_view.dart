import 'dart:io';

import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';

class UploadedImageView extends StatelessWidget {
  final XFile xFile;
  final void Function()? onRemoved;
  const UploadedImageView({required this.xFile, this.onRemoved, super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Image.file(
            File(xFile.path),
            height: 154.h,
            width: (1.sw - 44.w) / 2,
            fit: BoxFit.cover,
          ),
        ),
        Positioned(
          right: 8,
          top: 8,
          child: InkWell(
            onTap: onRemoved,
            child: Container(
              width: 24.w,
              height: 24.h,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: ColorConstants.colorF26464,
              ),
              alignment: Alignment.center,
              child: Icon(
                Icons.delete_rounded,
                size: 18,
                color: Colors.white,
              ),
            ),
          ),
        )
      ],
    );
  }
}
