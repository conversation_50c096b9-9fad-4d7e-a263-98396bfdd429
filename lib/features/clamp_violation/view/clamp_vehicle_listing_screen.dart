import 'package:albalad_operator_app/features/clamp_violation/provider/assigned_clamping_list_provider.dart';
import 'package:albalad_operator_app/features/clamp_violation/provider/clamped_vehicle_list_provider.dart';
import 'package:albalad_operator_app/features/clamp_violation/provider/clamping_request_list_provider.dart';
import 'package:albalad_operator_app/features/clamp_violation/widgets/assigned_clamp_listing.dart';
import 'package:albalad_operator_app/features/clamp_violation/widgets/clamp_request_listing.dart';
import 'package:albalad_operator_app/features/clamp_violation/widgets/clamp_vehicle_tab_bar.dart';
import 'package:albalad_operator_app/features/clamp_violation/widgets/clamped_vehicle_listing.dart';
import 'package:albalad_operator_app/features/clamp_violation/widgets/parking_violation_listing.dart';
import 'package:albalad_operator_app/features/home/<USER>/home_search_field.dart';
import 'package:albalad_operator_app/features/number_plate_scanner/view/number_plate_scanner.dart';
import 'package:albalad_operator_app/features/search/provider/search_provider.dart';
import 'package:albalad_operator_app/features/search/view/search_screen.dart';
import 'package:albalad_operator_app/features/violation/provider/parking_violation_listing_provider.dart';
import 'package:albalad_operator_app/features/violation/widgets/skeleton_parking_violation_card.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:albalad_operator_app/shared/widgets/smart_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ClampVehicleListingScreen extends HookConsumerWidget {
  static const route = '/clamp_vehicle_listing';
  final int tabIndex;
  const ClampVehicleListingScreen({this.tabIndex = 0, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedTab = useState<int>(tabIndex);

    final notifiers = (
      parking: ref.read(parkingViolationListingNotifierProvider.notifier),
      request: ref.read(clampingRequestListNotifierProvider.notifier),
      clamped: ref.read(clampedVehicleListNotifierProvider.notifier),
      assigned: ref.read(assignedClampingListNotifierProvider.notifier),
    );

    final state = (
      parking: ref.watch(parkingViolationListingNotifierProvider),
      request: ref.watch(clampingRequestListNotifierProvider),
      clamped: ref.watch(clampedVehicleListNotifierProvider),
      assigned: ref.watch(assignedClampingListNotifierProvider),
    );

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifiers.parking.fetchParkingViolationList();
        notifiers.request.fetchClampingRequestList();
        notifiers.clamped.fetchClampedVehicleList();
        notifiers.assigned.fetchAssignedClampingList();
      });
      return null;
    }, []);

    final connectionError = (
      parking: state.parking.isConnectionError,
      request: state.request.isConnectionError,
      clamped: state.clamped.isConnectionError,
      assigned: state.assigned.isConnectionError,
    );

    return SmartScaffold(
      isInternetAvailable: !(connectionError.parking &&
          connectionError.request &&
          connectionError.clamped &&
          connectionError.assigned),
      retryConnection: () {
        notifiers.parking.reset();
        notifiers.request.reset();
        notifiers.clamped.reset();
        notifiers.assigned.reset();
        Future.sync(
          () {
            notifiers.request.fetchClampingRequestList();
            notifiers.clamped.fetchClampedVehicleList();
            notifiers.parking.fetchParkingViolationList();
            notifiers.assigned.fetchAssignedClampingList();
          },
        );
      },
      appBar: InnerAppBar(
        title: Text(tr(context, 'clampVehicle')),
      ),
      body: NotificationListener<ScrollNotification>(
        onNotification: (scrollNotification) {
          if (scrollNotification is ScrollEndNotification &&
              scrollNotification.metrics.extentAfter == 0) {
            if (selectedTab.value == 0 && state.request.isLoading == false) {
              notifiers.request.fetchClampingRequestList();
            } else if (selectedTab.value == 1 &&
                state.clamped.isLoading == false) {
              notifiers.clamped.fetchClampedVehicleList();
            } else if (selectedTab.value == 2 &&
                state.parking.isLoading == false) {
              notifiers.parking.fetchParkingViolationList();
            } else if (selectedTab.value == 3 &&
                state.assigned.isLoading == false) {
              notifiers.assigned.fetchAssignedClampingList();
            }
          }
          return false;
        },
        child: RefreshIndicator(
          onRefresh: () {
            notifiers.parking.reset();
            notifiers.request.reset();
            notifiers.clamped.reset();
            notifiers.assigned.reset();
            return Future.sync(
              () {
                notifiers.request.fetchClampingRequestList();
                notifiers.clamped.fetchClampedVehicleList();
                notifiers.parking.fetchParkingViolationList();
                notifiers.assigned.fetchAssignedClampingList();
              },
            );
          },
          child: ListView(
            padding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 30.h,
            ),
            children: [
              HomeSearchField(
                hintText: tr(context, 'searchVehicleNumber'),
                readOnly: true,
                onPress: () {
                  final notifier = ref.read(searchNotifierProvider.notifier);
                  notifier.reset();
                  final arguments = SearchScreen(previousRoute: route);
                  Navigator.pushNamed(context, SearchScreen.route,
                      arguments: arguments);
                },
                onTapSuffix: () {
                  final arguments = NumberPlateScanner(previousRoute: route);
                  Navigator.of(context).pushNamed(
                    NumberPlateScanner.route,
                    arguments: arguments,
                  );
                },
              ),
              Gap(16.h),
              ClampVehicleTabBar(
                onChanged: (value) => selectedTab.value = value,
                value: selectedTab.value,
              ),
              Gap(24.h),
              switch (selectedTab.value) {
                0 => ClampRequestListing(previousRoute: route),
                1 => ClampedVehicleListing(previousRoute: route),
                2 => ParkingViolationListing(previousRoute: route),
                _ => AssignedClampListing(),
              }
            ],
          ),
        ),
      ),
    );
  }
}

class SkeletonParkingViolations extends StatelessWidget {
  const SkeletonParkingViolations({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: ListView.separated(
        shrinkWrap: true,
        itemBuilder: (context, index) => SkeletonParkingViolationCard(),
        separatorBuilder: (context, index) => Gap(24.h),
        itemCount: 5,
      ),
    );
  }
}
