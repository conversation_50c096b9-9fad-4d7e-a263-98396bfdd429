import 'package:albalad_operator_app/features/clamp_violation/provider/clamping_request_list_provider.dart';
import 'package:albalad_operator_app/features/clamp_violation/provider/close_clamping_provider.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/clamp_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/success_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/provider/vehicle_details_provider.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/custom_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CloseClampingSheet extends HookConsumerWidget {
  final String violationUid;
  final String vehicleUid;
  final String previousRoute;
  const CloseClampingSheet({
    required this.violationUid,
    required this.vehicleUid,
    required this.previousRoute,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    double bottom = MediaQuery.of(context).viewInsets.bottom;
    double paddingBottom = 30.h;
    if (bottom > 0) {
      paddingBottom = bottom;
    }

    final formKey = useMemoized(() => GlobalKey<FormState>());
    final noteController = useTextEditingController();

    final notifier = ref.read(closeClampingNotifierProvider.notifier);
    final state = ref.watch(closeClampingNotifierProvider);

    ref.listen(closeClampingNotifierProvider, (previous, next) {
      if (next is CloseClampingSuccess) {
        if (previousRoute == VehicleDetailsScreen.route) {
          final notifier = ref.read(vehicleDetailsNotifierProvider.notifier);
          notifier.getVehicleDetails(uid: vehicleUid);
        } else {
          final requestNotifier =
              ref.read(clampingRequestListNotifierProvider.notifier);
          requestNotifier.reset();
          requestNotifier.fetchClampingRequestList();
        }

        final arguments = SuccessScreen(
          title: tr(context, 'successful'),
          message: tr(context, 'clamping_procedure_successfully_closed'),
          onPressed: (context) {
            Navigator.popUntil(
              context,
              ModalRoute.withName(
                previousRoute == VehicleDetailsScreen.route
                    ? VehicleDetailsScreen.route
                    : ClampVehicleListingScreen.route,
              ),
            );
          },
        );
        Navigator.pushReplacementNamed(
          context,
          SuccessScreen.route,
          arguments: arguments,
        );
      }
    });

    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 43.h, 16.w, paddingBottom),
      child: SingleChildScrollView(
        child: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                tr(context, 'close_clamping'),
                style: TextStyles.ts18w600c353535,
              ),
              Gap(10.h),
              CustomTextFormField(
                controller: noteController,
                labelText: tr(context, 'note_star'),
                hintText: tr(context, 'note_here'),
                maxLines: 5,
                validator: (p0) {
                  if (p0 == null || p0.isEmpty) {
                    return tr(context, 'please_enter_note');
                  }
                  // if (p0.length < 10) {
                  //   return tr(context, 'note')_minimum_chars_validation(10);
                  // }
                  return null;
                },
              ),
              Gap(20.h),
              if (state is CloseClampingLoading)
                const Center(
                  child: CustomGradientSpinner(),
                )
              else
                ElevatedButton(
                  onPressed: () {
                    if (formKey.currentState?.validate() == true) {
                      notifier.submitCloseClamping(
                        violationUid: violationUid,
                        closedReason: noteController.text,
                      );
                    }
                  },
                  child: Text(tr(context, 'submit')),
                )
            ],
          ),
        ),
      ),
    );
  }
}
