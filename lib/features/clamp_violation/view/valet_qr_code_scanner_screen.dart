import 'dart:io';

import 'package:albalad_operator_app/features/valet/view/current_valet_vehicles_screen.dart';
import 'package:albalad_operator_app/features/valet/view/generate_valet_ticket_screen.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';

class ValetQrCodeScannerScreen extends StatefulWidget {
  const ValetQrCodeScannerScreen({super.key});

  @override
  State<ValetQrCodeScannerScreen> createState() =>
      _ValetQrCodeScannerScreenState();
}

class _ValetQrCodeScannerScreenState extends State<ValetQrCodeScannerScreen> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  Barcode? result;
  QRViewController? controller;

  // In order to get hot reload to work we need to pause the camera if the platform
  // is android, or resume the camera if the platform is iOS.
  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      controller!.pauseCamera();
    } else if (Platform.isIOS) {
      controller!.resumeCamera();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: InnerAppBar(
        title: Text(tr(context, 'scan_qr')),
      ),
      body: _buildQrView(context),
    );
  }

  Widget _buildQrView(BuildContext context) {
    return QRView(
      key: qrKey,
      onQRViewCreated: _onQRViewCreated,
      overlay: QrScannerOverlayShape(
        borderColor: Colors.red,
        borderRadius: 10,
        borderLength: 30,
        borderWidth: 10,
        cutOutSize: 0.7.sw,
      ),
      onPermissionSet: (ctrl, p) => _onPermissionSet(context, ctrl, p),
    );
  }

  void _onQRViewCreated(QRViewController controller) {
    setState(() {
      this.controller = controller;
    });
    controller.scannedDataStream.listen((scanData) {
      if (scanData.code != null) {
        final uri = Uri.parse(scanData.code!);
        String? action = uri.queryParameters['action'];
        String? uid = uri.queryParameters['uid'];
        if (action == 'valet' && uid != null) {
          controller
              .pauseCamera(); // Pause the camera to avoid multiple triggers.
          if (!mounted) return;
          final arguments = GenerateValetTicketScreen(
            previousRoute: CurrentValetVehiclesScreen.route,
            valetUid: uid,
          );
          Navigator.pushReplacementNamed(
            context,
            GenerateValetTicketScreen.route,
            arguments: arguments,
          );
        }
      }
    });
  }

  bool isPermissionMessageShown = false;

  void _onPermissionSet(BuildContext context, QRViewController ctrl, bool p) {
    if (!p) {
      if (!isPermissionMessageShown) {
        isPermissionMessageShown = true;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No permission for camera access')),
        );
      }
    }
  }
}
