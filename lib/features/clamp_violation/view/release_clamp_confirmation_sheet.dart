import 'package:albalad_operator_app/features/clamp_violation/provider/clamped_vehicle_list_provider.dart';
import 'package:albalad_operator_app/features/clamp_violation/provider/release_clamp_provider.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/clamp_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/success_screen.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/helper/dialog_helper.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/elevated_secondary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ReleaseClampConfirmationSheet extends ConsumerWidget {
  final String vehicleUid;
  final String previousRoute;
  const ReleaseClampConfirmationSheet(
      {required this.vehicleUid, required this.previousRoute, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(releaseClampNotifierProvider);
    final notifier = ref.watch(releaseClampNotifierProvider.notifier);
    ref.listen(
      releaseClampNotifierProvider,
      (previous, next) {
        if (next is ReleaseClampSuccess) {
          if (previousRoute == ClampVehicleListingScreen.route) {
            final preNotifier =
                ref.read(clampedVehicleListNotifierProvider.notifier);
            preNotifier.reset();
            preNotifier.fetchClampedVehicleList();
          }
          final arguments = SuccessScreen(
            title: tr(context, 'successful'),
            message: tr(context, 'release_clamp_success_message'),
            onPressed: (context) =>
                Navigator.popUntil(context, ModalRoute.withName(previousRoute)),
          );
          Navigator.pushNamed(context, SuccessScreen.route,
              arguments: arguments);
        }

        if (next is ReleaseClampFailure && next.message != null) {
          DialogHelper.showErrorDialog(
              context: context, message: next.message!);
        }
      },
    );
    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 50.h, 16.w, 30.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SvgPicture.asset(
            'info-circle'.asIconSvg(),
            height: 94.17.h,
            width: 94.17.w,
          ),
          Gap(18.h),
          Text(
            tr(context, 'release_clamp_question'),
            style: TextStyles.ts30w700c44322D,
            textAlign: TextAlign.center,
          ),
          Text(
            tr(context, 'release_clamp_description'),
            style: TextStyles.ts14w600c959595,
            textAlign: TextAlign.center,
          ),
          Gap(50.h),
          Row(
            spacing: 10.w,
            children: [
              Expanded(
                child: ElevatedSecondaryButton(
                  title: tr(context, 'cancel'),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
              Expanded(
                child: state is ReleaseClampLoading
                    ? const Center(
                        child: CustomGradientSpinner(),
                      )
                    : ElevatedButton(
                        onPressed: () => notifier.releaseClamp(vehicleUid),
                        child: Text(tr(context, 'confirm')),
                      ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
