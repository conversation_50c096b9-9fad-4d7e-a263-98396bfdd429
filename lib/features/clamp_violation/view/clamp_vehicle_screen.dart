import 'package:albalad_operator_app/features/clamp_violation/provider/assigned_clamping_list_provider.dart';
import 'package:albalad_operator_app/features/clamp_violation/provider/clamp_vehicle_provider.dart';
import 'package:albalad_operator_app/features/clamp_violation/provider/clamped_vehicle_list_provider.dart';
import 'package:albalad_operator_app/features/clamp_violation/provider/clamping_request_list_provider.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/clamp_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/close_clamping_sheet.dart';
import 'package:albalad_operator_app/features/clamp_violation/widgets/uploaded_image_view.dart';
import 'package:albalad_operator_app/features/success_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/provider/vehicle_details_provider.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/features/violation/widgets/upload_image_card.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/helper/dialog_helper.dart';
import 'package:albalad_operator_app/shared/widgets/bottom_navigation_bar_elevated_button.dart';
import 'package:albalad_operator_app/shared/widgets/custom_text_form_field.dart';
import 'package:albalad_operator_app/shared/widgets/image_source_sheet.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

class ClampVehicleScreen extends HookConsumerWidget {
  static const String route = '/clamp-vehicle';
  final String violationUid;
  final String? previousRoute;
  final String vehicleUid;
  const ClampVehicleScreen({
    required this.violationUid,
    this.previousRoute,
    required this.vehicleUid,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final descriptionController = useTextEditingController();
    final beforeImageFile = useState<XFile?>(null);
    final afterImageFile = useState<XFile?>(null);
    final showBeforeImageError = useState<bool>(false);
    final showAfterImageError = useState<bool>(false);

    ref.listen(clampVehicleNotifierProvider, (previous, next) {
      if (next is ClampVehicleSuccess) {
        if (previousRoute == VehicleDetailsScreen.route) {
          final notifier = ref.read(vehicleDetailsNotifierProvider.notifier);
          notifier.getVehicleDetails(uid: vehicleUid);
        } else {
          final notifiers = (
            assigned: ref.read(assignedClampingListNotifierProvider.notifier),
            clamped: ref.read(clampedVehicleListNotifierProvider.notifier),
            request: ref.read(clampingRequestListNotifierProvider.notifier),
          );

          notifiers.request.reset();
          notifiers.clamped.reset();
          notifiers.assigned.reset();

          notifiers.request.fetchClampingRequestList();
          notifiers.clamped.fetchClampedVehicleList();
          notifiers.assigned.fetchAssignedClampingList();
        }

        final arguments = SuccessScreen(
          title: tr(context, 'clamping_successful'),
          message: tr(context, 'clamping_successful_message'),
          onPressed: (context) {
            Navigator.popUntil(
              context,
              ModalRoute.withName(
                previousRoute == VehicleDetailsScreen.route
                    ? VehicleDetailsScreen.route
                    : ClampVehicleListingScreen.route,
              ),
            );
          },
        );
        Navigator.pushReplacementNamed(
          context,
          SuccessScreen.route,
          arguments: arguments,
        );
      }
      if (next is ClampVehicleError) {
        DialogHelper.showErrorDialog(context: context, message: next.message);
      }
    });

    final notifier = ref.read(clampVehicleNotifierProvider.notifier);
    final state = ref.watch(clampVehicleNotifierProvider);

    return Scaffold(
      appBar: InnerAppBar(
        title: Text(tr(context, 'clampVehicle')),
      ),
      body: Form(
        key: formKey,
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
          children: [
            CustomTextFormField(
              controller: descriptionController,
              labelText: tr(context, 'description_star'),
              maxLines: 5,
              hintText: tr(context, 'description_here'),
              autovalidateMode: AutovalidateMode.onUserInteraction,
              textCapitalization: TextCapitalization.sentences,
              validator: (p0) {
                if (p0 == null || p0.isEmpty) {
                  return tr(context, 'please_enter_description');
                }
                return null;
              },
            ),
            Gap(24.h),
            Text(
              tr(context, 'upload_image_star'),
              style: TextStyles.ts12w400c505050,
            ),
            Gap(5.h),
            Row(
              spacing: 10.w,
              children: [
                Expanded(
                  child: Column(
                    children: [
                      if (beforeImageFile.value != null)
                        UploadedImageView(
                          xFile: beforeImageFile.value!,
                          onRemoved: () => beforeImageFile.value = null,
                        )
                      else
                        UploadImageCard(
                          error: showBeforeImageError.value,
                          onTap: () async {
                            final image = await pickImage(context);
                            if (image != null) {
                              beforeImageFile.value = image;
                            }
                          },
                        ),
                      Gap(5.h),
                      Text(
                        tr(context, 'before'),
                        style: TextStyles.ts12w600c4D4D4D,
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      if (afterImageFile.value != null)
                        UploadedImageView(
                          xFile: afterImageFile.value!,
                          onRemoved: () => afterImageFile.value = null,
                        )
                      else
                        UploadImageCard(
                          error: showAfterImageError.value,
                          onTap: () {
                            pickImage(context).then((image) {
                              if (image != null) {
                                afterImageFile.value = image;
                              }
                            });
                          },
                        ),
                      Gap(5.h),
                      Text(
                        tr(context, 'after'),
                        style: TextStyles.ts12w600c4D4D4D,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Gap(24.h),
            Container(
              decoration: BoxDecoration(
                color: ColorConstants.colorF8F8F8,
                gradient: RadialGradient(
                  colors: [
                    Colors.white,
                    Colors.white,
                    ColorConstants.colorF9F9FF,
                  ],
                ),
                borderRadius: BorderRadius.circular(14.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    tr(context, 'could_not_find_vehicle'),
                    style: TextStyles.ts14w600c44322D,
                  ),
                  Text(
                    tr(context, 'vehicle_not_found_description'),
                    style: TextStyles.ts12w400c959595,
                  ),
                  Gap(12.h),
                  Divider(
                    color: ColorConstants.colorF1F1F1,
                    height: 0,
                    thickness: 1.h,
                  ),
                  Gap(14.h),
                  Align(
                    alignment: Alignment.centerRight,
                    child: ElevatedButton(
                      onPressed: () {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          builder: (context) => CloseClampingSheet(
                            violationUid: violationUid,
                            vehicleUid: vehicleUid,
                            previousRoute: previousRoute ?? '',
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        textStyle: TextStyles.ts12w600wE1DDD2,
                        padding: EdgeInsets.symmetric(
                          vertical: 6.h,
                          horizontal: 10.w,
                        ),
                        minimumSize: Size(98.w, 22.h),
                      ),
                      child: Text(tr(context, 'close_violation')),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigationBarElevatedButton(
        title: tr(context, 'submit'),
        isLoading: state is ClampVehicleLoading,
        onPressed: () {
          if (beforeImageFile.value == null) {
            showBeforeImageError.value = true;
          } else {
            showBeforeImageError.value = false;
          }
          if (afterImageFile.value == null) {
            showAfterImageError.value = true;
          } else {
            showAfterImageError.value = false;
          }
          if (formKey.currentState?.validate() == true &&
              beforeImageFile.value != null &&
              afterImageFile.value != null) {
            notifier.submitClampVehicle(
              vehicleUid: vehicleUid,
              description: descriptionController.text,
              beforeImage: beforeImageFile.value!.path,
              afterImage: afterImageFile.value!.path,
            );
          }
        },
      ),
    );
  }

  Future<XFile?> pickImage(BuildContext context) async {
    final imageSource = await showModalBottomSheet(
      context: context,
      builder: (context) => ImageSourceSheet(),
    );
    if (imageSource == null) return null;
    final ImagePicker picker = ImagePicker();
    final XFile? image =
        await picker.pickImage(source: imageSource, maxWidth: 1000);
    if (image != null) {
      final croppedImage = await cropImage(image: image);
      if (croppedImage != null) {
        return XFile(croppedImage.path);
      }
      return null;
    }
    return null;
  }

  Future<CroppedFile?> cropImage({required XFile image}) async {
    return ImageCropper().cropImage(
      sourcePath: image.path,
      uiSettings: [
        AndroidUiSettings(
          statusBarColor: Colors.black,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: false,
        ),
        IOSUiSettings(),
      ],
    );
  }
}
