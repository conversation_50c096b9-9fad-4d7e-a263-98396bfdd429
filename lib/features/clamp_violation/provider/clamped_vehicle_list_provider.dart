import 'dart:async';
import 'dart:io';

import 'package:albalad_operator_app/features/clamp_violation/models/clamped_vehicle.dart';
import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'clamped_vehicle_list_provider.g.dart';

class ClampedVehicleListState {
  final List<ClampedVehicle> items;
  final bool isLoading;
  final bool hasMore;
  final bool isConnectionError;
  final String? error;

  ClampedVehicleListState({
    required this.items,
    required this.isLoading,
    required this.hasMore,
    this.isConnectionError = false,
    this.error,
  });

  ClampedVehicleListState copyWith({
    List<ClampedVehicle>? items,
    bool? isLoading,
    bool? hasMore,
    bool? isConnectionError,
    String? error,
  }) {
    return ClampedVehicleListState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      isConnectionError: isConnectionError ?? this.isConnectionError,
      error: error ?? this.error,
    );
  }
}

@riverpod
class ClampedVehicleListNotifier extends _$ClampedVehicleListNotifier {
  @override
  ClampedVehicleListState build() {
    return ClampedVehicleListState(
      items: [],
      isLoading: false,
      hasMore: true,
      isConnectionError: false,
      error: null,
    );
  }

  int _currentPage = 1;

  Future<void> fetchClampedVehicleList() async {
    if (state.isLoading || !state.hasMore) return;
    state =
        state.copyWith(isLoading: true, error: null, isConnectionError: false);

    try {
      final violationServices = ref.read(violationServicesProvider);
      final response =
          await violationServices.fetchClampedVehicleList(page: _currentPage);
      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          final List<dynamic> records = data['records'] ?? [];
          final List<ClampedVehicle> clampedVehicles =
              records.map((e) => ClampedVehicle.fromJson(e)).toList();
          state = state.copyWith(
            items: [...state.items, ...clampedVehicles],
            isLoading: false,
            hasMore: data['pagination']?['has_next'] == true,
          );
          _currentPage++;
        } else {
          state = state.copyWith(
            isLoading: false,
            hasMore: false,
          );
        }
      } else {
        state = state.copyWith(
          isLoading: false,
          hasMore: false,
        );
      }
    } on SocketException {
      _handleError(appLocalization.translate('networkError'));
    } catch (e, stackTrace) {
      debugPrint('Error in fetchParkingViolationList: $e');
      debugPrint('StackTrace: $stackTrace');
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        _handleError(null, isConnectionError: true);
        return;
      }
      _handleError(e.toString());
    }
  }

  _handleError(String? error, {bool isConnectionError = false}) {
    state = state.copyWith(
        isLoading: false,
        hasMore: false,
        error: error,
        isConnectionError: isConnectionError);
  }

  void reset() {
    _currentPage = 1;
    state = ClampedVehicleListState(
      items: [],
      isLoading: false,
      hasMore: true,
      isConnectionError: false,
    );
  }
}
