import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'close_clamping_provider.g.dart';

abstract class CloseClampingState {}

class CloseClampingInitial extends CloseClampingState {}

class CloseClampingLoading extends CloseClampingState {}

class CloseClampingSuccess extends CloseClampingState {}

class CloseClampingError extends CloseClampingState {
  final String message;
  CloseClampingError(this.message);
}

@riverpod
class CloseClampingNotifier extends _$CloseClampingNotifier {
  @override
  CloseClampingState build() {
    return CloseClampingInitial();
  }

  final Dio _dio = DioClient().dio;

  Future<void> submitCloseClamping({
    required String violationUid,
    required String closedReason,
  }) async {
    state = CloseClampingLoading();
    try {
      final response = await _dio.post(
        ApiConstants.closeClampingViolation,
        data: {
          'closed_reason': closedReason,
          'violation': violationUid,
        },
        options: Options(headers: await ApiConstants.authHeaders()),
      );
      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          state = CloseClampingSuccess();
        } else {
          state = CloseClampingError(response.data['message']);
        }
      } else {
        state = CloseClampingError(response.data['message']);
      }
    } catch (e) {
      state = CloseClampingError(e.toString());
    }
  }
}
