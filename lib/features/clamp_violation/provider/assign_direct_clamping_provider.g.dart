// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'assign_direct_clamping_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$assignDirectClampingNotifierHash() =>
    r'd51ead4f5a720b653a5f09e6e3010d1cd709ed5f';

/// See also [AssignDirectClampingNotifier].
@ProviderFor(AssignDirectClampingNotifier)
final assignDirectClampingNotifierProvider = AutoDisposeNotifierProvider<
    AssignDirectClampingNotifier, AssignDirectClampingState>.internal(
  AssignDirectClampingNotifier.new,
  name: r'assignDirectClampingNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$assignDirectClampingNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AssignDirectClampingNotifier
    = AutoDisposeNotifier<AssignDirectClampingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
