// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'clamping_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$assignClampingNotifierHash() =>
    r'4a1411c1e61564497074f34b46b74548d459b3dc';

/// See also [AssignClampingNotifier].
@ProviderFor(AssignClampingNotifier)
final assignClampingNotifierProvider = AutoDisposeNotifierProvider<
    AssignClampingNotifier, AssignClampingState>.internal(
  AssignClampingNotifier.new,
  name: r'assignClampingNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$assignClampingNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AssignClampingNotifier = AutoDisposeNotifier<AssignClampingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
