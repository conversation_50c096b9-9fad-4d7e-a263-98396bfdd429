import 'package:albalad_operator_app/features/clamp_violation/models/clamped_details.dart';
import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'clamped_details_provider.g.dart';

abstract class ClampedDetailsState {}

class ClampedDetailsStateInitial extends ClampedDetailsState {}

class ClampedDetailsStateLoading extends ClampedDetailsState {}

class ClampedDetailsStateLoaded extends ClampedDetailsState {
  final ClampedDetails clampedDetails;

  ClampedDetailsStateLoaded(this.clampedDetails);
}

class ClampedDetailsStateError extends ClampedDetailsState {
  final String error;
  final bool isConnectionError;

  ClampedDetailsStateError(this.error, {this.isConnectionError = false});
}

@riverpod
class ClampedDetailsNotifier extends _$ClampedDetailsNotifier {
  @override
  ClampedDetailsState build() {
    return ClampedDetailsStateInitial();
  }

  Future<void> fetchClampedDetails(String uid) async {
    state = ClampedDetailsStateLoading();
    try {
      final violationServices = ref.read(violationServicesProvider);
      final clampedDetails = await violationServices.fetchClampedDetails(uid);
      state = ClampedDetailsStateLoaded(clampedDetails);
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = ClampedDetailsStateError(e.toString(), isConnectionError: true);
        return;
      }
      state = ClampedDetailsStateError(e.toString());
    }
  }
}
