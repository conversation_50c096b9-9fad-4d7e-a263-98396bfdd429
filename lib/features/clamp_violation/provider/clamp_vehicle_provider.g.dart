// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'clamp_vehicle_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$clampVehicleNotifierHash() =>
    r'3e1617779c4a1c8d3e1ace804a56f76e33c40c61';

/// See also [ClampVehicleNotifier].
@ProviderFor(ClampVehicleNotifier)
final clampVehicleNotifierProvider = AutoDisposeNotifierProvider<
    ClampVehicleNotifier, ClampVehicleState>.internal(
  ClampVehicleNotifier.new,
  name: r'clampVehicleNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$clampVehicleNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ClampVehicleNotifier = AutoDisposeNotifier<ClampVehicleState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
