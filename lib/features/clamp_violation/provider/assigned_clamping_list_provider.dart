import 'dart:io';

import 'package:albalad_operator_app/features/clamp_violation/models/assigned_clamp_violation.dart';
import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'assigned_clamping_list_provider.g.dart';

class AssignedClampingListState {
  final List<AssignedClampViolation> items;
  final bool isLoading;
  final bool hasMore;
  final String? error;
  final bool isConnectionError;

  AssignedClampingListState({
    required this.items,
    required this.isLoading,
    required this.hasMore,
    required this.isConnectionError,
    this.error,
  });

  AssignedClampingListState copyWith({
    List<AssignedClampViolation>? items,
    bool? isLoading,
    bool? hasMore,
    bool? isConnectionError,
    String? error,
  }) {
    return AssignedClampingListState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      isConnectionError: isConnectionError ?? this.isConnectionError,
      error: error ?? this.error,
    );
  }
}

@riverpod
class AssignedClampingListNotifier extends _$AssignedClampingListNotifier {
  @override
  AssignedClampingListState build() {
    return AssignedClampingListState(
      items: [],
      isLoading: false,
      hasMore: true,
      isConnectionError: false,
    );
  }

  int _currentPage = 1;

  Future<void> fetchAssignedClampingList() async {
    if (state.isLoading || !state.hasMore) return;
    state =
        state.copyWith(isLoading: true, error: null, isConnectionError: false);
    try {
      final violationServices = ref.read(violationServicesProvider);
      final response = await violationServices.fetchAssignedClampingList(
        page: _currentPage,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          final List<dynamic> records = data['records'] ?? [];

          final List<AssignedClampViolation> assignedClampingViolations =
              records.map((e) => AssignedClampViolation.fromJson(e)).toList();

          state = state.copyWith(
            items: [...state.items, ...assignedClampingViolations],
            isLoading: false,
            hasMore: data['pagination']?['has_next'] == true,
          );

          _currentPage++;
        } else {
          state = state.copyWith(
            isLoading: false,
            hasMore: false,
          );
        }
      } else {
        state = state.copyWith(
          isLoading: false,
          hasMore: false,
        );
      }
    } on SocketException {
      _handleError(appLocalization.translate('networkError'));
    } catch (e, stackTrace) {
      debugPrint('Error in fetchParkingViolationList: $e');
      debugPrint('StackTrace: $stackTrace');
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        _handleError(null, isConnectionError: true);
        return;
      }
      _handleError(e.toString());
    }
  }

  _handleError(String? error, {bool isConnectionError = false}) {
    state = state.copyWith(
        isLoading: false,
        hasMore: false,
        error: error,
        isConnectionError: isConnectionError);
  }

  void reset() {
    _currentPage = 1;
    state = AssignedClampingListState(
        items: [], isLoading: false, hasMore: true, isConnectionError: false);
  }
}
