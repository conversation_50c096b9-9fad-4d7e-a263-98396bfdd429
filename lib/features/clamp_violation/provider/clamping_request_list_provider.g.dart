// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'clamping_request_list_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$clampingRequestListNotifierHash() =>
    r'1b0f19265036fcd449d59dc318f5ce0ca8c74ce7';

/// See also [ClampingRequestListNotifier].
@ProviderFor(ClampingRequestListNotifier)
final clampingRequestListNotifierProvider = AutoDisposeNotifierProvider<
    ClampingRequestListNotifier, ClampingRequestListState>.internal(
  ClampingRequestListNotifier.new,
  name: r'clampingRequestListNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$clampingRequestListNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ClampingRequestListNotifier
    = AutoDisposeNotifier<ClampingRequestListState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
