import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'clamp_vehicle_provider.g.dart';

abstract class ClampVehicleState {}

class ClampVehicleInitial extends ClampVehicleState {}

class ClampVehicleLoading extends ClampVehicleState {}

class ClampVehicleSuccess extends ClampVehicleState {}

class ClampVehicleError extends ClampVehicleState {
  final String message;
  ClampVehicleError(this.message);
}

@riverpod
class ClampVehicleNotifier extends _$ClampVehicleNotifier {
  @override
  ClampVehicleState build() {
    return ClampVehicleInitial();
  }

  final Dio _dio = DioClient().dio;

  Future<void> submitClampVehicle({
    required String vehicleUid,
    required String description,
    required String beforeImage,
    required String afterImage,
  }) async {
    state = ClampVehicleLoading();
    try {
      List<MultipartFile> images = [
        await MultipartFile.fromFile(beforeImage),
        await MultipartFile.fromFile(afterImage),
      ];

      FormData formData = FormData.fromMap({
        "vehicle_uid": vehicleUid,
        "description": description,
        "images": images
      });

      final response = await _dio.post(
        ApiConstants.clampTowVehicle,
        data: formData,
        options: Options(headers: await ApiConstants.authFormDataHeaders()),
      );

      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          state = ClampVehicleSuccess();
        } else {
          state = ClampVehicleError(response.data['message']);
        }
      } else if (response.statusCode == 400) {
        Map<String, dynamic> errors = response.data['errors'] ?? {};
        if (errors.values.isNotEmpty) {
          state = ClampVehicleError(errors.values.first.toString());
        }
      } else {
        state = ClampVehicleError(response.data['message']);
      }
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = ClampVehicleError(
          appLocalization.translate('networkError'),
        );
        return;
      }
      state = ClampVehicleError(e.toString());
    }
  }
}
