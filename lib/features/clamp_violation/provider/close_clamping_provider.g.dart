// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'close_clamping_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$closeClampingNotifierHash() =>
    r'efd4b6b43351f221416fad3f2ee9f64cf3e1bb00';

/// See also [CloseClampingNotifier].
@ProviderFor(CloseClampingNotifier)
final closeClampingNotifierProvider = AutoDisposeNotifierProvider<
    CloseClampingNotifier, CloseClampingState>.internal(
  CloseClampingNotifier.new,
  name: r'closeClampingNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$closeClampingNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CloseClampingNotifier = AutoDisposeNotifier<CloseClampingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
