// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'clamped_details_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$clampedDetailsNotifierHash() =>
    r'352b1ce041ceec3cb560119d5dec357f5b449000';

/// See also [ClampedDetailsNotifier].
@ProviderFor(ClampedDetailsNotifier)
final clampedDetailsNotifierProvider = AutoDisposeNotifierProvider<
    ClampedDetailsNotifier, ClampedDetailsState>.internal(
  ClampedDetailsNotifier.new,
  name: r'clampedDetailsNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$clampedDetailsNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ClampedDetailsNotifier = AutoDisposeNotifier<ClampedDetailsState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
