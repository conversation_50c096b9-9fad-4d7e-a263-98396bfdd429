// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'release_clamp_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$releaseClampNotifierHash() =>
    r'c5bbf845dc6e5fa3ee83f53d4c9d63b6b62fc271';

/// See also [ReleaseClampNotifier].
@ProviderFor(ReleaseClampNotifier)
final releaseClampNotifierProvider = AutoDisposeNotifierProvider<
    ReleaseClampNotifier, ReleaseClampState>.internal(
  ReleaseClampNotifier.new,
  name: r'releaseClampNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$releaseClampNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ReleaseClampNotifier = AutoDisposeNotifier<ReleaseClampState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
