import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'assign_direct_clamping_provider.g.dart';

abstract class AssignDirectClampingState {}

class AssignDirectClampingInitial extends AssignDirectClampingState {}

class AssignDirectClampingLoading extends AssignDirectClampingState {}

class AssignDirectClampingSuccess extends AssignDirectClampingState {}

class AssignDirectClampingFailure extends AssignDirectClampingState {
  final String message;
  AssignDirectClampingFailure(this.message);
}

@riverpod
class AssignDirectClampingNotifier extends _$AssignDirectClampingNotifier {
  @override
  AssignDirectClampingState build() => AssignDirectClampingInitial();

  final Dio _dio = DioClient().dio;

  Future<void> submit({
    required String vehicleUid,
    required String violationTypeUid,
    required String description,
    required String clampingOperatorUid,
    required List<String> violationImages,
  }) async {
    state = AssignDirectClampingLoading();
    try {
      FormData formData = FormData.fromMap({
        'vehicle_uid': vehicleUid,
        'violation_type_uid': violationTypeUid,
        'description': description,
        'assigned_operator': clampingOperatorUid,
        'violation_images':
            violationImages.map((e) => MultipartFile.fromFileSync(e)).toList(),
      });

      final response = await _dio.post(
        ApiConstants.assignViolation,
        options: Options(
          headers: await ApiConstants.authHeaders(),
        ),
        data: formData,
      );

      if (response.statusCode == 400) {
        Map<String, dynamic>? errors = response.data['errors'];
        String? message = response.data['message'];
        if (errors != null && errors.isNotEmpty) {
          state =
              AssignDirectClampingFailure(errors.values.firstOrNull.toString());
          return;
        }
        if (message != null) {
          state = AssignDirectClampingFailure(message);
          return;
        }
      }
      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          state = AssignDirectClampingSuccess();
          return;
        }
      }
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = AssignDirectClampingFailure(
          appLocalization.translate('networkError'),
        );
        return;
      }
      state = AssignDirectClampingFailure(e.toString());
    }
  }
}
