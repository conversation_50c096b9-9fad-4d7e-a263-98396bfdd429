// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'assigned_clamping_list_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$assignedClampingListNotifierHash() =>
    r'191acfce5adc8b9a592cb6bce707a6a40f70ebdd';

/// See also [AssignedClampingListNotifier].
@ProviderFor(AssignedClampingListNotifier)
final assignedClampingListNotifierProvider = AutoDisposeNotifierProvider<
    AssignedClampingListNotifier, AssignedClampingListState>.internal(
  AssignedClampingListNotifier.new,
  name: r'assignedClampingListNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$assignedClampingListNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AssignedClampingListNotifier
    = AutoDisposeNotifier<AssignedClampingListState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
