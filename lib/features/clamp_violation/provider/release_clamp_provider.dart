import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'release_clamp_provider.g.dart';

abstract class ReleaseClampState {}

class ReleaseClampInitial extends ReleaseClampState {}

class ReleaseClampLoading extends ReleaseClampState {}

class ReleaseClampSuccess extends ReleaseClampState {}

class ReleaseClampFailure extends ReleaseClampState {
  final String? message;

  ReleaseClampFailure({this.message});
}

@riverpod
class ReleaseClampNotifier extends _$ReleaseClampNotifier {
  @override
  ReleaseClampState build() => ReleaseClampInitial();

  final Dio _dio = DioClient().dio;

  Future<void> releaseClamp(String vehicleUid) async {
    state = ReleaseClampLoading();
    try {
      final response = await _dio.post(
        ApiConstants.releaseClampedVehicle,
        data: {
          'clamp_vehicle_uid': vehicleUid,
        },
        options: Options(headers: await ApiConstants.authHeaders()),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          state = ReleaseClampSuccess();
          return;
        } else {
          state = ReleaseClampFailure();
          return;
        }
      }
      if (response.statusCode == 400) {
        final data = response.data;
        Map<String, dynamic> errors = data['errors'] ?? {};
        if (errors.isNotEmpty) {
          state = ReleaseClampFailure(
            message: errors.values.first.toString(),
          );
          return;
        }
      }
      state = ReleaseClampFailure();
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = ReleaseClampFailure(
          message: appLocalization.translate('networkError'),
        );
        return;
      }
      state = ReleaseClampFailure();
    }
  }
}
