import 'dart:async';
import 'dart:io';

import 'package:albalad_operator_app/features/clamp_violation/models/clamping_request_item.dart';
import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'clamping_request_list_provider.g.dart';

class ClampingRequestListState {
  final List<ClampingRequestItem> items;
  final bool isLoading;
  final bool hasMore;
  final bool isConnectionError;
  final String? error;

  ClampingRequestListState({
    required this.items,
    required this.isLoading,
    required this.hasMore,
    this.isConnectionError = false,
    this.error,
  });

  ClampingRequestListState copyWith({
    List<ClampingRequestItem>? items,
    bool? isLoading,
    bool? hasMore,
    bool? isConnectionError,
    String? error,
  }) {
    return ClampingRequestListState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      isConnectionError: isConnectionError ?? this.isConnectionError,
      error: error ?? this.error,
    );
  }
}

@riverpod
class ClampingRequestListNotifier extends _$ClampingRequestListNotifier {
  @override
  ClampingRequestListState build() {
    return ClampingRequestListState(
      items: [],
      isLoading: false,
      hasMore: true,
      error: null,
      isConnectionError: false,
    );
  }

  int _currentPage = 1;

  Future<void> fetchClampingRequestList() async {
    if (state.isLoading || !state.hasMore) return;
    state = state.copyWith(
      isLoading: true,
      error: null,
      isConnectionError: false,
    );

    try {
      final violationServices = ref.read(violationServicesProvider);
      final response =
          await violationServices.fetchClampingRequestList(page: _currentPage);
      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          final List<dynamic> records = data['records'] ?? [];
          final List<ClampingRequestItem> currentViolations =
              records.map((e) => ClampingRequestItem.fromJson(e)).toList();
          state = state.copyWith(
            items: [...state.items, ...currentViolations],
            isLoading: false,
            hasMore: data['pagination']?['has_next'] == true,
          );
          _currentPage++;
        } else {
          state = state.copyWith(
            isLoading: false,
            hasMore: false,
          );
        }
      } else {
        state = state.copyWith(
          isLoading: false,
          hasMore: false,
        );
      }
    } on SocketException {
      _handleError(appLocalization.translate('networkError'));
    } catch (e, stackTrace) {
      debugPrint('Error in fetchParkingViolationList: $e');
      debugPrint('StackTrace: $stackTrace');
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        _handleError(null, isConnectionError: true);
        return;
      }
      _handleError(e.toString());
    }
  }

  _handleError(String? error, {bool isConnectionError = false}) {
    state = state.copyWith(
        isLoading: false,
        hasMore: false,
        error: error,
        isConnectionError: isConnectionError);
  }

  void reset() {
    _currentPage = 1;
    state = ClampingRequestListState(
      items: [],
      isLoading: false,
      hasMore: true,
      isConnectionError: false,
    );
  }
}
