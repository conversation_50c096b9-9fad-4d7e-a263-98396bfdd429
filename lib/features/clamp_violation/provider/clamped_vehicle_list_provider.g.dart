// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'clamped_vehicle_list_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$clampedVehicleListNotifierHash() =>
    r'764de7369559822b5f1a09439525967c7fc0bb9f';

/// See also [ClampedVehicleListNotifier].
@ProviderFor(ClampedVehicleListNotifier)
final clampedVehicleListNotifierProvider = AutoDisposeNotifierProvider<
    ClampedVehicleListNotifier, ClampedVehicleListState>.internal(
  ClampedVehicleListNotifier.new,
  name: r'clampedVehicleListNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$clampedVehicleListNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ClampedVehicleListNotifier
    = AutoDisposeNotifier<ClampedVehicleListState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
