import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/skeleton_vehicle_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SkeletonCurrentParked extends StatelessWidget {
  const SkeletonCurrentParked({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: ListView.separated(
        shrinkWrap: true,
        physics: const PageScrollPhysics(),
        itemBuilder: (context, index) {
          return SkeletonVehicleCard();
        },
        separatorBuilder: (context, index) => SizedBox(height: 24.h),
        itemCount: 5,
      ),
    );
  }
}
