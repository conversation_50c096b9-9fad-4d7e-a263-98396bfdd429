import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class VehicleInfoChip extends StatelessWidget {
  final String title;
  final bool shared;
  const VehicleInfoChip({required this.title, this.shared = false, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: shared ? ColorConstants.colorEAFFEC : null,
        borderRadius: BorderRadius.circular(20.r),
        border: shared
            ? null
            : Border.all(
                width: 0.5,
                color: ColorConstants.colorE1DDD2,
              ),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 10.w,
        vertical: 4.h,
      ),
      child: Text(
        title,
        style: shared ? TextStyles.ts12w600c32993E : TextStyles.ts12w600c94684E,
      ),
    );
  }
}
