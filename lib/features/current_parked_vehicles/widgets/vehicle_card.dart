import 'package:albalad_operator_app/features/current_parked_vehicles/models/parked_vehicle.dart';
import 'package:albalad_operator_app/features/current_parked_vehicles/provider/current_parked_providers.dart';
import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/vehicle_info_chip.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/widgets/vehicle_parking_timer.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class VehicleCard extends ConsumerWidget {
  final ParkedVehicle parkedVehicle;
  const VehicleCard({required this.parkedVehicle, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return InkWell(
      onTap: () {
        final args = VehicleDetailsScreen(
          uid: parkedVehicle.uid,
        );
        Navigator.pushNamed(
          context,
          VehicleDetailsScreen.route,
          arguments: args,
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              offset: const Offset(0, 0),
              blurRadius: 20,
              spreadRadius: 0,
            ),
          ],
        ),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (parkedVehicle.vehicleId != null)
                        Text.rich(
                          TextSpan(
                            text: tr(context, 'id'),
                            style: TextStyles.ts12w400c4D4D4D,
                            children: [
                              TextSpan(
                                text: ' ${parkedVehicle.vehicleId}',
                                style: TextStyles.ts12w700c353535,
                              ),
                            ],
                          ),
                        ),
                      Gap(10.h),
                      Row(
                        children: [
                          Container(
                            height: 35.h,
                            width: 35.w,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                              color: ColorConstants.colorF1EFE9,
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8.r),
                              child: CachedNetworkImage(
                                imageUrl: parkedVehicle
                                        .vehicleImage?.firstOrNull?.image ??
                                    'https://',
                                memCacheWidth: 200,
                                fit: BoxFit.fill,
                                errorWidget: (context, url, error) =>
                                    Image.asset('car'.asImagePng()),
                              ),
                            ),
                          ),
                          Gap(10.w),
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  parkedVehicle.number ?? '',
                                  style: TextStyles.ts14w600c181818,
                                ),
                                Text(
                                  parkedVehicle.vehicleName ?? '',
                                  style: TextStyles.ts10w400cA1A09B,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Gap(16.h),
                      Wrap(
                        spacing: 8.w,
                        runSpacing: 8.h,
                        children: [
                          if (parkedVehicle.makeYear != null)
                            VehicleInfoChip(
                              title: '${parkedVehicle.makeYear}',
                            ),
                          if (parkedVehicle.vehicleType != null)
                            VehicleInfoChip(
                              title: parkedVehicle.vehicleType ?? '',
                            ),
                          if (parkedVehicle.isSharedVehicle == true)
                            VehicleInfoChip(
                              title: tr(context, 'shared'),
                              shared: true,
                            ),
                          if (parkedVehicle.numberPlateType != null)
                            VehicleInfoChip(
                              title: parkedVehicle.numberPlateType ?? '',
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
                if (parkedVehicle.coorparateVehicle != null)
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20.r),
                      color: ColorConstants.colorEAFFEC,
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: 10.w,
                      vertical: 4.h,
                    ),
                    child: Text(
                      parkedVehicle.coorparateVehicle ?? '',
                      style: TextStyles.ts12w500c32993E,
                    ),
                  )
                else
                  VehicleParkingTimer(
                    totalTimeInSeconds: parkedVehicle.totalHrTime?.toInt() ?? 0,
                    remainingTimeInSeconds:
                        parkedVehicle.remainingSeconds?.toInt() ?? 0,
                    onTimerEnd: () {
                      final notifier =
                          ref.read(parkedVehicleNotifierProvider.notifier);
                      notifier.removeVehicle(parkedVehicle.uid ?? '');
                    },
                  ),
              ],
            ),
            Gap(16.h),
            Divider(
              color: ColorConstants.colorF1F1F1,
              thickness: 1.h,
              height: 0,
            ),
            Gap(10.h),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tr(context, 'checkInTime'),
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                    Text(
                      parkedVehicle.checkinTime ?? '-',
                      style: TextStyles.ts12w700c353535,
                    )
                  ],
                ),
                Gap(20.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tr(context, 'totalHrs'),
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                    Text(
                      (parkedVehicle.totalHrTime?.toInt() ?? 0) == 0
                          ? '-'
                          : convertSecondsToTime(
                              parkedVehicle.totalHrTime?.toInt() ?? 0),
                      style: TextStyles.ts12w700c353535,
                    )
                  ],
                ),
                if (parkedVehicle.violationStatus != null)
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      mainAxisSize: MainAxisSize.min,
                      spacing: 3.w,
                      children: [
                        SvgPicture.asset(
                          'danger-bold'.asIconSvg(),
                          height: 14.h,
                          width: 14.w,
                        ),
                        Flexible(
                          child: Text(
                            parkedVehicle.violationStatus ?? '',
                            style: TextStyles.ts12w400c4D4D4D,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
