import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:albalad_operator_app/features/current_parked_vehicles/models/parked_vehicle.dart';
import 'package:albalad_operator_app/features/current_parked_vehicles/provider/parked_vehicle_state.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/services/vehicle_services.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'current_parked_providers.g.dart';

@riverpod
class ParkedVehicleNotifier extends _$ParkedVehicleNotifier {
  @override
  ParkedVehicleState build() {
    return ParkedVehicleState(
      items: [],
      isLoading: false,
      hasMore: true,
      error: null,
      isConnectionError: false,
    );
  }

  int _currentPage = 1;

  Timer? _debounce;

  void searchVehicle({String query = ''}) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    _debounce = Timer(const Duration(milliseconds: 300), () {
      _currentPage = 1;
      state = state.copyWith(items: [], isLoading: false, hasMore: true);
      getParkedVehicles(query: query);
    });
  }

  Future<void> getParkedVehicles({String query = ''}) async {
    if (state.isLoading || !state.hasMore) return;
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await VehicleServices().fetchParkedVehicles(
        page: _currentPage,
        query: query,
      );
      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          final List<dynamic> vehicles = data['records'];
          final List<ParkedVehicle> parkedVehicles =
              vehicles.map((e) => ParkedVehicle.fromJson(e)).toList();
          state = state.copyWith(
            items: [...state.items, ...parkedVehicles],
            isLoading: false,
            hasMore: data['pagination']['has_next'] == true,
          );
          _currentPage++;
        }
      }
    } on SocketException catch (e) {
      if (kDebugMode) log(e.toString());
      state = state.copyWith(
        isLoading: false,
        hasMore: false,
        error: appLocalization.translate('networkError'),
        isConnectionError: true,
      );
    } catch (e) {
      bool isConnectionError = false;
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        isConnectionError = true;
      }
      state = state.copyWith(
        isLoading: false,
        hasMore: false,
        error: e.toString(),
        isConnectionError: isConnectionError,
      );
    }
  }

  removeVehicle(String vehicleUid) {
    state = state.copyWith(
      items: state.items.where((element) => element.uid != vehicleUid).toList(),
    );
  }

  void reset() {
    _currentPage = 1;
    state = ParkedVehicleState(
      items: [],
      isLoading: false,
      hasMore: true,
      isConnectionError: false,
    );
  }
}
