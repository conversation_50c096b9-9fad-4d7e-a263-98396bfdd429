// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'current_parked_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$parkedVehicleNotifierHash() =>
    r'a34cdc9bd262b9a3527ec3d1646203b08669125d';

/// See also [ParkedVehicleNotifier].
@ProviderFor(ParkedVehicleNotifier)
final parkedVehicleNotifierProvider = AutoDisposeNotifierProvider<
    ParkedVehicleNotifier, ParkedVehicleState>.internal(
  ParkedVehicleNotifier.new,
  name: r'parkedVehicleNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$parkedVehicleNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ParkedVehicleNotifier = AutoDisposeNotifier<ParkedVehicleState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
