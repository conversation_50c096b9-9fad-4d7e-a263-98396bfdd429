import 'package:albalad_operator_app/features/current_parked_vehicles/models/parked_vehicle.dart';

class ParkedVehicleState {
  final List<ParkedVehicle> items;
  final bool isLoading;
  final bool hasMore;
  final bool isConnectionError;
  final String? error;

  ParkedVehicleState({
    required this.items,
    required this.isLoading,
    required this.hasMore,
    required this.isConnectionError,
    this.error,
  });

  ParkedVehicleState copyWith({
    List<ParkedVehicle>? items,
    bool? isLoading,
    bool? hasMore,
    bool? isConnectionError,
    String? error,
  }) {
    return ParkedVehicleState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      isConnectionError: isConnectionError ?? this.isConnectionError,
      error: error ?? this.error,
    );
  }
}
