import 'package:albalad_operator_app/shared/models/vehicle_image.dart';

class ParkedVehicle {
  String? uid;
  String? vehicleId;
  String? vehicleName;
  int? makeYear;
  String? vehicleType;
  String? numberPlateType;
  String? vehicleOwnership;
  bool? isSharedVehicle;
  List<VehicleImage>? vehicleImage;
  String? number;
  String? checkinTime;
  double? totalHrTime;
  String? checkOutTime;
  double? remainingSeconds;
  OwnerDetails? ownerDetails;
  String? violationStatus;
  String? coorparateVehicle;

  ParkedVehicle({
    this.uid,
    this.vehicleId,
    this.vehicleName,
    this.vehicleType,
    this.makeYear,
    this.numberPlateType,
    this.vehicleOwnership,
    this.isSharedVehicle,
    this.vehicleImage,
    this.number,
    this.checkinTime,
    this.totalHrTime,
    this.checkOutTime,
    this.remainingSeconds,
    this.ownerDetails,
    this.violationStatus,
    this.coorparateVehicle,
  });

  ParkedVehicle.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    vehicleId = json['vehicle_id'];
    vehicleName = json['vehicle_name'];
    vehicleType = json['vehicle_type'];
    numberPlateType = json['number_plate_type'];
    makeYear = int.tryParse(json['make_year'].toString());
    vehicleOwnership = json['vehicle_ownership'];
    isSharedVehicle = json['is_shared_vehicle'];
    if (json['vehicle_image'] != null) {
      vehicleImage = <VehicleImage>[];
      json['vehicle_image'].forEach((v) {
        vehicleImage!.add(VehicleImage.fromJson(v));
      });
    }
    number = json['number'];
    checkinTime = json['checkin_time'];
    totalHrTime = double.tryParse(json['total_hr_time'].toString());
    checkOutTime = json['check_out_time'];
    remainingSeconds = double.tryParse(json['remaining_seconds'].toString());
    ownerDetails = json['owner_details'] != null
        ? OwnerDetails.fromJson(json['owner_details'])
        : null;
    violationStatus = json['violation_status'];
    coorparateVehicle = json['coorparate_vehicle'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    if (vehicleImage != null) {
      data['vehicle_image'] = vehicleImage!.map((v) => v.toJson()).toList();
    }
    data['number'] = number;
    data['checkin_time'] = checkinTime;
    data['total_hr_time'] = totalHrTime;
    data['check_out_time'] = checkOutTime;
    data['remaining_seconds'] = remainingSeconds;
    if (ownerDetails != null) {
      data['owner_details'] = ownerDetails!.toJson();
    }
    data['violation_status'] = violationStatus;
    data['coorparate_vehicle'] = coorparateVehicle;
    return data;
  }
}

class OwnerDetails {
  String? name;
  String? phonenumber;

  OwnerDetails({this.name, this.phonenumber});

  OwnerDetails.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    phonenumber = json['phonenumber'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['name'] = name;
    data['phonenumber'] = phonenumber;
    return data;
  }
}
