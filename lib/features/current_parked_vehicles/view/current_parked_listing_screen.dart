import 'package:albalad_operator_app/features/current_parked_vehicles/provider/current_parked_providers.dart';
import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/skeleton_current_parked.dart';
import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/vehicle_card.dart';
import 'package:albalad_operator_app/features/home/<USER>/home_search_field.dart';
import 'package:albalad_operator_app/features/number_plate_scanner/view/number_plate_scanner.dart';
import 'package:albalad_operator_app/features/search/provider/search_provider.dart';
import 'package:albalad_operator_app/features/search/view/search_screen.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/bottom_add_vehicle_button.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:albalad_operator_app/shared/widgets/no_vehicle_widget.dart';
import 'package:albalad_operator_app/shared/widgets/smart_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class CurrentParkedListingScreen extends ConsumerWidget {
  static const route = '/current_parked_listing';
  const CurrentParkedListingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifier = ref.read(parkedVehicleNotifierProvider.notifier);
    final state = ref.watch(parkedVehicleNotifierProvider);

    // Trigger the API call when the screen is first built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (state.items.isEmpty) {
        notifier.getParkedVehicles();
      }
    });
    return SmartScaffold(
      isInternetAvailable: !state.isConnectionError,
      retryConnection: () {
        notifier.reset();
        notifier.getParkedVehicles();
      },
      appBar: InnerAppBar(
        title: Text(tr(context, 'currentParkedVehicles')),
      ),
      body: NotificationListener<ScrollNotification>(
        onNotification: (scrollNotification) {
          if (scrollNotification is ScrollEndNotification &&
              scrollNotification.metrics.extentAfter == 0) {
            notifier.getParkedVehicles();
          }
          return false;
        },
        child: RefreshIndicator(
          onRefresh: () async {
            notifier.reset();
            return notifier.getParkedVehicles();
          },
          child: ListView(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            children: [
              Gap(30.h),
              HomeSearchField(
                hintText: tr(context, 'searchVehicleNumber'),
                readOnly: true,
                onPress: () {
                  final notifier = ref.read(searchNotifierProvider.notifier);
                  notifier.reset();
                  final arguments = SearchScreen(previousRoute: route);
                  Navigator.pushNamed(
                    context,
                    SearchScreen.route,
                    arguments: arguments,
                  );
                },
                onTapSuffix: () {
                  final arguments = NumberPlateScanner(previousRoute: route);
                  Navigator.of(context).pushNamed(
                    NumberPlateScanner.route,
                    arguments: arguments,
                  );
                },
              ),
              Gap(25.h),
              if (state.items.isEmpty && !state.hasMore) ...[
                Gap(0.1.sh),
                const NoVehiclesWidget(),
              ] else if (state.items.isEmpty && state.isLoading) ...[
                const SkeletonCurrentParked(),
              ] else
                ListView.separated(
                  shrinkWrap: true,
                  physics: const PageScrollPhysics(),
                  itemBuilder: (context, index) {
                    if (index == state.items.length) {
                      return const Center(child: CustomGradientSpinner());
                    }
                    final item = state.items[index];
                    return VehicleCard(parkedVehicle: item);
                  },
                  separatorBuilder: (context, index) => SizedBox(height: 24.h),
                  itemCount: state.items.length + (state.hasMore ? 1 : 0),
                ),
              Gap(25.h),
            ],
          ),
        ),
      ),
      bottomNavigationBar: BottomAddVehicleButton(),
    );
  }
}
