import 'package:albalad_operator_app/features/clamp_violation/view/valet_qr_code_scanner_screen.dart';
import 'package:albalad_operator_app/features/home/<USER>/home_search_field.dart';
import 'package:albalad_operator_app/features/valet/providers/current_valet_vehicles_provider.dart';
import 'package:albalad_operator_app/features/valet/widgets/current_valet_vehicle_card.dart';
import 'package:albalad_operator_app/features/valet/widgets/skeleton_current_valet_vehicle_card.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:albalad_operator_app/shared/widgets/no_valet_widget.dart';
import 'package:albalad_operator_app/shared/widgets/smart_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CurrentValetVehiclesScreen extends ConsumerWidget {
  static const route = '/current-valet-vehicles';
  const CurrentValetVehiclesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(currentValetVehiclesNotifierProvider);
    final notifier = ref.read(currentValetVehiclesNotifierProvider.notifier);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (state.items.isEmpty) {
        notifier.fetchCurrentValetVehicles();
      }
    });

    return SmartScaffold(
      isInternetAvailable: state.isConnectionError == false,
      retryConnection: () {
        notifier.reset();
        notifier.fetchCurrentValetVehicles();
      },
      appBar: InnerAppBar(
        title: Text(tr(context, 'currentValetVehicles')),
      ),
      body: NotificationListener<ScrollNotification>(
        onNotification: (scrollNotification) {
          if (scrollNotification is ScrollEndNotification &&
              scrollNotification.metrics.extentAfter == 0 &&
              state.isLoading == false) {
            notifier.fetchCurrentValetVehicles();
          }
          return false;
        },
        child: RefreshIndicator(
          onRefresh: () async {
            notifier.reset();
            return notifier.fetchCurrentValetVehicles();
          },
          child: ListView(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
            children: [
              HomeSearchField(
                hintText: tr(context, 'searchVehicleNumber'),
                onChanged: (p0) => notifier.searchValetVehicle(query: p0),
                onTapSuffix: () {
                  Navigator.push(context, MaterialPageRoute(builder: (context) {
                    return const ValetQrCodeScannerScreen();
                  }));
                },
              ),
              Gap(24.h),
              Text(
                tr(context, 'valet_vehicles_list'),
                style: TextStyles.ts18w600c353535,
              ),
              Gap(8.h),
              if (state.items.isEmpty && !state.hasMore) ...[
                Gap(0.1.sh),
                const NoValetWidget(),
              ] else if (state.items.isEmpty && state.isLoading) ...[
                Skeletonizer(
                  enabled: true,
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: const PageScrollPhysics(),
                    itemBuilder: (context, index) =>
                        SkeletonCurrentValetVehicleCard(),
                    separatorBuilder: (context, index) =>
                        SizedBox(height: 24.h),
                    itemCount: 5,
                  ),
                ),
              ] else
                ListView.separated(
                  shrinkWrap: true,
                  physics: const PageScrollPhysics(),
                  itemBuilder: (context, index) {
                    if (index == state.items.length) {
                      return const Center(child: CustomGradientSpinner());
                    }
                    final item = state.items[index];
                    return CurrentValetVehicleCard(
                      valetVehicle: item,
                      previousRoute: route,
                    );
                  },
                  separatorBuilder: (context, index) => SizedBox(height: 24.h),
                  itemCount: state.items.length + (state.hasMore ? 1 : 0),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
