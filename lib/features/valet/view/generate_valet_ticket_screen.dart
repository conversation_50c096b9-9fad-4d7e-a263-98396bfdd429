import 'dart:io';

import 'package:albalad_operator_app/features/success_screen.dart';
import 'package:albalad_operator_app/features/valet/models/generate_valet_ticket_model.dart';
import 'package:albalad_operator_app/features/valet/providers/current_valet_vehicles_provider.dart';
import 'package:albalad_operator_app/features/valet/providers/enter_vehicle_provider.dart';
import 'package:albalad_operator_app/features/valet/providers/providers.dart';
import 'package:albalad_operator_app/features/valet/providers/request_vehicle_provider.dart';
import 'package:albalad_operator_app/features/valet/providers/valet_request_list_provider.dart';
import 'package:albalad_operator_app/features/valet/view/confirm_vehicle_exit_sheet.dart';
import 'package:albalad_operator_app/features/valet/view/current_valet_vehicles_screen.dart';
import 'package:albalad_operator_app/features/valet/view/enable_bluetooth_dialog.dart';
import 'package:albalad_operator_app/features/valet/view/settle_valet_ticket_sheet.dart';
import 'package:albalad_operator_app/features/valet/view/valet_operations_screen.dart';
import 'package:albalad_operator_app/features/valet/widgets/skeleton_ticket_details_card.dart';
import 'package:albalad_operator_app/features/valet/widgets/ticket_details_card.dart';
import 'package:albalad_operator_app/features/vehicle_details/provider/vehicle_details_provider.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/custom_searchable_dropdown.dart';
import 'package:albalad_operator_app/shared/widgets/elevated_secondary_button.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:albalad_operator_app/shared/widgets/no_network_widget.dart';
import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:print_bluetooth_thermal/print_bluetooth_thermal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:skeletonizer/skeletonizer.dart';

class GenerateValetTicketScreen extends ConsumerWidget {
  static const String route = '/generate-valet-ticket';
  final String previousRoute;
  final String? vehicleUid;
  final String valetUid;
  const GenerateValetTicketScreen({
    required this.previousRoute,
    required this.valetUid,
    this.vehicleUid,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    double bottom = MediaQuery.viewPaddingOf(context).bottom;
    if (Platform.isAndroid) {
      bottom = 30.h;
    }

    final generateTicket = ref.watch(
      generateValetTicketProvider(valetUid: valetUid),
    );

    return Scaffold(
      appBar: InnerAppBar(
        title: Text(tr(context, 'ticket_details')),
      ),
      body: generateTicket.when(
        data: (data) {
          return Padding(
            padding: EdgeInsets.fromLTRB(16.w, 30.h, 16.w, bottom),
            child: Column(
              children: [
                TicketDetailsCard(
                  valetUid: valetUid,
                  ticket: data,
                ),
                if (data.isEntered == true)
                  Center(
                    child: TextButton.icon(
                      onPressed: () => printTicket(context, data),
                      icon: SvgPicture.asset('receipt'.asIconSvg()),
                      label: Text(tr(context, 'print_ticket')),
                      style: TextButton.styleFrom(
                        textStyle: TextStyles.ts12w800c4D4D4D,
                      ),
                    ),
                  ),
                const Spacer(),
                if (data.isEntered == false && data.isSettled == false) ...[
                  ElevatedButton(
                    onPressed: () => printTicket(context, data),
                    child: Text(tr(context, 'print_ticket')),
                  ),
                  Gap(10.h),
                  EnterVehicleButton(
                    valetUid: data.valetUid ?? '',
                    previousRoute: previousRoute,
                    vehicleUid: vehicleUid,
                  ),
                ] else ...[
                  Row(
                    children: [
                      if (data.isSettled == false) ...[
                        Expanded(
                          child: ElevatedSecondaryButton(
                            title: tr(context, 'settle_ticket'),
                            onPressed: () async {
                              final isSettled = await showModalBottomSheet(
                                context: context,
                                isScrollControlled: true,
                                builder: (context) => SettleValetTicketSheet(
                                  valetUid: data.valetUid ?? '',
                                  previousRoute: previousRoute,
                                  vehicleUid: vehicleUid,
                                  valetRequestUid: valetUid,
                                ),
                              );
                              if (isSettled == true) {
                                data.isSettled = true;
                                if (!context.mounted) return;
                                showConfirmVehicleExitSheet(
                                  context,
                                  valetUid: data.valetUid ?? '',
                                );
                              }
                            },
                          ),
                        ),
                        if (data.assignedToMe == false) Gap(10.w),
                      ],
                      if (data.assignedToMe == false)
                        Expanded(
                          child: RequestVehicleButton(
                            valetUid: data.valetUid ?? '',
                          ),
                        ),
                    ],
                  ),
                  if (data.isExited != true) ...[
                    Gap(20.h),
                    ElevatedButton(
                      onPressed: data.isSettled == false
                          ? null
                          : () => showConfirmVehicleExitSheet(context,
                              valetUid: data.valetUid ?? ''),
                      child: Text(tr(context, 'exit_vehicle')),
                    )
                  ],
                ]
              ],
            ),
          );
        },
        error: (error, stackTrace) {
          if (error.toString().contains('connection error') ||
              error.toString().contains('connection timeout')) {
            return Center(
              child: NoNetworkWidget(
                onRefresh: () => ref.invalidate(generateValetTicketProvider),
              ),
            );
          }
          return const SizedBox();
        },
        loading: () => const SkeletonGenerateTicket(),
      ),
    );
  }

  showConfirmVehicleExitSheet(BuildContext context,
      {required String valetUid}) {
    showModalBottomSheet(
      context: context,
      builder: (context) => ConfirmVehicleExitSheet(
        valetUid: valetUid,
        previousRoute: previousRoute,
        vehicleId: vehicleUid,
      ),
    );
  }

  Future<void> printTicket(
    BuildContext context,
    GenerateValetTicketModel ticket,
  ) async {
    final bool result = await PrintBluetoothThermal.bluetoothEnabled;
    if (!result) {
      if (!context.mounted) return;
      return showModalBottomSheet(
        context: context,
        builder: (context) => const EnableBluetoothDialog(),
      );
    }

    EasyLoading.show();
    final List<BluetoothInfo> listResult =
        await PrintBluetoothThermal.pairedBluetooths;
    EasyLoading.dismiss();
    List<BluetoothInfo> devices =
        listResult.where((e) => e.name.startsWith('SPP')).toList();
    if (devices.isNotEmpty) {
      if (!context.mounted) return;
      final device = await showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        showDragHandle: true,
        useSafeArea: true,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(26.r)),
        ),
        builder: (BuildContext context) => CustomSearchableSheet(
          items: devices,
          displayBuilder: (p0) {
            return p0.name;
          },
          title: 'Select Printer',
          searchHintText: 'Search for printers',
        ),
      );
      if (device != null) {
        String mac = device.macAdress;
        if (!context.mounted) return;
        final isConnected = await connectPrinter(context: context, mac: mac);
        if (isConnected) {
          bool conecctionStatus = await PrintBluetoothThermal.connectionStatus;
          if (conecctionStatus) {
            List<int> ticketData = await getTicketDetails(ticket);
            final result = await PrintBluetoothThermal.writeBytes(ticketData);
            if (kDebugMode) {
              print("print result: $result");
            }
          } else {
            if (!context.mounted) return;
            _showPrinterErrorDialog(context, isConnectionIssue: false);
          }
        }
      }
    } else {
      if (!context.mounted) return;
      _showPrinterErrorDialog(context, isConnectionIssue: true);
    }
  }

  Future<bool> connectPrinter(
      {required BuildContext context, required String mac}) async {
    EasyLoading.show();
    bool result = await PrintBluetoothThermal.connect(
      macPrinterAddress: mac,
    );
    EasyLoading.dismiss();
    if (!result) {
      if (!context.mounted) return result;
      _showPrinterErrorDialog(context, isConnectionIssue: false);
    }
    return result;
  }

  Future<List<int>> getTicketDetails(GenerateValetTicketModel ticket) async {
    List<int> bytes = [];
    // Using default profile
    final profile = await CapabilityProfile.load();
    final generator = Generator(PaperSize.mm58, profile);
    bytes += generator.reset();

    String qrData = '${ApiConstants.baseURL}/app?action=valet&uid=$valetUid';

    //QR code
    bytes += generator.qrcode(qrData);

    bytes += generator.row([
      PosColumn(
        text: 'Number Plate\n${ticket.numberPlate}',
        width: 6,
        styles: PosStyles(fontType: PosFontType.fontB),
      ),
      PosColumn(
        text: 'Date\n${ticket.bookingDate}',
        width: 6,
        styles: PosStyles(fontType: PosFontType.fontB),
      ),
    ]);

    bytes += generator.row([
      PosColumn(
        text: 'Location\n${ticket.location}',
        width: 6,
        styles: PosStyles(fontType: PosFontType.fontB),
      ),
      PosColumn(
        text: 'Time\n${ticket.bookingTime}',
        width: 6,
        styles: PosStyles(fontType: PosFontType.fontB),
      ),
    ]);

    bytes += generator.text(
      'Valet Person\n${ticket.valetPerson}',
      styles: PosStyles(fontType: PosFontType.fontB),
    );

    bytes += generator.text(
      'Amount ${ticket.actualAmount}',
      styles: PosStyles(fontType: PosFontType.fontB),
    );
    bytes += generator.text(
      'VAT ${ticket.vatAmount}',
      styles: PosStyles(fontType: PosFontType.fontB),
    );
    bytes += generator.text(
      'Total Amount ${ticket.totalAmount}',
      styles: PosStyles(fontType: PosFontType.fontB),
    );

    bytes += generator.feed(2);

    return bytes;
  }

  // Future<void> printTicket(
  //     BuildContext context, GenerateValetTicketModel ticket) async {
  // final result = await requestBluetoothPermissions();
  // debugPrint(result.toString());
  // if (result) {
  //   final pairedDevices = await getPairedDevices();
  //   debugPrint(pairedDevices.toString());
  //   if (pairedDevices.isNotEmpty) {
  //     if (!context.mounted) return;
  //     showModalBottomSheet(
  //       context: context,
  //       isScrollControlled: true,
  //       showDragHandle: true,
  //       useSafeArea: true,
  //       shape: RoundedRectangleBorder(
  //         borderRadius: BorderRadius.vertical(top: Radius.circular(26.r)),
  //       ),
  //       builder: (BuildContext context) => CustomSearchableSheet(
  //         items: pairedDevices,
  //         displayBuilder: (p0) {
  //           List<String> deviceInfo = p0.toString().split('(');
  //           String logicalName = deviceInfo[0].split("_")[0].trim();
  //           return logicalName;
  //         },
  //         title: 'Select Printer',
  //         searchHintText: 'Search for printers',
  //         onChanged: (value) {
  //           if (value != null) {
  //             printWithQR(value, ticket, context);
  //           }
  //         },
  //       ),
  //     );
  //   } else {
  //     if (!context.mounted) return;
  //     _showPrinterErrorDialog(context, isConnectionIssue: true);
  //   }
  // }
  // }

  // Future<bool> requestBluetoothPermissions() async {
  //   final btprinterPlugin = Btprinter();
  //   String? result;
  //   try {
  //     result = await btprinterPlugin.getBtPermission();
  //   } on PlatformException catch (e) {
  //     result = 'Error: ${e.message}';
  //   }
  //   debugPrint(result); // Prints "success" if permissions are granted
  //   return result == 'success';
  // }

  // Future<List<String?>> getPairedDevices() async {
  //   final btprinterPlugin = Btprinter();
  //   List<String?> devices = [];
  //   try {
  //     List<Object?> result = await btprinterPlugin.getPairedDevices();
  //     devices = result
  //         .cast<String>()
  //         .where((device) => device.startsWith('SPP'))
  //         .toList();
  //   } on PlatformException catch (e) {
  //     debugPrint('Error: ${e.message}');
  //   }
  //   debugPrint(devices.toString()); // Prints the list of paired devices
  //   return devices;
  // }

  // Future<void> printWithQR(String device, GenerateValetTicketModel ticket,
  //     BuildContext context) async {
  //   final btprinterPlugin = Btprinter();
  //   // Parse device info (e.g., "SPP-R310_123 (00:11:22:33:44:55)")
  //   List<String> deviceInfo = device.split('(');
  //   String logicalName = deviceInfo[0].split("_")[0].trim(); // e.g., "SPP-R310"
  //   String address =
  //       deviceInfo[1].replaceAll(')', '').trim(); // e.g., "00:11:22:33:44:55"

  //   // Define text requests
  //   List<Map<String, dynamic>> textRequests = [
  //     // Add spacing after QR code (no title in the image, so we skip the "Print Title Test")
  //     {
  //       'text': '\n', // Extra line feed to space out after QR code
  //       'textAlignment': Btprinter.ALIGNMENT_LEFT,
  //       'textAttribute': Btprinter.ATTRIBUTE_NORMAL,
  //       'textSize': 1,
  //     },

  //     {
  //       'text': '${_formatRowWithMixedAlignment(
  //         leftText: 'Number Plate: ${ticket.numberPlate}',
  //         rightText: 'Date: ${ticket.bookingDate}',
  //       )}\n',
  //       'textAlignment': Btprinter
  //           .ALIGNMENT_LEFT, // Base alignment (we'll handle right alignment manually)
  //       'textAttribute': Btprinter.ATTRIBUTE_NORMAL,
  //       'textSize': 1,
  //     },

  //     // Location
  //     {
  //       'text': '${_formatRowWithMixedAlignment(
  //         leftText: 'Location: ${ticket.location}',
  //         rightText: 'Time: ${ticket.bookingTime}',
  //       )}\n',
  //       'textAlignment': Btprinter.ALIGNMENT_LEFT,
  //       'textAttribute': Btprinter.ATTRIBUTE_NORMAL,
  //       'textSize': 1,
  //     },
  //     // Valet Person
  //     {
  //       'text': 'Valet Person: ${ticket.valetPerson}\n',
  //       'textAlignment': Btprinter.ALIGNMENT_LEFT,
  //       'textAttribute': Btprinter.ATTRIBUTE_NORMAL,
  //       'textSize': 1,
  //     },
  //     // Amount
  //     {
  //       'text':
  //           'Amount: ${ticket.actualAmount}\n', // Extra \n for spacing at the end
  //       'textAlignment': Btprinter.ALIGNMENT_LEFT,
  //       'textAttribute': Btprinter.ATTRIBUTE_NORMAL,
  //       'textSize': 1,
  //     },
  //     // Vat Amount
  //     {
  //       'text':
  //           'Vat Amount: ${ticket.vatAmount}\n', // Extra \n for spacing at the end
  //       'textAlignment': Btprinter.ALIGNMENT_LEFT,
  //       'textAttribute': Btprinter.ATTRIBUTE_NORMAL,
  //       'textSize': 1,
  //     },
  //     // Total Amount
  //     {
  //       'text':
  //           'Total Amount:${ticket.totalAmount}\n\n', // Extra \n for spacing at the end
  //       'textAlignment': Btprinter.ALIGNMENT_LEFT,
  //       'textAttribute': Btprinter.ATTRIBUTE_NORMAL,
  //       'textSize': 1,
  //     },
  //   ];

  //   String qrData = '${ApiConstants.baseURL}/app?action=valet&uid=$valetUid';
  //   // Define QR code request
  //   List<Map<String, dynamic>> qrCodeRequests = [
  //     {
  //       'data': qrData, // QR code content (e.g., URL or text)
  //       'symbology':
  //           Btprinter.BARCODE_TYPE_QRCODE, // QR code type (assumed constant)
  //       'width': 5, // QR module size (adjust for readability, typically 1-10)
  //       'height': 5, // Often same as width for QR codes
  //       'alignment': Btprinter.ALIGNMENT_CENTER, // Center the QR code
  //     },
  //   ];

  //   String? result;
  //   try {
  //     // Print text first

  //     // Then print QR code
  //     result = await btprinterPlugin.printBarcode(
  //         qrCodeRequests, logicalName, address);
  //     if (result == "Success") {
  //       debugPrint("QR code printed successfully");
  //     } else {
  //       debugPrint("QR code printing failed: $result");
  //     }

  //     result =
  //         await btprinterPlugin.printText(textRequests, logicalName, address);
  //     if (result == "Success") {
  //       debugPrint("Text printed successfully");
  //     } else {
  //       debugPrint("Text printing failed: $result");
  //       return;
  //     }
  //   } on PlatformException catch (e) {
  //     result = 'Error: ${e.message}';
  //     debugPrint(result);
  //     if (!context.mounted) return;
  //     _showPrinterErrorDialog(context, isConnectionIssue: false);
  //   }
  // }

  // Helper method to format a row with left and right aligned text
  // String _formatRowWithMixedAlignment(
  //     {required String leftText, required String rightText}) {
  //   const int printerWidth =
  //       48; // SPP-R310: ~48 characters per line at normal size (80mm, 203 DPI)

  //   // Calculate padding
  //   int totalLength = leftText.length + rightText.length;
  //   int paddingLength = printerWidth - totalLength;

  //   // Ensure padding isn't negative (in case text is too long)
  //   if (paddingLength < 0) {
  //     // Truncate leftText if necessary to fit
  //     leftText =
  //         '${leftText.substring(0, leftText.length + paddingLength - 3)}...';
  //     paddingLength = 0;
  //   }

  //   // Add spaces between left and right text
  //   String padding = ' ' * paddingLength;
  //   return '$leftText$padding$rightText';
  // }

  // // Updated error dialog method to handle different error types
  void _showPrinterErrorDialog(BuildContext context,
      {required bool isConnectionIssue}) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog.adaptive(
        title: Text(
          isConnectionIssue
              ? tr(context,
                  'printer_not_detected_title') // "Let’s Connect Your Printer"
              : tr(context,
                  'printing_issue_title'), // New title for general printing errors
        ),
        content: Text(
          isConnectionIssue
              ? tr(context,
                  'printer_not_detected_description') // "No printer detected..."
              : tr(context, 'printing_issue_description'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(tr(context, 'okay')), // "OK"
          ),
        ],
      ),
    );
  }
}

class RequestVehicleButton extends ConsumerWidget {
  final String valetUid;
  const RequestVehicleButton({required this.valetUid, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(requestVehicleNotifierProvider);
    final notifier = ref.read(requestVehicleNotifierProvider.notifier);

    ref.listen(
      requestVehicleNotifierProvider,
      (previous, next) {
        if (next is RequestVehicleSuccess) {
          SnackBar snackBar = SnackBar(
            content:
                Text(tr(context, 'vehicle_request_submitted_successfully')),
            showCloseIcon: true,
          );
          ScaffoldMessenger.of(context).showSnackBar(snackBar);
        }
      },
    );

    if (state is RequestVehicleLoading) {
      return const Center(child: CustomGradientSpinner());
    }
    return ElevatedButton(
      onPressed: () => notifier.requestVehicle(valetUid: valetUid),
      style: ElevatedButton.styleFrom(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
      ),
      child: Text(tr(context, 'request_vehicle')),
    );
  }
}

class EnterVehicleButton extends ConsumerWidget {
  final String valetUid;
  final String previousRoute;
  final String? vehicleUid;
  const EnterVehicleButton(
      {required this.valetUid,
      required this.previousRoute,
      this.vehicleUid,
      super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(enterVehicleNotifierProvider);
    final notifier = ref.read(enterVehicleNotifierProvider.notifier);

    ref.listen(
      enterVehicleNotifierProvider,
      (previous, next) {
        if (next is EnterVehicleSuccess) {
          if (previousRoute == VehicleDetailsScreen.route) {
            final notifier = ref.read(vehicleDetailsNotifierProvider.notifier);
            notifier.getVehicleDetails(uid: vehicleUid);
          }
          if (previousRoute == CurrentValetVehiclesScreen.route) {
            final notifier =
                ref.read(currentValetVehiclesNotifierProvider.notifier);
            notifier.reset();
            notifier.fetchCurrentValetVehicles();
          }
          if (previousRoute == ValetOperationsScreen.route) {
            ref.invalidate(valetRequestListNotifierProvider);
          }
          final arguments = SuccessScreen(
            title: tr(context, 'successful'),
            message: tr(context, 'enter_vehicle_description'),
            onPressed: (context) {
              Navigator.popUntil(context, ModalRoute.withName(previousRoute));
            },
          );
          Navigator.pushNamed(context, SuccessScreen.route,
              arguments: arguments);
        }
      },
    );

    return state is EnterVehicleLoading
        ? const Center(child: CustomGradientSpinner())
        : ElevatedSecondaryButton(
            onPressed: () => notifier.enterVehicle(valetUid: valetUid),
            title: tr(context, 'enter_vehicle'),
          );
  }
}

class SkeletonGenerateTicket extends StatelessWidget {
  const SkeletonGenerateTicket({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Padding(
        padding: EdgeInsets.fromLTRB(16.w, 30.h, 16.w, 30.h),
        child: Column(
          children: [
            const SkeletonTicketDetailsCard(),
            const Spacer(),
            ElevatedButton(
              onPressed: () {},
              child: Text(tr(context, 'print_ticket')),
            ),
            Gap(10.h),
            ElevatedSecondaryButton(
              title: tr(context, 'enter_vehicle'),
              onPressed: () {},
            )
          ],
        ),
      ),
    );
  }
}
