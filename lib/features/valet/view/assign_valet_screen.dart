import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/vehicle_info_chip.dart';
import 'package:albalad_operator_app/features/success_screen.dart';
import 'package:albalad_operator_app/features/valet/models/valet_location.dart';
import 'package:albalad_operator_app/features/valet/models/valet_operator.dart';
import 'package:albalad_operator_app/features/valet/providers/providers.dart';
import 'package:albalad_operator_app/features/valet/providers/valet_request_list_provider.dart';
import 'package:albalad_operator_app/features/valet/view/generate_valet_ticket_screen.dart';
import 'package:albalad_operator_app/features/valet/view/valet_operations_screen.dart';
import 'package:albalad_operator_app/features/valet/widgets/valet_location_dropdown.dart';
import 'package:albalad_operator_app/features/valet/widgets/valet_person_dropdown.dart';
import 'package:albalad_operator_app/features/vehicle_details/provider/vehicle_details_provider.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/bottom_navigation_bar_elevated_button.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AssignValetScreen extends HookConsumerWidget {
  static const route = '/assign_valet';
  final String? vehicleUid;
  final String? vehicleId;
  final String? vehicleNumber;
  final String? vehicleName;
  final String? vehicleImage;
  final String? makeYear;
  final String? vehicleType;
  final String? numberPlateType;
  final String previousRoute;
  final String? valetRequestUid;
  const AssignValetScreen({
    required this.vehicleUid,
    required this.vehicleId,
    required this.vehicleNumber,
    required this.vehicleName,
    required this.vehicleImage,
    required this.makeYear,
    required this.vehicleType,
    required this.numberPlateType,
    required this.previousRoute,
    this.valetRequestUid,
    super.key,
  });

  onAssignSuccess(BuildContext context, WidgetRef ref, String valetUid) {
    if (previousRoute == VehicleDetailsScreen.route) {
      final notifier = ref.read(vehicleDetailsNotifierProvider.notifier);
      notifier.getVehicleDetails(uid: vehicleUid);
    }
    if (previousRoute == ValetOperationsScreen.route) {
      ref.invalidate(valetRequestListNotifierProvider);
    }
    final arguments = SuccessScreen(
      title: tr(context, 'valet_assigned_successfully'),
      message: tr(context, 'valet_assigned_success_message'),
      buttonName: tr(context, 'generate_ticket'),
      onPressed: (context) {
        final arguments = GenerateValetTicketScreen(
          previousRoute: previousRoute,
          valetUid: valetUid,
          vehicleUid: vehicleUid,
        );
        Navigator.pushReplacementNamed(
          context,
          GenerateValetTicketScreen.route,
          arguments: arguments,
        );
      },
    );
    Navigator.pushReplacementNamed(context, SuccessScreen.route,
        arguments: arguments);
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final selectedValetOperator = useState<ValetOperator?>(null);
    final selectedValetLocation = useState<ValetLocation?>(null);

    ref.listen(
      assignValetNotifierProvider,
      (previous, next) {
        if (next is AssignValetSuccess) {
          onAssignSuccess(context, ref, next.valetUid);
        }
      },
    );

    final state = ref.watch(assignValetNotifierProvider);
    final notifier = ref.read(assignValetNotifierProvider.notifier);

    return Scaffold(
      appBar: InnerAppBar(
        title: Text(tr(context, 'assign_valet')),
      ),
      body: Form(
        key: formKey,
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
          children: [
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    offset: const Offset(0, 0),
                    blurRadius: 20,
                    spreadRadius: 0,
                  ),
                ],
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text.rich(
                    TextSpan(
                      text: tr(context, 'id'),
                      style: TextStyles.ts12w400c4D4D4D,
                      children: [
                        TextSpan(
                          text: ' ${vehicleId ?? ''}',
                          style: TextStyles.ts12w700c353535,
                        ),
                      ],
                    ),
                  ),
                  Gap(12.h),
                  Divider(
                    color: ColorConstants.colorF1F1F1,
                    thickness: 1.h,
                    height: 0,
                  ),
                  Gap(15.h),
                  Row(
                    children: [
                      Container(
                        height: 35.h,
                        width: 35.w,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8.r),
                          color: ColorConstants.colorF1EFE9,
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8.r),
                          child: CachedNetworkImage(
                            imageUrl: vehicleImage ?? 'https://',
                            memCacheWidth: 200,
                            fit: BoxFit.fill,
                            errorWidget: (context, url, error) => Image.asset(
                              'car'.asImagePng(),
                            ),
                          ),
                        ),
                      ),
                      Gap(10.w),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            vehicleNumber ?? '',
                            style: TextStyles.ts14w600c181818,
                          ),
                          Text(
                            vehicleName ?? '',
                            style: TextStyles.ts10w400cA1A09B,
                          ),
                        ],
                      ),
                    ],
                  ),
                  Gap(16.h),
                  Row(
                    spacing: 8.w,
                    children: [
                      if (makeYear != null)
                        VehicleInfoChip(
                          title: '$makeYear',
                        ),
                      if (vehicleType != null)
                        VehicleInfoChip(
                          title: vehicleType ?? '',
                        ),
                      if (numberPlateType != null)
                        VehicleInfoChip(
                          title: numberPlateType ?? '',
                        ),
                    ],
                  ),
                ],
              ),
            ),
            Gap(24.h),
            ValetPersonDropdown(
              onChanged: (p0) => selectedValetOperator.value = p0,
              value: selectedValetOperator.value,
            ),
            Gap(24.h),
            ValetLocationDropdown(
              onChanged: (p0) => selectedValetLocation.value = p0,
              value: selectedValetLocation.value,
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigationBarElevatedButton(
        title: tr(context, 'submit'),
        isLoading: state is AssignValetLoading,
        onPressed: () {
          if (formKey.currentState?.validate() == true) {
            notifier.assignValet(
              vehicleUid: vehicleUid ?? '',
              operatorUid: selectedValetOperator.value?.uid ?? '',
              locationUid: selectedValetLocation.value?.uid ?? '',
              valetRequestUid: valetRequestUid,
            );
          }
        },
      ),
    );
  }
}
