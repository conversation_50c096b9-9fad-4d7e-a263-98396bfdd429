import 'package:albalad_operator_app/features/valet/providers/current_valet_vehicles_provider.dart';
import 'package:albalad_operator_app/features/valet/providers/providers.dart';
import 'package:albalad_operator_app/features/valet/providers/settle_valet_provider.dart';
import 'package:albalad_operator_app/features/valet/view/current_valet_vehicles_screen.dart';
import 'package:albalad_operator_app/features/valet/widgets/valet_settlement_status_dropdown.dart';
import 'package:albalad_operator_app/features/vehicle_details/provider/vehicle_details_provider.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/features/violation/models/payment_method.dart';
import 'package:albalad_operator_app/features/violation/models/settlement_type.dart';
import 'package:albalad_operator_app/features/violation/widgets/payment_method_dropdown.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/helper/dialog_helper.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/custom_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SettleValetTicketSheet extends HookConsumerWidget {
  final String valetUid;
  final String? vehicleUid;
  final String previousRoute;
  final String valetRequestUid;
  const SettleValetTicketSheet({
    required this.previousRoute,
    required this.valetUid,
    required this.valetRequestUid,
    this.vehicleUid,
    super.key,
  });

  onSuccess(BuildContext context, WidgetRef ref) {
    ref.invalidate(generateValetTicketProvider);
    if (previousRoute == VehicleDetailsScreen.route) {
      final notifier = ref.read(vehicleDetailsNotifierProvider.notifier);
      notifier.getVehicleDetails(uid: vehicleUid);
    }
    if (previousRoute == CurrentValetVehiclesScreen.route) {
      final notifier = ref.read(currentValetVehiclesNotifierProvider.notifier);
      notifier.reset();
      notifier.fetchCurrentValetVehicles();
    }
    ref.watch(generateValetTicketProvider(valetUid: valetUid));
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    double bottom = MediaQuery.of(context).viewInsets.bottom;
    double paddingBottom = 30.h;
    if (bottom > 0) {
      paddingBottom = bottom;
    }

    final formKey = useMemoized(() => GlobalKey<FormState>());
    final selectedPaymentMethod = useState<PaymentMethod?>(null);
    final slectedStatus = useState<SettlementType?>(null);
    final noteController = useTextEditingController();

    final state = ref.watch(settleValetNotifierProvider);
    final notifier = ref.read(settleValetNotifierProvider.notifier);

    ref.listen(settleValetNotifierProvider, (previous, next) {
      if (next is SettleValeSuccess) {
        onSuccess(context, ref);
        Navigator.pop(context, true);
      }
      if (next is SettleValeFailure) {
        DialogHelper.showErrorDialog(context: context, message: next.message);
      }
    });

    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 43.h, 16.w, paddingBottom),
      child: SingleChildScrollView(
        child: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                tr(context, 'settle_ticket'),
                style: TextStyles.ts18w600c353535,
              ),
              Gap(10.h),
              ValetSettlementStatusDropdown(
                onChanged: (p0) => slectedStatus.value = p0,
                value: slectedStatus.value,
              ),
              Gap(24.h),
              PaymentMethodDropdown(
                onChanged: (p0) => selectedPaymentMethod.value = p0,
                value: selectedPaymentMethod.value,
              ),
              Gap(24.h),
              CustomTextFormField(
                controller: noteController,
                labelText: tr(context, 'note'),
                hintText: tr(context, 'customer_have_settled_ticket'),
                maxLines: 5,
                // validator: (p0) {
                //   if (p0 == null || p0.isEmpty) {
                //     return tr(context, 'please_enter_note');
                //   }
                //   if (p0.length < 10) {
                //     return context.loc
                //         .settle_violation_note_minimum_chars_validation(10);
                //   }
                //   return null;
                // },
              ),
              Gap(20.h),
              if (state is SettleValeLoading)
                const Center(
                  child: CustomGradientSpinner(),
                )
              else
                ElevatedButton(
                  onPressed: () {
                    if (formKey.currentState?.validate() == true) {
                      notifier.submit(
                        valetUid: valetUid,
                        status: slectedStatus.value?.id ?? '',
                        paymentMethodUid:
                            selectedPaymentMethod.value?.uid ?? '',
                        additionalNote: noteController.text,
                      );
                    }
                  },
                  child: Text(tr(context, 'submit')),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
