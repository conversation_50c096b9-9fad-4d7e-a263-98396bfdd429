import 'package:albalad_operator_app/features/home/<USER>/home_search_field.dart';
import 'package:albalad_operator_app/features/number_plate_scanner/view/number_plate_scanner.dart';
import 'package:albalad_operator_app/features/search/provider/search_provider.dart';
import 'package:albalad_operator_app/features/search/view/search_screen.dart';
import 'package:albalad_operator_app/features/valet/providers/valet_request_list_provider.dart';
import 'package:albalad_operator_app/features/valet/widgets/skeleton_valet_request_card.dart';
import 'package:albalad_operator_app/features/valet/widgets/valet_request_card.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:albalad_operator_app/shared/widgets/no_valet_widget.dart';
import 'package:albalad_operator_app/shared/widgets/smart_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ValetOperationsScreen extends ConsumerWidget {
  static const String route = '/valet_operations_screen';
  const ValetOperationsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifier = ref.watch(valetRequestListNotifierProvider.notifier);
    final state = ref.watch(valetRequestListNotifierProvider);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (state.items.isEmpty) {
        notifier.fetchValetRequestList();
      }
    });

    return SmartScaffold(
      isInternetAvailable: !state.isConnectionError,
      retryConnection: () {
        notifier.reset();
        notifier.fetchValetRequestList();
      },
      appBar: InnerAppBar(
        title: Text(tr(context, 'valet_operations')),
      ),
      body: NotificationListener<ScrollNotification>(
        onNotification: (scrollNotification) {
          if (scrollNotification is ScrollEndNotification &&
              scrollNotification.metrics.extentAfter == 0 &&
              state.isLoading == false) {
            notifier.fetchValetRequestList();
          }
          return false;
        },
        child: RefreshIndicator(
          onRefresh: () {
            notifier.reset();
            return notifier.fetchValetRequestList();
          },
          child: ListView(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
            children: [
              HomeSearchField(
                hintText: tr(context, 'searchVehicleNumber'),
                readOnly: true,
                onPress: () {
                  final notifier = ref.read(searchNotifierProvider.notifier);
                  notifier.reset();
                  final arguments = SearchScreen(previousRoute: route);
                  Navigator.pushNamed(context, SearchScreen.route,
                      arguments: arguments);
                },
                onChanged: (p0) => notifier.searchValetRequests(query: p0),
                onTapSuffix: () {
                  final arguments = NumberPlateScanner(previousRoute: route);
                  Navigator.of(context).pushNamed(
                    NumberPlateScanner.route,
                    arguments: arguments,
                  );
                },
              ),
              Gap(24.h),
              Text(
                tr(context, 'valet_request_list'),
                style: TextStyles.ts18w600c353535,
              ),
              Gap(8.h),
              if (state.items.isEmpty && !state.hasMore) ...[
                Gap(0.1.sh),
                const NoValetWidget(),
              ] else if (state.items.isEmpty && state.isLoading) ...[
                Skeletonizer(
                  enabled: true,
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: const PageScrollPhysics(),
                    itemBuilder: (context, index) => SkeletonValetRequestCard(),
                    separatorBuilder: (context, index) =>
                        SizedBox(height: 24.h),
                    itemCount: 5,
                  ),
                ),
              ] else
                ListView.separated(
                  shrinkWrap: true,
                  physics: const PageScrollPhysics(),
                  itemBuilder: (context, index) {
                    if (index == state.items.length) {
                      return const Center(child: CustomGradientSpinner());
                    }
                    final item = state.items[index];
                    return ValetRequestCard(
                      requestItem: item,
                      previousRoute: route,
                    );
                  },
                  separatorBuilder: (context, index) => SizedBox(height: 24.h),
                  itemCount: state.items.length + (state.hasMore ? 1 : 0),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
