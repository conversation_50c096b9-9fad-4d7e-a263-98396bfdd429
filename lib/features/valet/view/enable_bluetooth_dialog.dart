import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/elevated_secondary_button.dart';
import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';

class EnableBluetoothDialog extends StatelessWidget {
  const EnableBluetoothDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 50.h, 16.w, 30.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            'bluetooth'.asIconSvg(),
            height: 94.17.h,
            width: 94.17.w,
          ),
          Gap(18.h),
          Text(
            tr(context, 'bluetooth_not_enabled'),
            style: TextStyles.ts30w700c44322D,
            textAlign: TextAlign.center,
          ),
          Gap(5.h),
          Text(
            tr(context, 'bluetooth_not_enabled_description'),
            style: TextStyles.ts14w600c959595,
            textAlign: TextAlign.center,
          ),
          Gap(52.h),
          Row(
            spacing: 10.w,
            children: [
              Expanded(
                child: ElevatedSecondaryButton(
                  title: tr(context, 'try_again'),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => AppSettings.openAppSettings(
                    type: AppSettingsType.bluetooth,
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(horizontal: 0),
                  ),
                  child: Text(tr(context, 'enable_bluetooth')),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}
