import 'package:albalad_operator_app/features/success_screen.dart';
import 'package:albalad_operator_app/features/valet/providers/current_valet_vehicles_provider.dart';
import 'package:albalad_operator_app/features/valet/providers/exit_valet_vehicle_provider.dart';
import 'package:albalad_operator_app/features/valet/view/current_valet_vehicles_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/provider/vehicle_details_provider.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/elevated_secondary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ConfirmVehicleExitSheet extends ConsumerWidget {
  final String valetUid;
  final String? vehicleId;
  final String previousRoute;
  const ConfirmVehicleExitSheet({
    required this.valetUid,
    required this.previousRoute,
    this.vehicleId,
    super.key,
  });

  onSuccess(BuildContext context, WidgetRef ref) {
    if (previousRoute == VehicleDetailsScreen.route) {
      final notifier = ref.read(vehicleDetailsNotifierProvider.notifier);
      notifier.getVehicleDetails(uid: vehicleId);
    }
    if (previousRoute == CurrentValetVehiclesScreen.route) {
      final notifier = ref.read(currentValetVehiclesNotifierProvider.notifier);
      notifier.reset();
      notifier.fetchCurrentValetVehicles();
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(exitValetVehicleNotifierProvider);
    final notifier = ref.read(exitValetVehicleNotifierProvider.notifier);

    ref.listen(exitValetVehicleNotifierProvider, (previous, next) {
      if (next is ExitValetVehicleSuccess) {
        onSuccess(context, ref);
        final argumnets = SuccessScreen(
          title: tr(context, 'exit_successful'),
          message: tr(context, 'exit_successful_message'),
          onPressed: (context) => Navigator.popUntil(
            context,
            ModalRoute.withName(previousRoute),
          ),
        );
        Navigator.popAndPushNamed(context, SuccessScreen.route,
            arguments: argumnets);
      }
    });

    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 50.h, 16.w, 30.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SvgPicture.asset(
            'info-circle'.asIconSvg(),
            height: 94.17.h,
            width: 94.17.w,
          ),
          Gap(8.h),
          Text(
            tr(context, 'confirm_vehicle_exit'),
            style: TextStyles.ts30w700c44322D,
            textAlign: TextAlign.center,
          ),
          Gap(6.h),
          Text(
            tr(context, 'confirm_vehicle_exit_description'),
            style: TextStyles.ts14w600c959595,
            textAlign: TextAlign.center,
          ),
          Gap(50.h),
          Row(
            spacing: 10.w,
            children: [
              Expanded(
                child: ElevatedSecondaryButton(
                  title: tr(context, 'cancel'),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
              Expanded(
                child: Builder(builder: (context) {
                  if (state is ExitValetVehicleLoading) {
                    return const Center(
                      child: CustomGradientSpinner(),
                    );
                  }
                  return ElevatedButton(
                    onPressed: () => notifier.exitVehicle(valetUid: valetUid),
                    child: Text(tr(context, 'confirm')),
                  );
                }),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
