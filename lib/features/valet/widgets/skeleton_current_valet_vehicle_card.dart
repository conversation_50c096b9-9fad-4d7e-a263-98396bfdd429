import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/vehicle_info_chip.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SkeletonCurrentValetVehicleCard extends StatelessWidget {
  const SkeletonCurrentValetVehicleCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 0),
              blurRadius: 20,
              spreadRadius: 0,
              color: Colors.black.withValues(alpha: 0.1),
            ),
          ],
        ),
        padding: EdgeInsets.fromLTRB(16.w, 14.h, 16.w, 8.h),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text.rich(
                        TextSpan(
                          text: tr(context, 'id'),
                          style: TextStyles.ts12w400c4D4D4D,
                          children: [
                            TextSpan(
                              text: ' 2586547',
                              style: TextStyles.ts12w700c353535,
                            ),
                          ],
                        ),
                      ),
                      Gap(10.h),
                      Row(
                        children: [
                          Container(
                            height: 35.h,
                            width: 35.w,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                              color: ColorConstants.colorF1EFE9,
                            ),
                            child: Image.asset('car'.asImagePng()),
                          ),
                          Gap(10.w),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '7403 RUA',
                                style: TextStyles.ts14w600c181818,
                              ),
                              Text(
                                'Toyota Camry',
                                style: TextStyles.ts10w400cA1A09B,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Column(
                  children: [
                    CircleAvatar(
                      backgroundImage: const AssetImage(
                        'assets/icons/profile_picture.png',
                      ), // Replace with your image path
                      radius: 20.w,
                    ),
                    Gap(5.w),
                    Text(
                      'Abdul Rahman Rahman',
                      style: TextStyles.ts13w600c44322D,
                    ),
                  ],
                ),
              ],
            ),
            Gap(16.h),
            Row(
              spacing: 8.w,
              children: [
                VehicleInfoChip(
                  title: '2002',
                ),
                VehicleInfoChip(
                  title: 'Sedan',
                ),
                VehicleInfoChip(
                  title: tr(context, 'shared'),
                  shared: true,
                ),
                VehicleInfoChip(
                  title: 'Private',
                ),
              ],
            ),
            Gap(20.h),
            Divider(
              height: 0,
              color: ColorConstants.colorF1F1F1,
              thickness: 1.h,
            ),
            Gap(10.h),
            Row(
              children: [
                CircleAvatar(
                  backgroundImage: const AssetImage(
                    'assets/icons/profile_picture.png',
                  ), // Replace with your image path
                  radius: 20.w,
                ),
                Gap(5.w),
                Expanded(
                  flex: 2,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Mohammed Al-Rifai',
                        style: TextStyles.ts16w500c44322D,
                      ),
                      Text(
                        'Valet Person',
                        style: TextStyles.ts12w400c606060,
                      )
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Time',
                        style: TextStyles.ts12w400c4D4D4D,
                      ),
                      Gap(4.h),
                      Text(
                        '10.30 AM',
                        style: TextStyles.ts12w600c353535,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
