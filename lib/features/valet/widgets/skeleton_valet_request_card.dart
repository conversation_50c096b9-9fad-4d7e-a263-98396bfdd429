import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/vehicle_info_chip.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class SkeletonValetRequestCard extends StatelessWidget {
  const SkeletonValetRequestCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, 0),
            blurRadius: 20,
            spreadRadius: 0,
          ),
        ],
      ),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text.rich(
            TextSpan(
              text: tr(context, 'id'),
              style: TextStyles.ts12w400c4D4D4D,
              children: [
                TextSpan(
                  text: ' 2586547',
                  style: TextStyles.ts12w700c353535,
                ),
              ],
            ),
          ),
          Gap(12.h),
          Divider(
            color: ColorConstants.colorF1F1F1,
            thickness: 1.h,
            height: 0,
          ),
          Gap(15.h),
          Row(
            children: [
              Container(
                height: 35.h,
                width: 35.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.r),
                  color: ColorConstants.colorF1EFE9,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8.r),
                  child: Image.asset(
                    'car'.asImagePng(),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Gap(10.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '7403 RUA',
                    style: TextStyles.ts14w600c181818,
                  ),
                  Text(
                    'Toyota Camry',
                    style: TextStyles.ts10w400cA1A09B,
                  ),
                ],
              ),
            ],
          ),
          Gap(16.h),
          Row(
            spacing: 8.w,
            children: [
              VehicleInfoChip(
                title: '2002',
              ),
              VehicleInfoChip(
                title: 'Sedan',
              ),
              VehicleInfoChip(
                title: 'Private',
              ),
            ],
          ),
        ],
      ),
    );
  }
}
