import 'package:albalad_operator_app/features/valet/models/generate_valet_ticket_model.dart';
import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/currency_icon.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:qr_flutter/qr_flutter.dart';

class TicketDetailsCard extends StatelessWidget {
  final String valetUid;
  final GenerateValetTicketModel ticket;
  const TicketDetailsCard({
    required this.valetUid,
    required this.ticket,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    String qrData = '${ApiConstants.baseURL}/app?action=valet&uid=$valetUid';
    return Container(
      decoration: BoxDecoration(
        color: ColorConstants.colorF0F0F0,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Center(
            child: QrImageView(
              data: qrData,
              version: QrVersions.auto,
              size: 93.h,
              gapless: false,
            ),
          ),
          Gap(20.h),
          DottedLine(
            dashLength: 10,
            dashGapLength: 7,
            dashColor: ColorConstants.colorCECECE,
            lineThickness: 1.1,
            lineLength: 228.w,
          ),
          Gap(30.h),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TicketItemTile(
                      title: tr(context, 'number_plate'),
                      value: ticket.numberPlate,
                    ),
                    Gap(20.h),
                    TicketItemTile(
                      title: tr(context, 'location'),
                      value: ticket.location,
                    ),
                    Gap(20.h),
                    TicketItemTile(
                      title: tr(context, 'valet_person'),
                      value: ticket.valetPerson,
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TicketItemTile(
                          title: tr(context, 'date'),
                          value: '${ticket.bookingDate}',
                          textDirection: TextDirection.ltr,
                        ),
                        Gap(20.h),
                        TicketItemTile(
                          title: tr(context, 'time'),
                          value: ticket.bookingTime,
                          textDirection: TextDirection.ltr,
                        ),
                        Gap(20.h),
                        TicketItemTile(
                          title: '',
                          value: '',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          Gap(20.h),
          if (ticket.totalAmount != null)
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              child: Column(
                children: [
                  // Display the base amount row with currency
                  _buildAmountRow(context, 'amount', ticket.actualAmount!),
                  // Add vertical spacing of 10 units between amount and VAT
                  Gap(10.h),
                  // Display the VAT (Value Added Tax) row with currency
                  _buildAmountRow(context, 'vat', ticket.vatAmount!),
                  // Add a visual separator line before the total
                  _buildDivider(),
                  // Display the total amount (base amount + VAT) with currency
                  _buildAmountRow(context, 'total_amount', ticket.totalAmount!),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // Here's how the supporting methods might look with comments:

  /// Builds a row displaying a label and its corresponding amount with currency
  /// @param context - Build context for localization
  /// @param label - Translation key for the row label
  /// @param amount - The numerical amount to display
  Widget _buildAmountRow(BuildContext context, String label, String amount) {
    return Row(
      children: [
        // Left side - Label text that expands to fill available space
        Expanded(
          child: Text(
            tr(context, label),
            style: TextStyles.ts12w400cA39A9A,
          ),
        ),
        // Right side - Amount value
        Text(
          amount,
          style: TextStyles.ts12w600c181818,
        ),
        // Currency icon/symbol at the end
        const CurrencyIcon(),
      ],
    );
  }

  /// Creates a visual divider with proper spacing
  /// Used to separate the total amount from other items
  Widget _buildDivider() {
    return Column(
      children: [
        // Space above divider
        Gap(8.h),
        // Horizontal line
        const Divider(
          height: 0,
          color: Color(0xFFEEEEEE),
          thickness: 1,
        ),
        // Larger space below divider
        Gap(15.h),
      ],
    );
  }
}

class TicketItemTile extends StatelessWidget {
  final String? title;
  final String? value;
  final TextDirection? textDirection;
  const TicketItemTile({this.title, this.value, this.textDirection, super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 2.h,
      children: [
        if (title != null)
          Text(
            title!,
            style: TextStyles.ts12w400cA39A9A,
          ),
        if (value != null)
          Text(
            value!,
            style: TextStyles.ts12w600c181818,
            textDirection: textDirection,
          ),
      ],
    );
  }
}
