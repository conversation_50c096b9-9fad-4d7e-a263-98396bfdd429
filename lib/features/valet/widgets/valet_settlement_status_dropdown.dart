import 'package:albalad_operator_app/features/valet/providers/providers.dart';
import 'package:albalad_operator_app/features/violation/models/settlement_type.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_searchable_dropdown.dart';
import 'package:albalad_operator_app/shared/widgets/skeleton_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ValetSettlementStatusDropdown extends ConsumerWidget {
  final void Function(SettlementType?)? onChanged;
  final SettlementType? value;
  const ValetSettlementStatusDropdown(
      {required this.onChanged, this.value, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final provider = ref.watch(valetSettlementStatusProvider);
    return provider.when(
      data: (statusList) {
        List<SettlementType> items = statusList;
        return CustomSearchableDropdown(
          items: items,
          displayBuilder: (p0) => p0.name ?? '',
          onChanged: onChanged,
          hintText: tr(context, 'select'),
          labelText: tr(context, 'status_star'),
          title: tr(context, 'status'),
          searchHintText: tr(context, 'search_status'),
          validator: (p0) {
            if (p0 == null) {
              return tr(context, 'please_select_settlement_status');
            }
            return null;
          },
          value: value,
        );
        // return CustomDropdownField<SettlementType?>(
        //   items: items
        //       .map(
        //         (e) => DropdownMenuItem(value: e, child: Text(e.name ?? '')),
        //       )
        //       .toList(),
        //   onChanged: onChanged,
        //   label: tr(context, 'status_star'),
        //   hint: tr(context, 'select'),
        //   value: value,
        //   validator: (p0) {
        //     if (p0 == null) {
        //       return tr(context, 'please_select_settlement_status');
        //     }
        //     return null;
        //   },
        // );
      },
      error: (error, stackTrace) => SkeletonDropdown(enabled: true),
      loading: () => const SkeletonDropdown(),
    );
  }
}
