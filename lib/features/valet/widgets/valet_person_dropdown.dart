import 'package:albalad_operator_app/features/valet/models/valet_operator.dart';
import 'package:albalad_operator_app/features/valet/providers/providers.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_searchable_dropdown.dart';
import 'package:albalad_operator_app/shared/widgets/skeleton_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ValetPersonDropdown extends ConsumerWidget {
  final ValetOperator? value;
  final void Function(ValetOperator?)? onChanged;
  const ValetPersonDropdown({
    required this.onChanged,
    required this.value,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final futureState = ref.watch(valetOperatorsProvider);
    return futureState.when(
      data: (valetOperatorList) {
        List<ValetOperator> operators = valetOperatorList;
        return CustomSearchableDropdown<ValetOperator>(
          items: operators,
          displayBuilder: (p0) => p0.name ?? '',
          onChanged: onChanged,
          labelText: tr(context, 'valet_person_star'),
          title: tr(context, 'valet_person'),
          hintText: tr(context, 'select'),
          searchHintText: tr(context, 'search_valet_person'),
          value: value,
          validator: (p0) {
            if (p0 == null || p0.isEmpty) {
              return tr(context, 'please_select_valet_person');
            }
            return null;
          },
        );
      },
      error: (error, stackTrace) => SkeletonDropdown(enabled: true),
      loading: () => const SkeletonDropdown(),
    );
  }
}
