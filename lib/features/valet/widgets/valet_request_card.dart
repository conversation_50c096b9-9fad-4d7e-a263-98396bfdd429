import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/vehicle_info_chip.dart';
import 'package:albalad_operator_app/features/valet/models/valet_request_item.dart';
import 'package:albalad_operator_app/features/valet/view/assign_valet_screen.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/inner_assign_button.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class ValetRequestCard extends StatelessWidget {
  final ValetRequestItem requestItem;
  final String previousRoute;
  const ValetRequestCard({
    required this.requestItem,
    required this.previousRoute,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    String vehicleImage =
        requestItem.vehicleImage?.firstOrNull?.image ?? 'https://';
    return Stack(
      children: [
        InkWell(
          onTap: () => assignValet(context),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  offset: const Offset(0, 0),
                  blurRadius: 20,
                  spreadRadius: 0,
                ),
              ],
            ),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text.rich(
                  TextSpan(
                    text: tr(context, 'id'),
                    style: TextStyles.ts12w400c4D4D4D,
                    children: [
                      TextSpan(
                        text: ' ${requestItem.valetId ?? ''}',
                        style: TextStyles.ts12w700c353535,
                      ),
                    ],
                  ),
                ),
                Gap(12.h),
                Divider(
                  color: ColorConstants.colorF1F1F1,
                  thickness: 1.h,
                  height: 0,
                ),
                Gap(15.h),
                Row(
                  children: [
                    Container(
                      height: 35.h,
                      width: 35.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                        color: ColorConstants.colorF1EFE9,
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8.r),
                        child: CachedNetworkImage(
                          imageUrl: vehicleImage,
                          memCacheWidth: 200,
                          fit: BoxFit.fill,
                          errorWidget: (context, url, error) => Image.asset(
                            'car'.asImagePng(),
                          ),
                        ),
                      ),
                    ),
                    Gap(10.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          requestItem.vehicleNumber ?? '',
                          style: TextStyles.ts14w600c181818,
                        ),
                        Text(
                          requestItem.vehicleName ?? '',
                          style: TextStyles.ts10w400cA1A09B,
                        ),
                      ],
                    ),
                  ],
                ),
                Gap(16.h),
                Row(
                  spacing: 8.w,
                  children: [
                    if (requestItem.makeYear != null)
                      VehicleInfoChip(
                        title: '${requestItem.makeYear}',
                      ),
                    if (requestItem.vehicleType != null)
                      VehicleInfoChip(
                        title: requestItem.vehicleType ?? '',
                      ),
                    if (requestItem.numberPlateType != null)
                      VehicleInfoChip(
                        title: requestItem.numberPlateType ?? '',
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
        Positioned(
          top: 0.h,
          right: isRTL ? null : 10.w,
          left: isRTL ? 10.w : null,
          child: InnerAssignButton(
            onPressed: () => assignValet(context),
            title: tr(context, 'assign_valet'),
          ),
        ),
      ],
    );
  }

  assignValet(BuildContext context) {
    final images = requestItem.vehicleImage ?? [];
    final arguments = AssignValetScreen(
      vehicleUid: requestItem.vehicleUid,
      vehicleId: requestItem.vehicleId,
      vehicleNumber: requestItem.vehicleNumber,
      vehicleName: requestItem.vehicleName,
      vehicleImage: images.firstOrNull?.image,
      makeYear: requestItem.makeYear?.toString(),
      vehicleType: requestItem.vehicleType,
      numberPlateType: requestItem.numberPlateType,
      valetRequestUid: requestItem.uid,
      previousRoute: previousRoute,
    );
    Navigator.pushNamed(
      context,
      AssignValetScreen.route,
      arguments: arguments,
    );
  }
}
