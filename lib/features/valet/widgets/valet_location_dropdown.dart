import 'package:albalad_operator_app/features/valet/models/valet_location.dart';
import 'package:albalad_operator_app/features/valet/providers/providers.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_searchable_dropdown.dart';
import 'package:albalad_operator_app/shared/widgets/skeleton_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ValetLocationDropdown extends ConsumerWidget {
  final ValetLocation? value;
  final void Function(ValetLocation?)? onChanged;
  const ValetLocationDropdown({
    required this.onChanged,
    required this.value,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final futureState = ref.watch(valetLocationsProvider);
    return futureState.when(
      data: (valetLocationList) {
        List<ValetLocation> locations = valetLocationList;
        return CustomSearchableDropdown<ValetLocation>(
          items: locations,
          displayBuilder: (p0) => p0.name ?? '',
          onChanged: onChanged,
          labelText: tr(context, 'location_of_valet_parking_star'),
          title: tr(context, 'location_of_valet_parking'),
          hintText: tr(context, 'select'),
          searchHintText: tr(context, 'search_location_of_valet_parking'),
          value: value,
          validator: (p0) {
            if (p0 == null || p0.isEmpty) {
              return tr(context, 'please_select_location_of_valet_parking');
            }
            return null;
          },
        );
      },
      error: (error, stackTrace) => SkeletonDropdown(enabled: true),
      loading: () => const SkeletonDropdown(),
    );
  }
}
