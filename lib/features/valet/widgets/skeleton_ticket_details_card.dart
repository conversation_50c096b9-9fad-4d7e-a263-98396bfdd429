import 'package:albalad_operator_app/features/valet/widgets/ticket_details_card.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SkeletonTicketDetailsCard extends StatelessWidget {
  const SkeletonTicketDetailsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Container(
        decoration: BoxDecoration(
          color: ColorConstants.colorF0F0F0,
          borderRadius: BorderRadius.circular(10),
        ),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Center(
              child: QrImageView(
                data: 'test',
                version: QrVersions.auto,
                size: 93.h,
                gapless: false,
              ),
            ),
            Gap(20.h),
            DottedLine(
              dashLength: 10,
              dashGapLength: 7,
              dashColor: ColorConstants.colorCECECE,
              lineThickness: 1.1,
              lineLength: 228.w,
            ),
            Gap(30.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TicketItemTile(
                      title: tr(context, 'number_plate'),
                      value: '7403 RUA',
                    ),
                    Gap(20.h),
                    TicketItemTile(
                      title: tr(context, 'location'),
                      value: 'Palm Parking',
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TicketItemTile(
                      title: tr(context, 'date'),
                      value: '25-Nov-2024',
                    ),
                    Gap(20.h),
                    TicketItemTile(
                      title: tr(context, 'valet_person'),
                      value: 'Mohammed Al-Rifai',
                    ),
                  ],
                ),
              ],
            ),
            Gap(20.h),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    tr(context, 'total_amount'),
                    style: TextStyles.ts12w400cA39A9A,
                  ),
                  Text('1.25 SAR/Hr', style: TextStyles.ts12w600c181818),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
