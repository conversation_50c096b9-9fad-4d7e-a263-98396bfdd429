import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/vehicle_info_chip.dart';
import 'package:albalad_operator_app/features/valet/models/current_valet_vehicle.dart';
import 'package:albalad_operator_app/features/valet/view/generate_valet_ticket_screen.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/status_builder.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class CurrentValetVehicleCard extends StatelessWidget {
  final CurrentValetVehicle valetVehicle;
  final String previousRoute;
  const CurrentValetVehicleCard(
      {required this.valetVehicle, required this.previousRoute, super.key});

  @override
  Widget build(BuildContext context) {
    String vehicleImage =
        valetVehicle.vehicleImage?.firstOrNull?.image ?? 'https://';
    return InkWell(
      onTap: () {
        final arguments = GenerateValetTicketScreen(
          previousRoute: previousRoute,
          valetUid: valetVehicle.uid ?? '',
        );
        Navigator.pushNamed(
          context,
          GenerateValetTicketScreen.route,
          arguments: arguments,
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 0),
              blurRadius: 20,
              spreadRadius: 0,
              color: Colors.black.withValues(alpha: 0.1),
            ),
          ],
        ),
        padding: EdgeInsets.fromLTRB(16.w, 14.h, 16.w, 8.h),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text.rich(
                            TextSpan(
                              text: tr(context, 'id'),
                              style: TextStyles.ts12w400c4D4D4D,
                              children: [
                                TextSpan(
                                  text: ' ${valetVehicle.valetId}',
                                  style: TextStyles.ts12w700c353535,
                                ),
                              ],
                            ),
                          ),
                          if (valetVehicle.status?.toLowerCase() ==
                              'settled') ...[
                            Gap(10.w),
                            StatusBuilder(
                              status: valetVehicle.status,
                            )
                          ],
                        ],
                      ),
                      Gap(10.h),
                      Row(
                        children: [
                          Container(
                            height: 35.h,
                            width: 35.w,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                              color: ColorConstants.colorF1EFE9,
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8.r),
                              child: CachedNetworkImage(
                                imageUrl: vehicleImage,
                                fit: BoxFit.fill,
                                memCacheWidth: 200,
                                errorWidget: (context, url, error) {
                                  return Image.asset('car'.asImagePng());
                                },
                              ),
                            ),
                          ),
                          Gap(10.w),
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  valetVehicle.vehicleNumber ?? '',
                                  style: TextStyles.ts14w600c181818,
                                ),
                                Text(
                                  valetVehicle.vehicleName ?? '',
                                  style: TextStyles.ts10w400cA1A09B,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Column(
                  children: [
                    ClipOval(
                      child: CachedNetworkImage(
                        imageUrl:
                            valetVehicle.ownerDetails?.profilePic ?? 'https://',
                        width: 40.w,
                        height: 40.h,
                        fit: BoxFit.cover,
                        errorWidget: (context, url, error) {
                          return CircleAvatar(
                            backgroundImage: const AssetImage(
                              'assets/icons/profile_picture.png',
                            ), // Replace with your image path
                            radius: 20.w,
                          );
                        },
                      ),
                    ),
                    Gap(5.w),
                    SizedBox(
                      width: 82.w,
                      child: Text(
                        valetVehicle.ownerDetails?.ownerName ?? '',
                        style: TextStyles.ts13w600c44322D,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Gap(16.h),
            Row(
              spacing: 8.w,
              children: [
                if (valetVehicle.makeYear != null)
                  VehicleInfoChip(
                    title: valetVehicle.makeYear.toString(),
                  ),
                if (valetVehicle.vehicleType != null)
                  VehicleInfoChip(
                    title: valetVehicle.vehicleType.toString(),
                  ),
                // VehicleInfoChip(
                //   title: tr(context, 'shared'),
                //   shared: true,
                // ),
                if (valetVehicle.numberPlateType != null)
                  VehicleInfoChip(
                    title: valetVehicle.numberPlateType.toString(),
                  ),
              ],
            ),
            Gap(20.h),
            Divider(
              height: 0,
              color: ColorConstants.colorF1F1F1,
              thickness: 1.h,
            ),
            Gap(10.h),
            Row(
              children: [
                ClipOval(
                  child: CachedNetworkImage(
                    imageUrl: valetVehicle.valetPersonImage ?? 'https://',
                    width: 40.w,
                    height: 40.h,
                    fit: BoxFit.cover,
                    errorWidget: (context, url, error) {
                      return CircleAvatar(
                        backgroundImage: const AssetImage(
                          'assets/icons/profile_picture.png',
                        ), // Replace with your image path
                        radius: 20.w,
                      );
                    },
                  ),
                ),
                Gap(5.w),
                Expanded(
                  flex: 2,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        valetVehicle.valetPerson ?? '',
                        style: TextStyles.ts16w500c44322D,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        tr(context, 'valet_person'),
                        style: TextStyles.ts12w400c606060,
                      )
                    ],
                  ),
                ),
                Gap(20.w),
                if (valetVehicle.valetTime != null) ...[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        tr(context, 'time'),
                        style: TextStyles.ts12w400c4D4D4D,
                      ),
                      Gap(4.h),
                      Text(
                        valetVehicle.valetTime ?? '',
                        style: TextStyles.ts12w600c353535,
                        textDirection: TextDirection.ltr,
                      ),
                    ],
                  ),
                  Gap(30.w),
                ]
              ],
            ),
          ],
        ),
      ),
    );
  }
}
