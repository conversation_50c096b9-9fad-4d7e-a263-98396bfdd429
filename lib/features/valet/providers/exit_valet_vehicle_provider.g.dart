// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'exit_valet_vehicle_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$exitValetVehicleNotifierHash() =>
    r'3a227a64b1f02857e004ec11682f9ae4cc51c8a6';

/// See also [ExitValetVehicleNotifier].
@ProviderFor(ExitValetVehicleNotifier)
final exitValetVehicleNotifierProvider = AutoDisposeNotifierProvider<
    ExitValetVehicleNotifier, ExitValetVehicleState>.internal(
  ExitValetVehicleNotifier.new,
  name: r'exitValetVehicleNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$exitValetVehicleNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ExitValetVehicleNotifier = AutoDisposeNotifier<ExitValetVehicleState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
