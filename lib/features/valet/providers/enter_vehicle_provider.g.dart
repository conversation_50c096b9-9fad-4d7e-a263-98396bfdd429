// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enter_vehicle_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$enterVehicleNotifierHash() =>
    r'a848bf7446ca8116584299be3d4857a645b53b01';

/// See also [EnterVehicleNotifier].
@ProviderFor(EnterVehicleNotifier)
final enterVehicleNotifierProvider = AutoDisposeNotifierProvider<
    EnterVehicleNotifier, EnterVehicleState>.internal(
  EnterVehicleNotifier.new,
  name: r'enterVehicleNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$enterVehicleNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EnterVehicleNotifier = AutoDisposeNotifier<EnterVehicleState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
