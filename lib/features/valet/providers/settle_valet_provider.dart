import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'settle_valet_provider.g.dart';

abstract class SettleValetState {}

class SettleValeInitial extends SettleValetState {}

class SettleValeLoading extends SettleValetState {}

class SettleValeSuccess extends SettleValetState {}

class SettleValeFailure extends SettleValetState {
  final String message;
  SettleValeFailure(this.message);
}

@riverpod
class SettleValetNotifier extends _$SettleValetNotifier {
  @override
  SettleValetState build() => SettleValeInitial();

  final Dio _dio = DioClient().dio;

  Future<void> submit({
    required String valetUid,
    required String status,
    required String paymentMethodUid,
    required String additionalNote,
  }) async {
    state = SettleValeLoading();
    try {
      Map<String, dynamic> data = {
        'valet_uid': valetUid,
        'status': status,
        'payment_method_uid': paymentMethodUid,
      };
      if (additionalNote.isNotEmpty) {
        data['additional_note'] = additionalNote;
      }

      final response = await _dio.post(
        ApiConstants.valetSettlement,
        options: Options(
          headers: await ApiConstants.authHeaders(),
        ),
        data: data,
      );

      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          state = SettleValeSuccess();
          return;
        }
      }

      if (response.statusCode == 400) {
        Map<String, dynamic>? errors = response.data['errors'];
        String? message = response.data['message'];
        if (errors != null && errors.isNotEmpty) {
          state = SettleValeFailure(errors.values.firstOrNull.toString());
          return;
        }
        if (message != null) {
          state = SettleValeFailure(message);
          return;
        }
      }
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = SettleValeFailure(
          appLocalization.translate('networkError'),
        );
        return;
      }
      state = SettleValeFailure(e.toString());
    }
  }
}
