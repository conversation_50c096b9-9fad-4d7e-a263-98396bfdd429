// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'settle_valet_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$settleValetNotifierHash() =>
    r'a3169c9a111b2e7b39715aa5cf68612976596f37';

/// See also [SettleValetNotifier].
@ProviderFor(SettleValetNotifier)
final settleValetNotifierProvider =
    AutoDisposeNotifierProvider<SettleValetNotifier, SettleValetState>.internal(
  SettleValetNotifier.new,
  name: r'settleValetNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$settleValetNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SettleValetNotifier = AutoDisposeNotifier<SettleValetState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
