import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'request_vehicle_provider.g.dart';

abstract class RequestVehicleState {}

class RequestVehicleInitial extends RequestVehicleState {}

class RequestVehicleLoading extends RequestVehicleState {}

class RequestVehicleSuccess extends RequestVehicleState {}

class RequestVehicleFailure extends RequestVehicleState {
  final String message;
  RequestVehicleFailure(this.message);
}

@riverpod
class RequestVehicleNotifier extends _$RequestVehicleNotifier {
  @override
  RequestVehicleState build() => RequestVehicleInitial();

  final Dio _dio = DioClient().dio;

  Future<void> requestVehicle({required String valetUid}) async {
    state = RequestVehicleLoading();
    try {
      final response = await _dio.get(
        '${ApiConstants.vehicleRequest}$valetUid/',
        options: Options(
          headers: await ApiConstants.authHeaders(),
        ),
      );

      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          state = RequestVehicleSuccess();
          return;
        }
      }

      if (response.statusCode == 400) {
        Map<String, dynamic>? errors = response.data['errors'];
        String? message = response.data['message'];
        if (errors != null && errors.isNotEmpty) {
          state = RequestVehicleFailure(errors.values.firstOrNull.toString());
          return;
        }
        if (message != null) {
          state = RequestVehicleFailure(message);
          return;
        }
      }
    } catch (e) {
      state = RequestVehicleFailure(e.toString());
    }
  }
}
