import 'dart:async';
import 'dart:io';

import 'package:albalad_operator_app/features/valet/models/valet_request_item.dart';
import 'package:albalad_operator_app/features/valet/providers/providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'valet_request_list_provider.g.dart';

class ValetRequestListState {
  final List<ValetRequestItem> items;
  final bool isLoading;
  final bool hasMore;
  final bool isConnectionError;
  final String? error;

  ValetRequestListState({
    required this.items,
    required this.isLoading,
    required this.hasMore,
    this.isConnectionError = false,
    this.error,
  });

  ValetRequestListState copyWith({
    List<ValetRequestItem>? items,
    bool? isLoading,
    bool? hasMore,
    bool? isConnectionError,
    String? error,
  }) {
    return ValetRequestListState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      isConnectionError: isConnectionError ?? this.isConnectionError,
      error: error ?? this.error,
    );
  }
}

@riverpod
class ValetRequestListNotifier extends _$ValetRequestListNotifier {
  @override
  ValetRequestListState build() {
    return ValetRequestListState(
      items: [],
      isLoading: false,
      hasMore: true,
      error: null,
    );
  }

  int _currentPage = 1;

  Timer? _debounce;

  void searchValetRequests({String query = ''}) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    _debounce = Timer(const Duration(milliseconds: 300), () {
      _currentPage = 1;
      state = state.copyWith(items: [], isLoading: false, hasMore: true);
      fetchValetRequestList(query: query);
    });
  }

  Future<void> fetchValetRequestList({String query = ''}) async {
    if (state.isLoading || !state.hasMore) return;
    state =
        state.copyWith(isLoading: true, error: null, isConnectionError: false);

    try {
      final valetServices = ref.watch(valetServicesProvider);
      final response = await valetServices.fetchValetRequestList(
        page: _currentPage,
        query: query,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          final List<dynamic> records = data['records'];
          final List<ValetRequestItem> valetRequests = records
              .map(
                (e) => ValetRequestItem.fromJson(e),
              )
              .toList();

          state = state.copyWith(
            items: [...state.items, ...valetRequests],
            isLoading: false,
            hasMore: data['pagination']?['has_next'] == true,
          );
          _currentPage++;
        } else {
          state = state.copyWith(isLoading: false, hasMore: false);
        }
      } else {
        state = state.copyWith(isLoading: false, hasMore: false);
      }
    } on SocketException {
      state = state.copyWith(
        isLoading: false,
        hasMore: false,
        isConnectionError: true,
      );
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = state.copyWith(
          isLoading: false,
          hasMore: false,
          isConnectionError: true,
        );
        return;
      }
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
        hasMore: false,
      );
    }
  }

  void reset() {
    _currentPage = 1;
    state = ValetRequestListState(
      items: [],
      isLoading: false,
      hasMore: true,
    );
  }
}
