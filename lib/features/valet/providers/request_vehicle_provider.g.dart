// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'request_vehicle_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$requestVehicleNotifierHash() =>
    r'ad5694bef7506720c9e599e76164ee675c5b8103';

/// See also [RequestVehicleNotifier].
@ProviderFor(RequestVehicleNotifier)
final requestVehicleNotifierProvider = AutoDisposeNotifierProvider<
    RequestVehicleNotifier, RequestVehicleState>.internal(
  RequestVehicleNotifier.new,
  name: r'requestVehicleNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$requestVehicleNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RequestVehicleNotifier = AutoDisposeNotifier<RequestVehicleState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
