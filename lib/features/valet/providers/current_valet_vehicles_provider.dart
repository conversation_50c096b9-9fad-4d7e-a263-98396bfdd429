import 'dart:async';
import 'dart:io';

import 'package:albalad_operator_app/features/valet/models/current_valet_vehicle.dart';
import 'package:albalad_operator_app/features/valet/providers/providers.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'current_valet_vehicles_provider.g.dart';

class CurrentValetVehiclesState {
  final List<CurrentValetVehicle> items;
  final bool isLoading;
  final bool hasMore;
  final bool isConnectionError;
  final String? error;

  CurrentValetVehiclesState({
    required this.items,
    required this.isLoading,
    required this.hasMore,
    this.isConnectionError = false,
    this.error,
  });

  CurrentValetVehiclesState copyWith({
    List<CurrentValetVehicle>? items,
    bool? isLoading,
    bool? hasMore,
    bool? isConnectionError,
    String? error,
  }) {
    return CurrentValetVehiclesState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      isConnectionError: isConnectionError ?? this.isConnectionError,
      error: error ?? this.error,
    );
  }
}

@riverpod
class CurrentValetVehiclesNotifier extends _$CurrentValetVehiclesNotifier {
  @override
  CurrentValetVehiclesState build() {
    return CurrentValetVehiclesState(
      items: [],
      isLoading: false,
      hasMore: true,
      error: null,
      isConnectionError: false,
    );
  }

  int _currentPage = 1;

  Timer? _debounce;

  void searchValetVehicle({String query = ''}) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    _debounce = Timer(const Duration(milliseconds: 300), () {
      _currentPage = 1;
      state = state.copyWith(items: [], isLoading: false, hasMore: true);
      fetchCurrentValetVehicles(query: query);
    });
  }

  Future<void> fetchCurrentValetVehicles({String query = ''}) async {
    if (state.isLoading || !state.hasMore) return;
    state =
        state.copyWith(isLoading: true, error: null, isConnectionError: false);

    try {
      final valetServices = ref.watch(valetServicesProvider);
      final response = await valetServices.fetchCurrentValetVehicles(
          page: _currentPage, query: query);
      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          final List<dynamic> records = data['data'];
          final List<CurrentValetVehicle> currentValetVehicles =
              records.map((e) => CurrentValetVehicle.fromJson(e)).toList();
          state = state.copyWith(
            items: [...state.items, ...currentValetVehicles],
            isLoading: false,
            hasMore: data['pagination']?['has_next'] == true,
          );
          _currentPage++;
        } else {
          state = state.copyWith(isLoading: false, hasMore: false);
        }
      } else {
        state = state.copyWith(isLoading: false, hasMore: false);
      }
    } on SocketException {
      state = state.copyWith(
        isLoading: false,
        hasMore: false,
        error: appLocalization.translate('networkError'),
        isConnectionError: true,
      );
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = state.copyWith(
          isLoading: false,
          hasMore: false,
          error: appLocalization.translate('networkError'),
          isConnectionError: true,
        );
        return;
      }
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
        hasMore: false,
      );
    }
  }

  void reset() {
    _currentPage = 1;
    state = CurrentValetVehiclesState(
      items: [],
      isLoading: false,
      hasMore: true,
      isConnectionError: false,
    );
  }
}
