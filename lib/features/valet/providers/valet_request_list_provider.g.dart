// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'valet_request_list_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$valetRequestListNotifierHash() =>
    r'048e402c4df33d401fa21abdffb8a2440b2cf93c';

/// See also [ValetRequestListNotifier].
@ProviderFor(ValetRequestListNotifier)
final valetRequestListNotifierProvider = AutoDisposeNotifierProvider<
    ValetRequestListNotifier, ValetRequestListState>.internal(
  ValetRequestListNotifier.new,
  name: r'valetRequestListNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$valetRequestListNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ValetRequestListNotifier = AutoDisposeNotifier<ValetRequestListState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
