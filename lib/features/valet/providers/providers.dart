import 'package:albalad_operator_app/features/valet/models/generate_valet_ticket_model.dart';
import 'package:albalad_operator_app/features/valet/models/valet_location.dart';
import 'package:albalad_operator_app/features/valet/models/valet_operator.dart';
import 'package:albalad_operator_app/features/violation/models/settlement_type.dart';
import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/services/valet_services.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'providers.g.dart';

// Define a Riverpod provider for the ValetServices class
final valetServicesProvider = Provider<ValetServices>((ref) {
  return ValetServices();
});

final valetSettlementStatusProvider =
    FutureProvider.autoDispose<List<SettlementType>>((ref) async {
  final valetServices = ref.read(valetServicesProvider);
  return await valetServices.fetchValetSettlementStatus();
});

@riverpod
Future<List<ValetOperator>> valetOperators(ref) {
  final valetServices = ref.watch(valetServicesProvider);
  return valetServices.fetchValetOperators();
}

@riverpod
Future<List<ValetLocation>> valetLocations(ref) {
  final valetServices = ref.watch(valetServicesProvider);
  return valetServices.fetchValetLocations();
}

abstract class AssignValetState {}

class AssignValetInitial extends AssignValetState {}

class AssignValetLoading extends AssignValetState {}

class AssignValetSuccess extends AssignValetState {
  final String valetUid;
  AssignValetSuccess(this.valetUid);
}

class AssignValetError extends AssignValetState {
  final String message;
  AssignValetError(this.message);
}

@riverpod
class AssignValetNotifier extends _$AssignValetNotifier {
  @override
  AssignValetState build() {
    return AssignValetInitial();
  }

  final Dio _dio = DioClient().dio;

  Future<void> assignValet({
    required String vehicleUid,
    required String operatorUid,
    required String locationUid,
    String? valetRequestUid,
  }) async {
    state = AssignValetLoading();
    try {
      Map<String, dynamic> requestBody = {
        'vehicle_uid': vehicleUid,
        'operator': operatorUid,
        'location': locationUid,
      };

      if (valetRequestUid != null) {
        requestBody['valet_request_uid'] = valetRequestUid;
      }

      final response = await _dio.post(
        ApiConstants.assignValet,
        options: Options(
          headers: await ApiConstants.authHeaders(),
        ),
        data: requestBody,
      );

      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          // String valetRequestUid = response.data['valet_request_uid'] ?? '';
          String valetUid = response.data['valet_uid'] ?? '';
          state = AssignValetSuccess(valetUid);
          return;
        } else if (response.data['message'] != null) {
          state = AssignValetError(response.data['message']);
        }
      }
      if (response.statusCode == 400) {
        Map<String, dynamic> errors = response.data['errors'];
        if (errors.isNotEmpty) {
          state = AssignValetError(errors.values.firstOrNull.toString());
        } else if (response.data['message'] != null) {
          state = AssignValetError(response.data['message']);
        }
        return;
      }
      state = AssignValetError(appLocalization.translate('somethingWentWrong'));
    } catch (e) {
      state = AssignValetError(e.toString());
    }
  }
}

@riverpod
Future<GenerateValetTicketModel> generateValetTicket(
  ref, {
  required String valetUid,
}) {
  final valetServices = ref.watch(valetServicesProvider);
  return valetServices.generateValetTicket(valetUid: valetUid);
}
