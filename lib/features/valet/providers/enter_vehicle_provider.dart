import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'enter_vehicle_provider.g.dart';

abstract class EnterVehicleState {}

class EnterVehicleInitial extends EnterVehicleState {}

class EnterVehicleLoading extends EnterVehicleState {}

class EnterVehicleSuccess extends EnterVehicleState {}

class EnterVehicleError extends EnterVehicleState {
  final String message;
  EnterVehicleError(this.message);
}

@riverpod
class EnterVehicleNotifier extends _$EnterVehicleNotifier {
  @override
  EnterVehicleState build() {
    return EnterVehicleInitial();
  }

  final Dio _dio = DioClient().dio;

  Future<void> enterVehicle({
    required String valetUid,
  }) async {
    state = EnterVehicleLoading();
    try {
      final response = await _dio.post(
        ApiConstants.enterVehicles,
        data: {
          'valet_uid': valetUid,
        },
        options: Options(
          headers: await ApiConstants.authHeaders(),
        ),
      );

      if (response.statusCode == 200) {
        state = EnterVehicleSuccess();
      } else if (response.statusCode == 400) {
        Map<String, dynamic> errors = response.data['errors'] ?? {};
        if (errors.isNotEmpty) {
          state = EnterVehicleError(errors.values.first[0]);
        } else {
          state = EnterVehicleError(response.data['message'] ??
              appLocalization.translate('somethingWentWrong'));
        }
      } else {
        state = EnterVehicleError(response.data['message'] ??
            appLocalization.translate('somethingWentWrong'));
      }
    } catch (e) {
      state = EnterVehicleError(e.toString());
    }
  }
}
