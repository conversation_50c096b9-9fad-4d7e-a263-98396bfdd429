// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$valetOperatorsHash() => r'2b04459f233d7a7895a16165fa20a1d8df5f1464';

/// See also [valetOperators].
@ProviderFor(valetOperators)
final valetOperatorsProvider =
    AutoDisposeFutureProvider<List<ValetOperator>>.internal(
  valetOperators,
  name: r'valetOperatorsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$valetOperatorsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ValetOperatorsRef = AutoDisposeFutureProviderRef<List<ValetOperator>>;
String _$valetLocationsHash() => r'e2072aae72c01fd2cb386e9ce9050bfe9fe02235';

/// See also [valetLocations].
@ProviderFor(valetLocations)
final valetLocationsProvider =
    AutoDisposeFutureProvider<List<ValetLocation>>.internal(
  valetLocations,
  name: r'valetLocationsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$valetLocationsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ValetLocationsRef = AutoDisposeFutureProviderRef<List<ValetLocation>>;
String _$generateValetTicketHash() =>
    r'40ca92206461dd8da654cb60b2fca3cd708d46fe';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [generateValetTicket].
@ProviderFor(generateValetTicket)
const generateValetTicketProvider = GenerateValetTicketFamily();

/// See also [generateValetTicket].
class GenerateValetTicketFamily
    extends Family<AsyncValue<GenerateValetTicketModel>> {
  /// See also [generateValetTicket].
  const GenerateValetTicketFamily();

  /// See also [generateValetTicket].
  GenerateValetTicketProvider call({
    required String valetUid,
  }) {
    return GenerateValetTicketProvider(
      valetUid: valetUid,
    );
  }

  @override
  GenerateValetTicketProvider getProviderOverride(
    covariant GenerateValetTicketProvider provider,
  ) {
    return call(
      valetUid: provider.valetUid,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'generateValetTicketProvider';
}

/// See also [generateValetTicket].
class GenerateValetTicketProvider
    extends AutoDisposeFutureProvider<GenerateValetTicketModel> {
  /// See also [generateValetTicket].
  GenerateValetTicketProvider({
    required String valetUid,
  }) : this._internal(
          (ref) => generateValetTicket(
            ref as GenerateValetTicketRef,
            valetUid: valetUid,
          ),
          from: generateValetTicketProvider,
          name: r'generateValetTicketProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$generateValetTicketHash,
          dependencies: GenerateValetTicketFamily._dependencies,
          allTransitiveDependencies:
              GenerateValetTicketFamily._allTransitiveDependencies,
          valetUid: valetUid,
        );

  GenerateValetTicketProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.valetUid,
  }) : super.internal();

  final String valetUid;

  @override
  Override overrideWith(
    FutureOr<GenerateValetTicketModel> Function(GenerateValetTicketRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GenerateValetTicketProvider._internal(
        (ref) => create(ref as GenerateValetTicketRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        valetUid: valetUid,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<GenerateValetTicketModel> createElement() {
    return _GenerateValetTicketProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GenerateValetTicketProvider && other.valetUid == valetUid;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, valetUid.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin GenerateValetTicketRef
    on AutoDisposeFutureProviderRef<GenerateValetTicketModel> {
  /// The parameter `valetUid` of this provider.
  String get valetUid;
}

class _GenerateValetTicketProviderElement
    extends AutoDisposeFutureProviderElement<GenerateValetTicketModel>
    with GenerateValetTicketRef {
  _GenerateValetTicketProviderElement(super.provider);

  @override
  String get valetUid => (origin as GenerateValetTicketProvider).valetUid;
}

String _$assignValetNotifierHash() =>
    r'f65006547dfb08045793a1f4c4e5c578a6e71f1b';

/// See also [AssignValetNotifier].
@ProviderFor(AssignValetNotifier)
final assignValetNotifierProvider =
    AutoDisposeNotifierProvider<AssignValetNotifier, AssignValetState>.internal(
  AssignValetNotifier.new,
  name: r'assignValetNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$assignValetNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AssignValetNotifier = AutoDisposeNotifier<AssignValetState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
