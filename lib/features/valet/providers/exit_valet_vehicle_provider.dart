import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'exit_valet_vehicle_provider.g.dart';

abstract class ExitValetVehicleState {}

class ExitValetVehicleInitial extends ExitValetVehicleState {}

class ExitValetVehicleLoading extends ExitValetVehicleState {}

class ExitValetVehicleSuccess extends ExitValetVehicleState {}

class ExitValetVehicleFailure extends ExitValetVehicleState {
  final String message;
  ExitValetVehicleFailure(this.message);
}

@riverpod
class ExitValetVehicleNotifier extends _$ExitValetVehicleNotifier {
  @override
  ExitValetVehicleState build() => ExitValetVehicleInitial();

  final Dio _dio = DioClient().dio;

  Future<void> exitVehicle({required String valetUid}) async {
    state = ExitValetVehicleLoading();
    try {
      final response = await _dio.post(
        ApiConstants.exitVehicle,
        options: Options(
          headers: await ApiConstants.authHeaders(),
        ),
        data: {'valet_uid': valetUid},
      );

      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          state = ExitValetVehicleSuccess();
          return;
        }
      }

      if (response.statusCode == 400) {
        Map<String, dynamic>? errors = response.data['errors'];
        String? message = response.data['message'];
        if (errors != null && errors.isNotEmpty) {
          state = ExitValetVehicleFailure(errors.values.firstOrNull.toString());
          return;
        }
        if (message != null) {
          state = ExitValetVehicleFailure(message);
          return;
        }
      }
    } catch (e) {
      state = ExitValetVehicleFailure(e.toString());
    }
  }
}
