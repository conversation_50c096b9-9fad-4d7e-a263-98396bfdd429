import 'package:albalad_operator_app/shared/models/vehicle_image.dart';

class CurrentValetVehicle {
  String? uid;
  String? valetId;
  String? valetRequestUid;
  String? vehicleNumber;
  List<VehicleImage>? vehicleImage;
  String? vehicleUid;
  String? vehicleId;
  String? vehicleName;
  String? vehicleType;
  int? makeYear;
  String? numberPlateType;
  OwnerDetails? ownerDetails;
  String? valetPerson;
  String? valetPersonImage;
  String? valetDate;
  String? valetTime;
  String? status;

  CurrentValetVehicle({
    this.uid,
    this.valetId,
    this.valetRequestUid,
    this.vehicleNumber,
    this.vehicleImage,
    this.vehicleUid,
    this.vehicleId,
    this.vehicleName,
    this.vehicleType,
    this.makeYear,
    this.numberPlateType,
    this.ownerDetails,
    this.valetPerson,
    this.valetPersonImage,
    this.valetDate,
    this.valetTime,
    this.status,
  });

  CurrentValetVehicle.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    valetId = json['valet_id'];
    valetRequestUid = json['valet_request_uid'];
    vehicleNumber = json['vehicle_number'];
    if (json['vehicle_image'] != null) {
      vehicleImage = <VehicleImage>[];
      json['vehicle_image'].forEach((v) {
        vehicleImage!.add(VehicleImage.fromJson(v));
      });
    }
    vehicleUid = json['vehicle_uid'];
    vehicleId = json['vehicle_id'];
    vehicleName = json['vehicle_name'];
    vehicleType = json['vehicle_type'];
    makeYear = int.tryParse(json['make_year'].toString());
    numberPlateType = json['number_plate_type'];
    ownerDetails = json['owner_details'] != null
        ? OwnerDetails.fromJson(json['owner_details'])
        : null;
    valetPerson = json['valet_person'];
    valetPersonImage = json['valet_person_image'];
    valetDate = json['valet_date'];
    valetTime = json['valet_time'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['valet_id'] = valetId;
    data['valet_request_uid'] = valetRequestUid;
    data['vehicle_number'] = vehicleNumber;
    if (vehicleImage != null) {
      data['vehicle_image'] = vehicleImage!.map((v) => v.toJson()).toList();
    }
    data['vehicle_uid'] = vehicleUid;
    data['vehicle_id'] = vehicleId;
    data['vehicle_name'] = vehicleName;
    data['vehicle_type'] = vehicleType;
    data['make_year'] = makeYear;
    data['number_plate_type'] = numberPlateType;
    if (ownerDetails != null) {
      data['owner_details'] = ownerDetails!.toJson();
    }
    data['valet_person'] = valetPerson;
    data['valet_person_image'] = valetPersonImage;
    data['valet_date'] = valetDate;
    data['valet_time'] = valetTime;
    data['status'] = status;
    return data;
  }
}

class OwnerDetails {
  String? ownerName;
  String? profilePic;

  OwnerDetails({this.ownerName, this.profilePic});

  OwnerDetails.fromJson(Map<String, dynamic> json) {
    ownerName = json['owner_name'];
    profilePic = json['profile_pic'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['owner_name'] = ownerName;
    data['profile_pic'] = profilePic;
    return data;
  }
}
