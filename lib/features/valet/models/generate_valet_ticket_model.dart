class GenerateValetTicketModel {
  String? uid;
  String? valetUid;
  String? numberPlate;
  String? bookingDate;
  String? bookingTime;
  String? location;
  String? valetPerson;
  String? actualAmount;
  String? vatAmount;
  String? totalAmount;
  bool? isEntered;
  bool? isSettled;
  bool? isExited;
  bool? assignedToMe;

  GenerateValetTicketModel({
    this.uid,
    this.valetUid,
    this.numberPlate,
    this.bookingDate,
    this.bookingTime,
    this.location,
    this.valetPerson,
    this.actualAmount,
    this.vatAmount,
    this.totalAmount,
    this.isEntered,
    this.isSettled,
    this.isExited,
    this.assignedToMe,
  });

  GenerateValetTicketModel.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    valetUid = json['valet_uid'];
    numberPlate = json['number_plate'];
    bookingDate = json['booking_date'];
    bookingTime = json['booking_time'];
    location = json['location'];
    valetPerson = json['valet_person'];
    actualAmount = json['actual_amount'];
    vatAmount = json['vat_amount'];
    totalAmount = json['total_amount'];
    isEntered = json['is_entered'];
    isSettled = json['is_settled'];
    isExited = json['is_exited'];
    assignedToMe = json['assigned_to_me'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['valet_uid'] = valetUid;
    data['number_plate'] = numberPlate;
    data['booking_date'] = bookingDate;
    data['booking_time'] = bookingTime;
    data['location'] = location;
    data['valet_person'] = valetPerson;
    data['actual_amount'] = actualAmount;
    data['vat_amount'] = vatAmount;
    data['total_amount'] = totalAmount;
    data['is_entered'] = isEntered;
    data['is_settled'] = isSettled;
    data['is_exited'] = isExited;
    data['assigned_to_me'] = assignedToMe;
    return data;
  }
}
