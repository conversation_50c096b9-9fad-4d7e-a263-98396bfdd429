import 'package:albalad_operator_app/shared/models/vehicle_image.dart';

class ValetRequestItem {
  String? uid;
  String? valetId;
  String? vehicleUid;
  String? vehicleNumber;
  List<VehicleImage>? vehicleImage;
  String? vehicleId;
  String? vehicleName;
  String? vehicleType;
  int? makeYear;
  String? numberPlateType;
  String? ownerName;
  bool? isValetReservation;

  ValetRequestItem({
    this.uid,
    this.valetId,
    this.vehicleUid,
    this.vehicleNumber,
    this.vehicleImage,
    this.vehicleId,
    this.vehicleName,
    this.vehicleType,
    this.makeYear,
    this.numberPlateType,
    this.ownerName,
    this.isValetReservation,
  });

  ValetRequestItem.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    valetId = json['valet_id'];
    vehicleUid = json['vehicle_uid'];
    vehicleNumber = json['vehicle_number'];
    if (json['vehicle_image'] != null) {
      vehicleImage = <VehicleImage>[];
      json['vehicle_image'].forEach((v) {
        vehicleImage!.add(VehicleImage.fromJson(v));
      });
    }
    vehicleId = json['vehicle_id'];
    vehicleName = json['vehicle_name'];
    vehicleType = json['vehicle_type'];
    makeYear = int.tryParse(json['make_year'].toString());
    numberPlateType = json['number_plate_type'];
    ownerName = json['owner_name'];
    isValetReservation = json['is_valet_reservation'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['valet_id'] = valetId;
    data['vehicle_uid'] = vehicleUid;
    data['vehicle_number'] = vehicleNumber;
    if (vehicleImage != null) {
      data['vehicle_image'] = vehicleImage!.map((v) => v.toJson()).toList();
    }
    data['vehicle_id'] = vehicleId;
    data['vehicle_name'] = vehicleName;
    data['vehicle_type'] = vehicleType;
    data['make_year'] = makeYear;
    data['number_plate_type'] = numberPlateType;
    data['owner_name'] = ownerName;
    data['is_valet_reservation'] = isValetReservation;
    return data;
  }
}
