import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class SuccessScreen extends StatelessWidget {
  static const route = '/success-screen';
  final String? title;
  final String? message;
  final String? buttonName;
  final void Function(BuildContext context)? onPressed;
  const SuccessScreen(
      {this.title, this.message, this.onPressed, this.buttonName, super.key});

  @override
  Widget build(BuildContext context) {
    late String buttonText;
    if (buttonName != null) {
      buttonText = buttonName!;
    } else {
      buttonText = tr(context, 'done');
    }
    return PopScope(
      canPop: false,
      child: Scaffold(
        body: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset('success'.asIconSvg()),
              if (title != null) ...[
                Text(
                  title!,
                  style: TextStyles.ts30w700c44322D,
                  textAlign: TextAlign.center,
                ),
                Gap(6.h),
              ],
              if (message != null)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Text(
                    message!,
                    textAlign: TextAlign.center,
                    style: TextStyles.ts14w500c959595,
                  ),
                )
            ],
          ),
        ),
        bottomNavigationBar: onPressed != null
            ? Padding(
                padding:
                    EdgeInsets.only(bottom: 30.0.h, left: 16.w, right: 16.w),
                child: ElevatedButton(
                  onPressed:
                      onPressed != null ? () => onPressed!(context) : null,
                  child: Text(buttonText),
                ),
              )
            : null,
      ),
    );
  }
}
