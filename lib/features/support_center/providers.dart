import 'package:albalad_operator_app/features/support_center/models/contact_admin.dart';
import 'package:albalad_operator_app/features/support_center/models/faq.dart';
import 'package:albalad_operator_app/features/support_center/models/privacy_policy.dart';
import 'package:albalad_operator_app/features/support_center/models/terms_and_conditions.dart';
import 'package:albalad_operator_app/shared/services/legal_services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final termsAndCoditionsProvider =
    FutureProvider.autoDispose<TermsAndConditions?>(
        (ref) => LegalServices().fetchTermsAndConditions());

final privacyPolicyProvider = FutureProvider.autoDispose<PrivacyPolicy?>(
    (ref) => LegalServices().fetchPrivacyPolicy());

final faqsProvider =
    FutureProvider.autoDispose<List<FAQ>>((ref) => LegalServices().fetchFAQs());

final contactAdminProvider =
    FutureProvider<ContactAdmin?>((ref) => LegalServices().contactUs());
