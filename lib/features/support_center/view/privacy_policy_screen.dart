import 'package:albalad_operator_app/features/support_center/models/privacy_policy.dart';
import 'package:albalad_operator_app/features/support_center/providers.dart';
import 'package:albalad_operator_app/features/support_center/view/legal_app_bar.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/no_network_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PrivacyPolicyScreen extends ConsumerWidget {
  static const String route = '/privacy_policy';
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<PrivacyPolicy?> privacyPolicy =
        ref.watch(privacyPolicyProvider);
    return Scaffold(
      appBar: LegalAppBar(
        title: Text(tr(context, 'privacy_policies')),
      ),
      body: privacyPolicy.when(
        data: (data) {
          return SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Html(data: data?.text ?? '', style: {
                "h1": Style(
                  fontWeight: FontWeight.w600,
                  fontSize: FontSize(16.sp),
                  color: ColorConstants.color181818,
                ),
                "h2": Style(
                  fontWeight: FontWeight.w600,
                  fontSize: FontSize(16.sp),
                  color: ColorConstants.color181818,
                ),
                "h3": Style(
                  fontWeight: FontWeight.w600,
                  fontSize: FontSize(16.sp),
                  color: ColorConstants.color181818,
                ),
                "p": Style(
                  fontWeight: FontWeight.w400,
                  fontSize: FontSize(13.sp),
                  color: ColorConstants.color959595,
                ),
                "li": Style(
                  fontWeight: FontWeight.w400,
                  fontSize: FontSize(13.sp),
                  color: ColorConstants.color959595,
                ),
              }),
            ),
          );
        },
        error: (error, stackTrace) {
          if (error.toString().contains('connection error') ||
              error.toString().contains('connection timeout')) {
            return Center(
              child: NoNetworkWidget(
                onRefresh: () => ref.invalidate(privacyPolicyProvider),
              ),
            );
          }
          return const SizedBox();
        },
        loading: () => const Center(
          child: CustomGradientSpinner(),
        ),
      ),
    );
  }
}
