import 'package:albalad_operator_app/features/home/<USER>/home_search_field.dart';
import 'package:albalad_operator_app/features/support_center/models/faq.dart';
import 'package:albalad_operator_app/features/support_center/providers.dart';
import 'package:albalad_operator_app/features/support_center/view/expansion_card.dart';
import 'package:albalad_operator_app/features/support_center/view/legal_app_bar.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/no_network_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

bool isFAQLoaded = false;

class FaqScreen extends HookConsumerWidget {
  static const route = '/faq';
  const FaqScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<List<FAQ>> faqs = ref.watch(faqsProvider);
    final filteredList = useState([]);
    final expandedUid = useState<String?>(null);
    return Scaffold(
      appBar: LegalAppBar(
        title: Text(tr(context, 'faqs')),
      ),
      body: faqs.when(
        data: (data) {
          return Builder(
            builder: (context) {
              if (!isFAQLoaded) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  filteredList.value = data;
                });
                isFAQLoaded = true;
              }
              return ListView(
                padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 20.h),
                children: [
                  HomeSearchField(
                    hintText: tr(context, 'search'),
                    suffixIconVisible: false,
                    onChanged: (p0) {
                      if (p0.isEmpty) {
                        filteredList.value = data;
                      } else {
                        filteredList.value = data
                            .where((element) =>
                                element.question
                                    ?.toLowerCase()
                                    .contains(p0.toLowerCase()) ??
                                false)
                            .toList();
                      }
                    },
                  ),
                  Gap(30.h),
                  if (filteredList.value.isEmpty)
                    Center(
                      child: Text(tr(context, 'no_data_found')),
                    )
                  else
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const PageScrollPhysics(),
                      itemBuilder: (context, index) {
                        final faq = filteredList.value[index];
                        return ExpansionCard(
                          faq: faq,
                          selectedValue: expandedUid.value,
                          onChanged: (value) {
                            if (value == expandedUid.value) {
                              expandedUid.value = null;
                              return;
                            }
                            expandedUid.value = value;
                          },
                        );
                      },
                      separatorBuilder: (context, index) {
                        return Gap(10.h);
                      },
                      itemCount: filteredList.value.length,
                    ),
                ],
              );
            },
          );
        },
        error: (error, stackTrace) {
          if (error.toString().contains('connection error') ||
              error.toString().contains('connection timeout')) {
            return Center(
              child: NoNetworkWidget(
                onRefresh: () => ref.invalidate(faqsProvider),
              ),
            );
          }
          return const SizedBox();
        },
        loading: () => const Center(
          child: CustomGradientSpinner(),
        ),
      ),
    );
  }
}
