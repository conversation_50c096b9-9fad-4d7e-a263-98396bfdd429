import 'package:albalad_operator_app/features/support_center/models/faq.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class ExpansionCard extends StatelessWidget {
  final FAQ faq;
  final void Function(String?) onChanged;
  final String? selectedValue;
  const ExpansionCard({
    required this.faq,
    required this.onChanged,
    required this.selectedValue,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    bool isExpanded = selectedValue == faq.uid;
    return InkWell(
      onTap: () => onChanged(faq.uid),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: BoxDecoration(
          border: Border.all(
            width: 1.w,
            color: ColorConstants.colorB7B7B7,
          ),
          borderRadius: BorderRadius.circular(isExpanded ? 10.r : 70.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              spreadRadius: 0,
              offset: const Offset(0, 0),
            ),
          ],
          color: isExpanded ? ColorConstants.colorF8F8F8 : Colors.white,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 16.w,
          vertical: 14,
        ),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    faq.question ?? '',
                    style: isExpanded
                        ? TextStyles.ts14w400c181818
                        : TextStyles.ts14w400c949494,
                  ),
                ),
                if (isExpanded)
                  Icon(
                    Icons.keyboard_arrow_up_rounded,
                    color: Colors.black,
                  )
                else
                  Icon(
                    Icons.keyboard_arrow_down_rounded,
                    color: ColorConstants.colorCECECE,
                  )
              ],
            ),
            AnimatedSize(
              duration: const Duration(milliseconds: 300),
              child: isExpanded
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Gap(10.h),
                        Html(
                          data: faq.answer ?? '',
                          style: {
                            "p": Style(
                              fontWeight: FontWeight.w400,
                              fontSize: FontSize(13.sp),
                              color: ColorConstants.color959595,
                            ),
                            "body": Style(
                              fontWeight: FontWeight.w400,
                              fontSize: FontSize(13.sp),
                              color: ColorConstants.color959595,
                            ),
                          },
                        ),
                        // Text(faq.answer ?? '',
                        //     style: TextStyles.ts13w400c959595)
                      ],
                    )
                  : const SizedBox(),
            ),
          ],
        ),
      ),
    );
  }
}
