import 'package:albalad_operator_app/features/authentication/widgets/contact_admin_dialog.dart';
import 'package:albalad_operator_app/features/support_center/models/contact_admin.dart';
import 'package:albalad_operator_app/features/support_center/providers.dart';
import 'package:albalad_operator_app/features/support_center/view/legal_app_bar.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/no_network_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ContactUsScreen extends ConsumerWidget {
  static const route = '/contact-us';
  const ContactUsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<ContactAdmin?> contact = ref.watch(contactAdminProvider);
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    return Scaffold(
      appBar: LegalAppBar(
        title: Text(tr(context, 'contact_us')),
      ),
      body: contact.when(
        data: (data) {
          return Padding(
            padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 30.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                InkWell(
                  onTap: () {
                    showModalBottomSheet(
                      context: context,
                      builder: (context) =>
                          ContactAdminDialog(phoneNumber: data?.text ?? ''),
                    );
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(14.r),
                      gradient: RadialGradient(
                        colors: [
                          Colors.white,
                          Colors.white,
                          ColorConstants.colorF9F9FF,
                        ],
                      ),
                      color: ColorConstants.colorF8F8F8,
                    ),
                    padding: EdgeInsets.fromLTRB(10.w, 10.h, 12.w, 10.h),
                    child: Row(
                      children: [
                        Container(
                          height: 38.h,
                          width: 40.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6.r),
                            color: ColorConstants.color94684E
                                .withValues(alpha: 0.09),
                          ),
                          alignment: Alignment.center,
                          child: SvgPicture.asset(
                            'contact'.asIconSvg(),
                            height: 18.h,
                            width: 18.w,
                          ),
                        ),
                        Gap(15.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                data?.title ?? '',
                                style: TextStyles.ts12w400cA39A9A,
                              ),
                              Text(
                                data?.text ?? '',
                                style: TextStyles.ts12w600c181818,
                                textDirection: TextDirection.ltr,
                              )
                            ],
                          ),
                        ),
                        Icon(
                          isRTL
                              ? Icons.keyboard_arrow_left_rounded
                              : Icons.keyboard_arrow_right_rounded,
                          color: ColorConstants.colorCCB5A7,
                        )
                      ],
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  'Released on 05 May 2025 v1',
                  style: TextStyles.ts10w400c878686,
                  textAlign: TextAlign.center,
                ),
                Gap(20.h),
              ],
            ),
          );
        },
        error: (error, stackTrace) {
          if (error.toString().contains('connection error') ||
              error.toString().contains('connection timeout')) {
            return Center(
              child: NoNetworkWidget(
                onRefresh: () => ref.invalidate(contactAdminProvider),
              ),
            );
          }
          return const SizedBox();
        },
        loading: () => const Center(
          child: CustomGradientSpinner(),
        ),
      ),
    );
  }
}
