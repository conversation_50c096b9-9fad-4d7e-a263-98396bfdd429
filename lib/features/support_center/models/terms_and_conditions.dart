class TermsAndConditions {
  String? title;
  String? key;
  String? text;
  String? updatedAt;

  TermsAndConditions({this.title, this.key, this.text, this.updatedAt});

  TermsAndConditions.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    key = json['key'];
    text = json['text'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['title'] = title;
    data['key'] = key;
    data['text'] = text;
    data['updated_at'] = updatedAt;
    return data;
  }
}
