class FAQ {
  String? uid;
  String? question;
  String? answer;
  String? updatedAt;

  FAQ({this.uid, this.question, this.answer, this.updatedAt});

  FAQ.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    question = json['question'];
    answer = json['answer'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['question'] = question;
    data['answer'] = answer;
    data['updated_at'] = updatedAt;
    return data;
  }
}
