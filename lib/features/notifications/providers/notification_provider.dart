import 'package:albalad_operator_app/features/notifications/model/notification_model.dart';
import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'notification_provider.g.dart';

class NotificationsState {
  final List<NotificationModel> notifications;
  final bool isLoading;
  final bool hasMore;
  final int page;
  final String? error;
  final bool isConnectionError;

  NotificationsState({
    required this.notifications,
    required this.isLoading,
    required this.hasMore,
    required this.page,
    required this.isConnectionError,
    this.error,
  });

  NotificationsState copyWith({
    List<NotificationModel>? notifications,
    bool? isLoading,
    bool? hasMore,
    int? page,
    String? error,
    bool? isConnectionError,
  }) {
    return NotificationsState(
      notifications: notifications ?? this.notifications,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      page: page ?? this.page,
      error: error ?? this.error,
      isConnectionError: isConnectionError ?? this.isConnectionError,
    );
  }
}

@riverpod
class NotificationsNotifier extends _$NotificationsNotifier {
  @override
  NotificationsState build() {
    return NotificationsState(
      notifications: [],
      isLoading: false,
      hasMore: true,
      page: 1,
      isConnectionError: false,
    );
  }

  final _dio = DioClient().dio;

  void fetchNotifications() async {
    if (state.isLoading || !state.hasMore) return;
    state = state.copyWith(isLoading: true);

    try {
      final filterMap = {
        NotificationFilter.admin: "admin",
        NotificationFilter.valet: "valet",
        NotificationFilter.clamping: "clamping",
        NotificationFilter.towing: "towing",
        NotificationFilter.releaseVehicle: "release_vehicle",
      };

      final filter = ref.watch(selectNotificationFilterProvider);

      String notificationUrl = ApiConstants.notifications;
      notificationUrl = '$notificationUrl?page=${state.page}&limit=20';
      if (filter != NotificationFilter.all) {
        notificationUrl = '$notificationUrl&filter_data=${filterMap[filter]}';
      }

      final response = await _dio.get(
        notificationUrl,
        options: Options(headers: await ApiConstants.authHeaders()),
      );

      if (response.statusCode == 200) {
        final json = response.data;
        if (json['result'] == 'success') {
          List records = json['records'];
          List<NotificationModel> notifications =
              records.map((e) => NotificationModel.fromJson(e)).toList();
          bool hasNext = json['pagination']['has_next'] == true;
          state = state.copyWith(
            notifications: [...state.notifications, ...notifications],
            isLoading: false,
            hasMore: hasNext,
            page: state.page + 1,
          );
          return;
        }
      }

      state = state.copyWith(
        isLoading: false,
        hasMore: false,
      );
    } catch (e) {
      bool isConnectionError = false;
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        isConnectionError = true;
      }
      state = state.copyWith(
        isLoading: false,
        hasMore: false,
        error: e.toString(),
        isConnectionError: isConnectionError,
      );
    }
  }

  readNotification({required String uid}) async {
    try {
      List<NotificationModel> notifications = state.notifications;
      //find notification by uid and update read_at
      notifications = notifications.map((e) {
        if (e.uid == uid) {
          return e.copyWith(hasRead: true);
        }
        return e;
      }).toList();

      state = state.copyWith(notifications: notifications);

      await _dio.patch(
        ApiConstants.readNotification,
        data: {
          "notification_uid": uid,
        },
        options: Options(headers: await ApiConstants.authHeaders()),
      );
      ref.invalidate(notificationCountProvider);
    } catch (e) {
      if (kDebugMode) print(e);
    }
  }

  reset() {
    state = state.copyWith(
      notifications: [],
      isLoading: false,
      hasMore: true,
      isConnectionError: false,
      page: 1,
    );
  }
}

// Filter options
enum NotificationFilter { all, admin, valet, clamping, towing, releaseVehicle }

final selectNotificationFilterProvider =
    StateProvider<NotificationFilter>((ref) => NotificationFilter.all);

final notificationCountProvider = FutureProvider.autoDispose<int>((ref) async {
  final dio = DioClient().dio;
  final response = await dio.get(
    ApiConstants.notificationCount,
    options: Options(headers: await ApiConstants.authHeaders()),
  );

  if (response.statusCode == 200) {
    final json = response.data;
    if (json['result'] == 'success') {
      return json['notification_count'];
    }
  }
  return 0;
});
