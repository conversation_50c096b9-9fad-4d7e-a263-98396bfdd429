import 'package:albalad_operator_app/features/notifications/providers/notification_provider.dart';
import 'package:albalad_operator_app/features/notifications/view/notification_screen.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NotificationFilterChip extends StatelessWidget {
  final NotificationFilterModel e;
  final NotificationFilter? selectedFilter;
  final ValueChanged<NotificationFilter> onSelected;
  const NotificationFilterChip(
      {required this.e,
      required this.onSelected,
      this.selectedFilter,
      super.key});

  @override
  Widget build(BuildContext context) {
    bool isSelected = selectedFilter == e.value;
    return InkWell(
      onTap: () => onSelected(e.value),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: ColorConstants.colorE1DDD2,
            width: 0.5.w,
          ),
          borderRadius: BorderRadius.circular(20.r),
          color: isSelected ? ColorConstants.primaryColor : null,
        ),
        padding: EdgeInsets.symmetric(vertical: 7.h, horizontal: 21.w),
        child: Text(
          e.title,
          style: isSelected
              ? TextStyles.ts12w600wE1DDD2
              : TextStyles.ts12w600c94684E,
        ),
      ),
    );
  }
}
