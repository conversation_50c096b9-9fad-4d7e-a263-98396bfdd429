import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SkeletonNotificationTile extends ConsumerWidget {
  const SkeletonNotificationTile({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Skeletonizer(
      enabled: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(14.r),
              gradient: RadialGradient(
                colors: [
                  Colors.white,
                  Colors.white,
                  ColorConstants.colorF9F9FF,
                ],
              ),
              color: ColorConstants.colorF8F8F8,
            ),
            padding: EdgeInsets.fromLTRB(10.w, 12.h, 16.w, 12.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Bone.square(
                  size: 40,
                  borderRadius: BorderRadius.circular(6.r),
                ),
                Gap(10.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Release Vehicle 2547 TNJ',
                        style: TextStyles.ts12w600c181818,
                      ),
                      Text(
                        'Violation ticket(ID:2342323) is settled and vehicle can release to the owner.',
                        style: TextStyles.ts12w400cA39A9A,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
