import 'package:albalad_operator_app/features/notifications/providers/notification_provider.dart';
import 'package:albalad_operator_app/features/notifications/view/notification_filter_chip.dart';
import 'package:albalad_operator_app/features/notifications/view/notification_tile.dart';
import 'package:albalad_operator_app/features/notifications/view/skeleton_notification_tile.dart';
import 'package:albalad_operator_app/features/support_center/view/legal_app_bar.dart';
import 'package:albalad_operator_app/providers/global_providers.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/not_found_widget.dart';
import 'package:albalad_operator_app/shared/widgets/smart_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart' as intl;

class NotificationScreen extends HookConsumerWidget {
  static const String route = '/notification';
  final bool? canPop;
  const NotificationScreen({this.canPop = true, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationList = [
      NotificationFilterModel(
          title: tr(context, 'all'), value: NotificationFilter.all),
      NotificationFilterModel(
          title: tr(context, 'admin'), value: NotificationFilter.admin),
      NotificationFilterModel(
          title: tr(context, 'valet'), value: NotificationFilter.valet),
      NotificationFilterModel(
          title: tr(context, 'clamping'), value: NotificationFilter.clamping),
      NotificationFilterModel(
          title: tr(context, 'towing'), value: NotificationFilter.towing),
      NotificationFilterModel(
        title: tr(context, 'release_vehicle'),
        value: NotificationFilter.releaseVehicle,
      ),
    ];

    final notifier = ref.read(notificationsNotifierProvider.notifier);
    final state = ref.watch(notificationsNotifierProvider);
    final filterNotifier = ref.read(selectNotificationFilterProvider.notifier);
    ref.listen(
      masterProvider,
      (previous, next) {
        if (next == 2) {
          filterNotifier.state = NotificationFilter.all;
          notifier.reset();
          notifier.fetchNotifications();
        }
      },
    );

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback(
        (_) {
          notifier.reset();
          notifier.fetchNotifications();
        },
      );
      return null;
    }, []);

    String? created;

    return SmartScaffold(
      isInternetAvailable: !state.isConnectionError,
      retryConnection: () {
        notifier.reset();
        notifier.fetchNotifications();
      },
      appBar: canPop == false
          ? AppBar(
              title: Text(tr(context, 'notifications')),
            )
          : LegalAppBar(
              title: Text(tr(context, 'notifications')),
            ),
      body: NotificationListener(
        onNotification: (scrollNotification) {
          if (scrollNotification is ScrollEndNotification &&
              scrollNotification.metrics.extentAfter == 0) {
            if (state.hasMore) {
              notifier.fetchNotifications();
            }
          }
          return false;
        },
        child: RefreshIndicator(
          onRefresh: () async {
            notifier.reset();
            return notifier.fetchNotifications();
          },
          child: ListView(
            padding: EdgeInsets.fromLTRB(16.w, 13.h, 16.w, 32.h),
            children: [
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  spacing: 10.w,
                  children: notificationList.map(
                    (e) {
                      final filter =
                          ref.watch(selectNotificationFilterProvider);
                      return NotificationFilterChip(
                        e: e,
                        selectedFilter: filter,
                        onSelected: (value) {
                          filterNotifier.state = value;
                          notifier.reset();
                          notifier.fetchNotifications();
                        },
                      );
                    },
                  ).toList(),
                ),
              ),
              if (state.notifications.isEmpty && state.isLoading) ...[
                ListView.separated(
                  shrinkWrap: true,
                  padding: EdgeInsets.only(top: 10.h),
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) =>
                      const SkeletonNotificationTile(),
                  separatorBuilder: (context, index) => Gap(10.h),
                  itemCount: 10,
                ),
              ] else if (state.notifications.isEmpty && !state.isLoading) ...[
                SizedBox(height: 100.h),
                NotFoundWidget(
                  title: tr(context, 'no_notifications'),
                  description: tr(context, 'no_notifications_description'),
                )
              ] else
                ListView.separated(
                  shrinkWrap: true,
                  padding: EdgeInsets.only(top: 10.h),
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    if (index == state.notifications.length) {
                      return const Center(child: CustomGradientSpinner());
                    }
                    final notification = state.notifications[index];
                    final createdAt = notification.createdAt;
                    final dateTime = DateTime.parse(createdAt!);
                    final createdLocal =
                        '${dateTime.year}-${dateTime.month}-${dateTime.day}';
                    bool showDate = false;
                    if (createdLocal != created) {
                      showDate = true;
                      created = createdLocal;
                    }

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (showDate) ...[
                          Gap(18.h),
                          Container(
                            decoration: BoxDecoration(
                              color: ColorConstants.colorEAFFEC,
                              borderRadius: BorderRadius.circular(10.r),
                            ),
                            padding: EdgeInsets.symmetric(
                              horizontal: 10.w,
                              vertical: 4.h,
                            ),
                            child: Text(
                              formatDate(dateTime, context),
                              style: TextStyles.ts12w600c32993E,
                              textDirection: TextDirection.ltr,
                            ),
                          ),
                          Gap(10.h)
                        ],
                        NotificationTile(
                          notification: notification,
                          route: route,
                        ),
                      ],
                    );
                  },
                  separatorBuilder: (context, index) => Gap(10.h),
                  itemCount:
                      state.notifications.length + (state.hasMore ? 1 : 0),
                )
            ],
          ),
        ),
      ),
    );
  }

  String formatDate(DateTime date, BuildContext context) {
    DateTime now = DateTime.now();
    DateTime today = DateTime(now.year, now.month, now.day);
    DateTime notificationDate = DateTime(date.year, date.month, date.day);
    DateTime yesterday = today.subtract(Duration(days: 1));

    if (notificationDate.isAtSameMomentAs(today)) {
      return tr(context, 'today');
    } else if (notificationDate.isAtSameMomentAs(yesterday)) {
      return tr(context, 'yesterday');
    } else {
      return intl.DateFormat('dd MMM yyyy').format(notificationDate);
    }
  }
}

class NotificationFilterModel {
  final String title;
  final NotificationFilter value;
  NotificationFilterModel({
    required this.title,
    required this.value,
  });
}
