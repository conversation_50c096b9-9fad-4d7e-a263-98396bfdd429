import 'package:albalad_operator_app/features/clamp_violation/view/clamp_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/clamped_details_screen.dart';
import 'package:albalad_operator_app/features/notifications/model/notification_model.dart';
import 'package:albalad_operator_app/features/notifications/providers/notification_provider.dart';
import 'package:albalad_operator_app/features/profile/view/my_profile_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/tow_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/towed_details_screen.dart';
import 'package:albalad_operator_app/features/valet/view/generate_valet_ticket_screen.dart';
import 'package:albalad_operator_app/features/valet/view/valet_operations_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/providers/global_providers.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:easy_date_formatter/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get_time_ago/get_time_ago.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class NotificationTile extends ConsumerWidget {
  final NotificationModel notification;
  final String route;
  const NotificationTile({
    required this.notification,
    required this.route,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final locale = ref.watch(localeProvider);
    DateTime dateTime = DateTime.parse(notification.createdAt ?? '');

    return InkWell(
      onTap: () {
        handleNotification(
          notificationUid: notification.uid ?? '',
          action: notification.action ?? '',
          actionUid: notification.actionId,
          context: context,
          ref: ref,
        );
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(14.r),
          gradient: notification.hasRead == true
              ? null
              : RadialGradient(
                  colors: [
                    Colors.white,
                    Colors.white,
                    ColorConstants.colorF9F9FF,
                  ],
                ),
          color:
              notification.hasRead == true ? null : ColorConstants.colorF8F8F8,
        ),
        padding: EdgeInsets.fromLTRB(10.w, 12.h, 16.w, 12.w),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.r),
                color: ColorConstants.color94684E.withValues(alpha: 0.09),
              ),
              width: 40.w,
              height: 38.h,
              alignment: Alignment.center,
              child: getIcon(
                notification.type ?? '',
                isAdmin: notification.isSenderAdmin ?? false,
              ),
            ),
            Gap(10.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    notification.title ?? '',
                    style: TextStyles.ts12w600c181818,
                  ),
                  Text(
                    notification.message ?? '',
                    style: TextStyles.ts12w400cA39A9A,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Localizations.override(
                        context: context,
                        locale: Locale('en'),
                        child: Text(
                          // GetTimeAgo.parse(dateTime, locale: locale.languageCode),
                          getTime(dateTime, locale.languageCode),
                          style: TextStyles.ts10w400c878686,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String getTime(DateTime dateTime, String locale) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final notificationDate =
        DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (notificationDate.isAtSameMomentAs(today)) {
      return GetTimeAgo.parse(dateTime);
    }
    return DateFormatter.formatDateTime(
      dateTime: dateTime,
      outputFormat: 'hh:mm a',
    );
  }

  handleNotification({
    required String notificationUid,
    required String action,
    required BuildContext context,
    required WidgetRef ref,
    String? actionUid,
  }) {
    final notifier = ref.read(notificationsNotifierProvider.notifier);
    notifier.readNotification(
      uid: notificationUid,
    );
    switch (action) {
      case 'valet_settled_operator':
      case 'assign_valet':
      case 'vehicle_request_operator':
        final arguments = GenerateValetTicketScreen(
          previousRoute: route,
          valetUid: actionUid ?? '',
        );
        Navigator.pushNamed(
          context,
          GenerateValetTicketScreen.route,
          arguments: arguments,
        );
        break;
      case 'valet_booking_operator':
        Navigator.pushNamed(context, ValetOperationsScreen.route);
        break;
      case 'clamping_assigned_operator':
        final arguments = ClampVehicleListingScreen(tabIndex: 0);
        Navigator.pushNamed(
          context,
          ClampVehicleListingScreen.route,
          arguments: arguments,
        );
        break;
      case 'clamped_vehicle_operator':
        final arguments = VehicleDetailsScreen(
          uid: actionUid ?? '',
          previousRoute: route,
        );
        Navigator.pushNamed(
          context,
          VehicleDetailsScreen.route,
          arguments: arguments,
        );
        break;
      case 'clamp_violation_settled_operator':
        final arguments = ClampedDetailsScreen(
          uid: actionUid ?? '',
          previousRoute: route,
        );
        Navigator.pushNamed(
          context,
          ClampedDetailsScreen.route,
          arguments: arguments,
        );
        break;
      case 'towing_assigned_operator':
        final arguments = TowVehicleListingScreen(tabIndex: 0);
        Navigator.pushNamed(
          context,
          TowVehicleListingScreen.route,
          arguments: arguments,
        );
        break;
      case 'towed_vehicle_operator':
        final arguments = VehicleDetailsScreen(
          uid: actionUid ?? '',
          previousRoute: route,
        );
        Navigator.pushNamed(
          context,
          VehicleDetailsScreen.route,
          arguments: arguments,
        );
        break;
      case 'tow_violation_settled_operator':
        final arguments = TowedDetailsScreen(
          uid: actionUid ?? '',
          previousRoute: route,
        );
        Navigator.pushNamed(
          context,
          TowedDetailsScreen.route,
          arguments: arguments,
        );
        break;
      case 'user_profile_update':
        Navigator.pushNamed(context, MyProfileScreen.route);
        break;
      case 'parking_extension_operator_alert':
        final arguments = VehicleDetailsScreen(
          uid: actionUid ?? '',
          previousRoute: route,
        );
        Navigator.pushNamed(
          context,
          VehicleDetailsScreen.route,
          arguments: arguments,
        );
        break;
    }
  }

  Widget getIcon(String type, {bool isAdmin = false}) {
    if (type.contains('admin') || isAdmin) {
      return SvgPicture.asset('admin-user'.asIconSvg());
    } else if (type.contains('valet')) {
      return SvgPicture.asset('receipt-minus'.asIconSvg());
    }
    return SvgPicture.asset('car'.asIconSvg());
  }
}
