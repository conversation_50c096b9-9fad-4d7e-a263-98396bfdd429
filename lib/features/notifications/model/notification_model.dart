class NotificationModel {
  String? uid;
  String? title;
  String? message;
  String? action;
  String? actionId;
  String? type;
  String? status;
  bool? hasRead;
  bool? isSenderAdmin;
  String? createdAt;

  NotificationModel({
    this.uid,
    this.title,
    this.message,
    this.action,
    this.actionId,
    this.type,
    this.status,
    this.hasRead,
    this.isSenderAdmin,
    this.createdAt,
  });

  NotificationModel.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    title = json['title'];
    message = json['message'];
    action = json['action'];
    actionId = json['action_id'];
    type = json['type'];
    status = json['status'];
    hasRead = json['has_read'];
    isSenderAdmin = json['is_sender_admin'];
    createdAt = json['created_at'];
  }

  NotificationModel copyWith({
    String? uid,
    String? title,
    String? message,
    String? action,
    String? actionId,
    String? type,
    String? status,
    bool? hasRead,
    bool? isSenderAdmin,
    String? createdAt,
  }) {
    return NotificationModel(
      uid: uid ?? this.uid,
      title: title ?? this.title,
      message: message ?? this.message,
      action: action ?? this.action,
      actionId: actionId ?? this.actionId,
      type: type ?? this.type,
      status: status ?? this.status,
      hasRead: hasRead ?? this.hasRead,
      isSenderAdmin: isSenderAdmin ?? this.isSenderAdmin,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['title'] = title;
    data['message'] = message;
    data['action'] = action;
    data['action_id'] = actionId;
    data['type'] = type;
    data['status'] = status;
    data['has_read'] = hasRead;
    data['is_sender_admin'] = isSenderAdmin;
    data['created_at'] = createdAt;
    return data;
  }
}
