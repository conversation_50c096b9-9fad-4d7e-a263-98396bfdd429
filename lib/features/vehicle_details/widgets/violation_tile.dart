import 'package:albalad_operator_app/features/violation/view/violation_details_screen.dart';
import 'package:albalad_operator_app/providers/permission_providers.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/constants/violation_request_type.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/models/vehicle_details.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class ViolationTile extends ConsumerWidget {
  final ViolationDetails violationDetails;
  final String previousRoute;
  final String? vehicleUid;
  const ViolationTile(
      {required this.violationDetails,
      required this.previousRoute,
      this.vehicleUid,
      super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    List<String> permissions = ref.watch(permissionsProvider);
    return InkWell(
      onTap: () async {
        int? permissionId = violationDetails.violationRequestType ?? 0;
        String permission = ViolationRequestType.getLabel(permissionId);
        bool hasPermission = false;
        if (permission.toLowerCase() == 'parking') {
          const validParkingRoles = {
            'operator',
            'clamp',
            'tow',
          };
          hasPermission = permissions.any(validParkingRoles.contains);
        } else {
          hasPermission = permissions.contains(permission.toLowerCase());
        }

        if (hasPermission) {
          final args = ViolationDetailsScreen(
            violationUid: violationDetails.uid ?? '',
            vehicleUid: vehicleUid,
            previousRoute: previousRoute,
          );
          Navigator.pushNamed(context, ViolationDetailsScreen.route,
              arguments: args);
        } else {
          showPermissionErrorMessage(
            context: context,
            message:
                tr(context, 'violation_details_view_permission_error_message'),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: Colors.white,
          border: Border.all(
            width: 1.w,
            color: ColorConstants.colorEAEAEA,
          ),
        ),
        padding: EdgeInsets.symmetric(vertical: 18.h, horizontal: 16.w),
        child: Column(
          children: [
            Row(
              children: [
                Text.rich(
                  TextSpan(
                    text: tr(context, 'id'),
                    style: TextStyles.ts12w400c4D4D4D,
                    children: [
                      TextSpan(
                        text: ' ${violationDetails.violationId ?? ''}',
                        style: TextStyles.ts12w700c353535,
                      ),
                    ],
                  ),
                ),
                Gap(9.w),
                SizedBox(
                  height: 15.h,
                  child: VerticalDivider(
                    color: ColorConstants.colorF1F1F1,
                    thickness: 1.w,
                    width: 0,
                  ),
                ),
                Gap(9.w),
                Expanded(
                  child: Text(
                    violationDetails.violationType ?? '',
                    style: TextStyles.ts12w400c4D4D4D,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Gap(9.w),
                Container(
                  decoration: BoxDecoration(
                    color: ColorConstants.colorFFF7E2,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: 10.w,
                    vertical: 2.h,
                  ),
                  child: Text(
                    violationDetails.paymentStatus ?? '',
                    style: TextStyles.ts12w600cE8B020,
                  ),
                ),
              ],
            ),
            Gap(16.h),
            Divider(
              color: ColorConstants.colorF1F1F1,
              thickness: 1.w,
              height: 0,
            ),
            Gap(16.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  tr(context, 'violation_date_and_time'),
                  style: TextStyles.ts12w400c4D4D4D,
                ),
                Row(
                  children: [
                    SvgPicture.asset(
                      'clock'.asIconSvg(),
                      height: 14.h,
                      width: 14.w,
                    ),
                    Gap(5.w),
                    Text(
                      violationDetails.violationDateTime ?? '',
                      style: TextStyles.ts12w400c4D4D4D,
                      textDirection: TextDirection.ltr,
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
