import 'dart:async';

import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/circular_progress_with_thumb_painter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class VehicleParkingTimer extends StatefulWidget {
  final int totalTimeInSeconds;
  final int remainingTimeInSeconds;
  final VoidCallback? onTimerEnd;
  const VehicleParkingTimer({
    required this.totalTimeInSeconds,
    required this.remainingTimeInSeconds,
    this.onTimerEnd,
    super.key,
  });

  @override
  State<VehicleParkingTimer> createState() => _VehicleParkingTimerState();
}

class _VehicleParkingTimerState extends State<VehicleParkingTimer> {
  Timer? _timer;
  double progress = 0.0; // Progress value (0.0 to 1.0)
  int totalTimeInSeconds = 0; // Total time for the timer (in seconds)
  int remainingTimeInSeconds = 0; // Remaining time (in seconds)
  bool isTimerRunning = true;

  @override
  void initState() {
    super.initState();
    totalTimeInSeconds = widget.totalTimeInSeconds;
    remainingTimeInSeconds = widget.remainingTimeInSeconds;
    if (totalTimeInSeconds != 0) {
      progress = 1.0 - remainingTimeInSeconds / totalTimeInSeconds;
      startTimer();
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (remainingTimeInSeconds > 0) {
          remainingTimeInSeconds--;
          progress = 1.0 - remainingTimeInSeconds / totalTimeInSeconds;
        } else {
          if (widget.onTimerEnd != null) {
            widget.onTimerEnd!();
          }
          isTimerRunning = false;
          timer.cancel();
        }
      });
    });
  }

  String formatTime(int seconds) {
    int hours = seconds ~/ 3600;
    int minutes = (seconds % 3600) ~/ 60;
    int secs = seconds % 60;
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    if (totalTimeInSeconds == 0 || !isTimerRunning) {
      return const SizedBox();
    }
    return Column(
      children: [
        CustomPaint(
          size: Size(63.w, 63.h),
          painter: CircularProgressWithThumbPainter(progress: progress),
          child: SizedBox(
            width: 63.w,
            height: 63.h,
            child: Center(
              child: Image.asset(
                'car'.asImagePng(),
                width: 49.w,
              ),
            ),
          ),
        ),
        Gap(10.h),
        Text(
          formatTime(remainingTimeInSeconds),
          style: TextStyle(
            fontSize: 13.sp,
            fontWeight: FontWeight.w700,
            color: progress == 1
                ? const Color(0xFFF50000)
                : const Color(0xFF32993E),
          ),
        )
      ],
    );
  }
}
