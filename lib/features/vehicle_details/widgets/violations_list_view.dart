import 'package:albalad_operator_app/features/vehicle_details/widgets/violation_tile.dart';
import 'package:albalad_operator_app/shared/models/vehicle_details.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class ViolationsListView extends StatelessWidget {
  final List<ViolationDetails> violations;
  const ViolationsListView({required this.violations, super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      itemBuilder: (context, index) {
        return ViolationTile(
          violationDetails: violations[index],
          previousRoute: '',
        );
      },
      separatorBuilder: (context, index) => Gap(10.h),
      itemCount: violations.length,
    );
  }
}
