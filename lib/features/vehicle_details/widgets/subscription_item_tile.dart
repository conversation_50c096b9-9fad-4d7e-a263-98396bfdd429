import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class SubscriptionItemTile extends StatelessWidget {
  final String name;
  final Widget? child;
  final String? label;
  const SubscriptionItemTile(
      {required this.name, this.child, this.label, super.key})
      : assert(child != null || label != null,
            'Either child or label must be provided');

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            name,
            style: TextStyles.ts12w400c4D4D4D,
          ),
        ),
        Gap(20.w),
        if (label != null)
          Expanded(
            child: Text(
              label ?? '',
              style: TextStyles.ts12w400c4D4D4D,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.end,
            ),
          )
        else
          child!,
      ],
    );
  }
}
