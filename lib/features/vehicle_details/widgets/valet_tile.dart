import 'package:albalad_operator_app/features/valet/view/assign_valet_screen.dart';
import 'package:albalad_operator_app/features/valet/view/generate_valet_ticket_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/providers/permission_providers.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/models/vehicle_details.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ValetTile extends ConsumerWidget {
  final ValetDetails valetDetails;
  final String previousRoute;
  final String? vehicleUid;
  final String? vehicleId;
  final String? vehicleNumber;
  final String? vehicleName;
  final String? vehicleImage;
  final String? makeYear;
  final String? vehicleType;
  final String? numberPlateType;
  const ValetTile({
    super.key,
    required this.valetDetails,
    required this.previousRoute,
    this.vehicleId,
    this.vehicleNumber,
    this.vehicleName,
    this.vehicleImage,
    this.makeYear,
    this.vehicleType,
    this.numberPlateType,
    this.vehicleUid,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    bool hasPermission = ref.watch(valetPermissionProvider);
    if (valetDetails.valetPerson == null) {
      return AssignValetTile(
        valetDetails: valetDetails,
        vehicleUid: vehicleUid,
        vehicleId: vehicleId,
        vehicleNumber: vehicleNumber,
        vehicleName: vehicleName,
        vehicleImage: vehicleImage,
        makeYear: makeYear,
        vehicleType: vehicleType,
        numberPlateType: numberPlateType,
      );
    }
    return InkWell(
      onTap: () {
        if (hasPermission) {
          Navigator.pushNamed(
            context,
            GenerateValetTicketScreen.route,
            arguments: GenerateValetTicketScreen(
              previousRoute: previousRoute,
              valetUid: valetDetails.uid ?? '',
              vehicleUid: vehicleUid ?? '',
            ),
          );
        } else {
          showPermissionErrorMessage(
            context: context,
            message: tr(
                context, 'valet_booking_details_view_permission_error_message'),
          );
        }
      },
      splashFactory: NoSplash.splashFactory,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: Colors.white,
          border: Border.all(
            width: 1.w,
            color: ColorConstants.colorEAEAEA,
          ),
        ),
        padding: EdgeInsets.symmetric(vertical: 18.h, horizontal: 16.w),
        child: Column(
          children: [
            Row(
              children: [
                Text.rich(
                  TextSpan(
                    text: tr(context, 'id'),
                    style: TextStyles.ts12w400c4D4D4D,
                    children: [
                      TextSpan(
                        text: ' ${valetDetails.valetId}',
                        style: TextStyles.ts12w700c353535,
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                if (valetDetails.valetTime != null)
                  Row(
                    children: [
                      SvgPicture.asset(
                        'clock'.asIconSvg(),
                        height: 14.h,
                        width: 14.w,
                      ),
                      Gap(5.w),
                      Text(
                        valetDetails.valetTime!,
                        style: TextStyles.ts12w400c4D4D4D,
                        textDirection: TextDirection.ltr,
                      ),
                    ],
                  ),
              ],
            ),
            Gap(16.h),
            Divider(
              color: ColorConstants.colorF1F1F1,
              thickness: 1.w,
              height: 0,
            ),
            Gap(16.h),
            Row(
              children: [
                ClipOval(
                  child: CachedNetworkImage(
                    imageUrl:
                        valetDetails.valetPersonImage?.image ?? 'https://',
                    width: 40.w,
                    height: 40.h,
                    fit: BoxFit.cover,
                    errorWidget: (context, url, error) {
                      return CircleAvatar(
                        backgroundImage: const AssetImage(
                          'assets/icons/profile_picture.png',
                        ), // Replace with your image path
                        radius: 20.w,
                      );
                    },
                  ),
                ),
                Gap(5.w),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        valetDetails.valetPerson ?? '',
                        style: TextStyles.ts16w600c44322D,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        tr(context, 'valet_person'),
                        style: TextStyles.ts12w400c606060,
                      )
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class AssignValetTile extends StatelessWidget {
  final ValetDetails valetDetails;
  final String? vehicleUid;
  final String? vehicleId;
  final String? vehicleNumber;
  final String? vehicleName;
  final String? vehicleImage;
  final String? makeYear;
  final String? vehicleType;
  final String? numberPlateType;
  const AssignValetTile({
    required this.valetDetails,
    this.vehicleId,
    this.vehicleNumber,
    this.vehicleName,
    this.vehicleImage,
    this.makeYear,
    this.vehicleType,
    this.numberPlateType,
    this.vehicleUid,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        color: Colors.white,
        border: Border.all(
          width: 1.w,
          color: ColorConstants.colorEAEAEA,
        ),
      ),
      padding: EdgeInsets.symmetric(vertical: 18.h, horizontal: 16.w),
      child: Column(
        children: [
          Row(
            children: [
              Text.rich(
                TextSpan(
                  text: tr(context, 'id'),
                  style: TextStyles.ts12w400c4D4D4D,
                  children: [
                    TextSpan(
                      text: ' ${valetDetails.valetId}',
                      style: TextStyles.ts12w700c353535,
                    ),
                  ],
                ),
              ),
              const Spacer(),
              if (valetDetails.bookingTime != null)
                Row(
                  children: [
                    SvgPicture.asset(
                      'clock'.asIconSvg(),
                      height: 14.h,
                      width: 14.w,
                    ),
                    Gap(5.w),
                    Text(
                      valetDetails.bookingTime!,
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                  ],
                ),
            ],
          ),
          Gap(16.h),
          Divider(
            color: ColorConstants.colorF1F1F1,
            thickness: 1.w,
            height: 0,
          ),
          Gap(16.h),
          Text(
            tr(context, 'no_valet_assigned'),
            style: TextStyles.ts12w400c4D4D4D,
          ),
          // Gap(5.h),
          ElevatedButton(
            onPressed: () {
              final arguments = AssignValetScreen(
                vehicleUid: vehicleUid,
                vehicleId: vehicleId,
                vehicleNumber: vehicleNumber,
                vehicleName: vehicleName,
                vehicleImage: vehicleImage,
                makeYear: makeYear,
                vehicleType: vehicleType,
                numberPlateType: numberPlateType,
                previousRoute: VehicleDetailsScreen.route,
                valetRequestUid: valetDetails.uid,
              );
              Navigator.pushNamed(
                context,
                AssignValetScreen.route,
                arguments: arguments,
              );
            },
            style: ElevatedButton.styleFrom(
              minimumSize: Size(84.w, 22.h),
              textStyle: TextStyles.ts12w600wE1DDD2,
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
            ),
            child: Text(tr(context, 'assign_valet')),
          )
        ],
      ),
    );
  }
}
