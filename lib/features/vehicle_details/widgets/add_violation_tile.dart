import 'package:albalad_operator_app/features/clamp_violation/view/assign_direct_clamping_screen.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/clamp_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/assign_direct_towing_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/tow_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/features/violation/view/assign_violation_screen.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/add_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class AddViolationTile extends StatelessWidget {
  final String vehicleUid;
  final String? previousRoute;
  const AddViolationTile(
      {required this.vehicleUid, this.previousRoute, super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // Navigation logic based on the previous route
        // Handles different scenarios for vehicle-related actions

        // If coming from the clamp vehicle listing screen
        if (previousRoute == ClampVehicleListingScreen.route) {
          // Navigate to direct clamping assignment screen
          Navigator.pushNamed(
            context,
            AssignDirectClampingScreen.route,
            arguments: AssignDirectClampingScreen(
              vehicleUid: vehicleUid,
              // Fallback to VehicleDetailsScreen route if previousRoute is null
              previousRoute: previousRoute ?? VehicleDetailsScreen.route,
            ),
          );
          // If coming from the tow vehicle listing screen
        } else if (previousRoute == TowVehicleListingScreen.route) {
          // Navigate to direct towing assignment screen
          Navigator.pushNamed(
            context,
            AssignDirectTowingScreen.route,
            arguments: AssignDirectTowingScreen(
              vehicleUid: vehicleUid,
              // Fallback to VehicleDetailsScreen route if previousRoute is null
              previousRoute: previousRoute ?? VehicleDetailsScreen.route,
            ),
          );
          // Default case - if coming from any other screen
        } else {
          // Navigate to the general violation assignment screen
          Navigator.pushNamed(
            context,
            AssignViolationScreen.route,
            arguments: AssignViolationScreen(
              vehicleUid: vehicleUid,
            ),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: Colors.white,
          border: Border.all(
            width: 1.w,
            color: ColorConstants.colorEAEAEA,
          ),
        ),
        padding: EdgeInsets.symmetric(vertical: 17.h),
        child: Column(
          children: [
            Text(
              tr(context, 'no_violation_assigned'),
              style: TextStyles.ts12w400c4D4D4D,
            ),
            Gap(5.h),
            const AddButton(),
          ],
        ),
      ),
    );
  }
}
