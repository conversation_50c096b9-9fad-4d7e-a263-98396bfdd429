import 'package:albalad_operator_app/features/vehicle_details/widgets/subscription_item_tile.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/models/vehicle_details.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class SubscriptionDetailsWidget extends StatelessWidget {
  final SubscriptionModel subscription;
  const SubscriptionDetailsWidget({required this.subscription, super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Gap(20.h),
        Text(
          tr(context, 'subscriptionDetails'),
          style: TextStyles.ts14w500c353535,
        ),
        Gap(12.h),
        SubscriptionItemTile(
          name: tr(context, 'subscriptionName'),
          label: subscription.subscriptionName ?? '',
        ),
        Gap(10.h),
        SubscriptionItemTile(
          name: tr(context, 'location'),
          label: subscription.location ?? '',
        ),
        Gap(10.h),
        SubscriptionItemTile(
          name: tr(context, 'type'),
          label: subscription.type ?? '',
        ),
        Gap(10.h),
        SubscriptionItemTile(
          name: tr(context, 'startAndEndTime'),
          child: Row(
            children: [
              SvgPicture.asset(
                'clock'.asIconSvg(),
                height: 14.h,
                width: 14.w,
              ),
              Gap(5.w),
              Text(
                subscription.subscriptionTime ?? '',
                style: TextStyles.ts12w400c4D4D4D,
                textDirection: TextDirection.ltr,
              ),
            ],
          ),
        ),
        Gap(10.h),
        SubscriptionItemTile(
          name: tr(context, 'expiryDate'),
          child: Text(
            subscription.expiryDate ?? '',
            style: TextStyles.ts12w400c4D4D4D,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.end,
            textDirection: TextDirection.ltr,
          ),
        ),
      ],
    );
  }
}
