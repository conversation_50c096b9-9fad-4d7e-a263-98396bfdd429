import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SkeletonViolationTile extends ConsumerWidget {
  const SkeletonViolationTile({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Skeletonizer(
      enabled: true,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: Colors.white,
          border: Border.all(
            width: 1.w,
            color: ColorConstants.colorEAEAEA,
          ),
        ),
        padding: EdgeInsets.symmetric(vertical: 18.h, horizontal: 16.w),
        child: Column(
          children: [
            Row(
              children: [
                Text.rich(
                  TextSpan(
                    text: tr(context, 'id'),
                    style: TextStyles.ts12w400c4D4D4D,
                    children: [
                      TextSpan(
                        text: ' 2586547',
                        style: TextStyles.ts12w700c353535,
                      ),
                    ],
                  ),
                ),
                Gap(9.w),
                SizedBox(
                  height: 15.h,
                  child: VerticalDivider(
                    color: ColorConstants.colorF1F1F1,
                    thickness: 1.w,
                    width: 0,
                  ),
                ),
                Gap(9.w),
                Text(
                  'Parking Violation',
                  style: TextStyles.ts12w400c4D4D4D,
                ),
                const Spacer(),
                Container(
                  decoration: BoxDecoration(
                    color: ColorConstants.colorFFF7E2,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: 10.w,
                    vertical: 2.h,
                  ),
                  child: Text(
                    'Not Settled',
                    style: TextStyles.ts12w500cE8B020,
                  ),
                ),
              ],
            ),
            Gap(16.h),
            Divider(
              color: ColorConstants.colorF1F1F1,
              thickness: 1.w,
              height: 0,
            ),
            Gap(16.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  tr(context, 'violation_date_and_time'),
                  style: TextStyles.ts12w400c4D4D4D,
                ),
                Row(
                  children: [
                    SvgPicture.asset(
                      'clock'.asIconSvg(),
                      height: 14.h,
                      width: 14.w,
                    ),
                    Gap(5.w),
                    Text(
                      '20-11-2024, 03:30 PM',
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
