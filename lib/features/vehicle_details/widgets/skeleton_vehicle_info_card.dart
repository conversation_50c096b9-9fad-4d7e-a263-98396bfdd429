import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/vehicle_info_chip.dart';
import 'package:albalad_operator_app/features/vehicle_details/widgets/vehicle_parking_timer.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SkeletonVehicleInfoCard extends StatelessWidget {
  const SkeletonVehicleInfoCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              offset: const Offset(0, 0),
              blurRadius: 20,
              spreadRadius: 0,
            ),
          ],
        ),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text.rich(
                      TextSpan(
                        text: tr(context, 'id'),
                        style: TextStyles.ts12w400c4D4D4D,
                        children: [
                          TextSpan(
                            text: ' 2586547',
                            style: TextStyles.ts12w700c353535,
                          ),
                        ],
                      ),
                    ),
                    Gap(10.h),
                    Row(
                      children: [
                        Container(
                          height: 35.h,
                          width: 35.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            color: ColorConstants.colorF1EFE9,
                          ),
                          child: CachedNetworkImage(
                            imageUrl: '',
                            memCacheWidth: 100,
                            errorWidget: (context, url, error) => Image.asset(
                              'car'.asImagePng(),
                            ),
                          ),
                        ),
                        Gap(10.w),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '7403 RUA',
                              style: TextStyles.ts14w600c181818,
                            ),
                            Text(
                              'Toyota Camry',
                              style: TextStyles.ts10w400cA1A09B,
                            ),
                          ],
                        ),
                      ],
                    ),
                    Gap(16.h),
                    Row(
                      spacing: 8.w,
                      children: [
                        VehicleInfoChip(
                          title: '2002',
                        ),
                        VehicleInfoChip(
                          title: 'Sedan',
                        ),
                        VehicleInfoChip(
                          title: tr(context, 'shared'),
                          shared: true,
                        ),
                        VehicleInfoChip(
                          title: 'Private',
                        ),
                      ],
                    ),
                  ],
                ),
                VehicleParkingTimer(
                  totalTimeInSeconds: 0,
                  remainingTimeInSeconds: 0,
                ),
              ],
            ),
            Gap(16.h),
            Divider(
              color: ColorConstants.colorF1F1F1,
              thickness: 1.h,
              height: 0,
            ),
            Gap(10.h),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              spacing: 40.w,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tr(context, 'checkInTime'),
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                    Text(
                      '03.30 PM',
                      style: TextStyles.ts12w600c353535,
                    )
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tr(context, 'totalHrs'),
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                    Text(
                      '3 Hr',
                      style: TextStyles.ts12w600c353535,
                    )
                  ],
                ),
              ],
            ),
            Gap(25.h),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                CircleAvatar(
                  backgroundImage: const AssetImage(
                    'assets/icons/profile_picture.png',
                  ), // Replace with your image path
                  radius: 20.w,
                ),
                Gap(5.w),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Abdullah Obaid',
                        style: TextStyles.ts16w500c44322D,
                      ),
                      Row(
                        children: [
                          Text(
                            tr(context, 'owner'),
                            style: TextStyles.ts12w400c606060,
                          ),
                          const Spacer(),
                          Row(
                            children: [
                              SvgPicture.asset(
                                'call'.asIconSvg(),
                                height: 17.h,
                                width: 17.w,
                              ),
                              Gap(3.w),
                              Text(
                                '+966 45 698 5238',
                                style: TextStyles.ts12w600c353535,
                              ),
                            ],
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ],
            ),
            // const SubscriptionDetailsWidget(),
          ],
        ),
      ),
    );
  }
}
