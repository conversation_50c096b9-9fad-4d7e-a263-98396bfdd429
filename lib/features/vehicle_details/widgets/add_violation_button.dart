import 'dart:async';

import 'package:albalad_operator_app/shared/widgets/add_button.dart';
import 'package:flutter/material.dart';

class AddViolationButton extends StatefulWidget {
  final String? gracePeriodEndDateTime;
  final int? totalGracePeriodSeconds;
  final bool? isGracePeriodActive;
  final Function()? onPressed;
  const AddViolationButton({
    this.gracePeriodEndDateTime,
    this.totalGracePeriodSeconds,
    this.isGracePeriodActive,
    super.key,
    required this.onPressed,
  });

  @override
  State<AddViolationButton> createState() => _AddViolationButtonState();
}

class _AddViolationButtonState extends State<AddViolationButton> {
  DateTime? gracePeriodEndDateTime; // Received from API
  int totalGracePeriodSeconds = 0; // Received from API
  Timer? _timer;
  int remainingSeconds = 0;
  bool isGracePeriodActive = true;

  @override
  void initState() {
    super.initState();
    if (widget.gracePeriodEndDateTime != null &&
        widget.totalGracePeriodSeconds != null) {
      gracePeriodEndDateTime = DateTime.parse(widget.gracePeriodEndDateTime!);
      totalGracePeriodSeconds = widget.totalGracePeriodSeconds!;
      startGracePeriodTimer();
    }
  }

  void startGracePeriodTimer() {
    if (gracePeriodEndDateTime != null) {
      _timer = Timer.periodic(Duration(seconds: 1), (timer) {
        final now = DateTime.now().toUtc();
        final difference = gracePeriodEndDateTime!.difference(now);

        setState(() {
          remainingSeconds = difference.inSeconds;
          if (remainingSeconds <= 0) {
            isGracePeriodActive = false;
            timer.cancel();
          }
        });
      });
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (isGracePeriodActive && widget.isGracePeriodActive != true) {
      return const SizedBox();
    }
    return AddButton(
      onTap: widget.onPressed,
    );
  }
}
