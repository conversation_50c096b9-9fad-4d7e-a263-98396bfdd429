import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SkeletonValetTile extends StatelessWidget {
  const SkeletonValetTile({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: Colors.white,
          border: Border.all(
            width: 1.w,
            color: ColorConstants.colorEAEAEA,
          ),
        ),
        padding: EdgeInsets.symmetric(vertical: 18.h, horizontal: 16.w),
        child: Column(
          children: [
            Row(
              children: [
                Text.rich(
                  TextSpan(
                    text: tr(context, 'id'),
                    style: TextStyles.ts12w400c4D4D4D,
                    children: [
                      TextSpan(
                        text: ' 2586547',
                        style: TextStyles.ts12w700c353535,
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                Row(
                  children: [
                    SvgPicture.asset(
                      'clock'.asIconSvg(),
                      height: 14.h,
                      width: 14.w,
                    ),
                    Gap(5.w),
                    Text(
                      '03:30 PM',
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                  ],
                ),
              ],
            ),
            Gap(16.h),
            Divider(
              color: ColorConstants.colorF1F1F1,
              thickness: 1.w,
              height: 0,
            ),
            Gap(16.h),
            Row(
              children: [
                CircleAvatar(
                  backgroundImage: const AssetImage(
                    'assets/icons/profile_picture.png',
                  ), // Replace with your image path
                  radius: 20.w,
                ),
                Gap(5.w),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Mohammed Al-Rifai',
                      style: TextStyles.ts16w500c44322D,
                    ),
                    Text(
                      'Valet Person',
                      style: TextStyles.ts12w400c606060,
                    )
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
