import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/vehicle_info_chip.dart';
import 'package:albalad_operator_app/features/vehicle_details/provider/vehicle_details_provider.dart';
import 'package:albalad_operator_app/features/vehicle_details/widgets/subscription_details_widget.dart';
import 'package:albalad_operator_app/features/vehicle_details/widgets/vehicle_parking_timer.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/models/vehicle_details.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class VehicleInfoCard extends ConsumerWidget {
  final VehicleDetails vehicleDetails;
  const VehicleInfoCard({required this.vehicleDetails, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final owner = vehicleDetails.ownerDetails;
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, 0),
            blurRadius: 20,
            spreadRadius: 0,
          ),
        ],
      ),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (vehicleDetails.vehicleId != null)
                      Text.rich(
                        TextSpan(
                          text: tr(context, 'id'),
                          style: TextStyles.ts12w400c4D4D4D,
                          children: [
                            TextSpan(
                              text: ' ${vehicleDetails.vehicleId}',
                              style: TextStyles.ts12w700c353535,
                            ),
                          ],
                        ),
                      ),
                    Gap(10.h),
                    Row(
                      children: [
                        Container(
                          height: 35.h,
                          width: 35.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            color: ColorConstants.colorF1EFE9,
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8.r),
                            child: CachedNetworkImage(
                              imageUrl: vehicleDetails
                                      .vehicleImage?.firstOrNull?.image ??
                                  '',
                              memCacheWidth: 200,
                              fit: BoxFit.fill,
                              errorWidget: (context, url, error) => Image.asset(
                                'car'.asImagePng(),
                              ),
                            ),
                          ),
                        ),
                        Gap(10.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                vehicleDetails.number ?? '',
                                style: TextStyles.ts14w600c181818,
                              ),
                              Text(
                                vehicleDetails.vehicleName ?? '',
                                style: TextStyles.ts10w400cA1A09B,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    Gap(16.h),
                    Wrap(
                      spacing: 8.w,
                      runSpacing: 8.h,
                      children: [
                        if (vehicleDetails.makeYear != null)
                          VehicleInfoChip(
                            title: '${vehicleDetails.makeYear}',
                          ),
                        if (vehicleDetails.vehicleType != null)
                          VehicleInfoChip(
                            title: vehicleDetails.vehicleType ?? '',
                          ),
                        if (vehicleDetails.isSharedVehicle == true)
                          VehicleInfoChip(
                            title: tr(context, 'shared'),
                            shared: true,
                          ),
                        if (vehicleDetails.numberPlateType != null)
                          VehicleInfoChip(
                            title: vehicleDetails.numberPlateType ?? '',
                          ),
                      ],
                    ),
                  ],
                ),
              ),
              Gap(20.w),
              if (vehicleDetails.coorparateVehicle != null)
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: ColorConstants.colorEAFFEC,
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: 10.w,
                    vertical: 4.h,
                  ),
                  child: Text(
                    vehicleDetails.coorparateVehicle ?? '',
                    style: TextStyles.ts12w500c32993E,
                  ),
                )
              else
                VehicleParkingTimer(
                  totalTimeInSeconds: vehicleDetails.totalHrTime?.toInt() ?? 0,
                  remainingTimeInSeconds:
                      vehicleDetails.remainingSeconds?.toInt() ?? 0,
                  onTimerEnd: () {
                    final notifier =
                        ref.read(vehicleDetailsNotifierProvider.notifier);
                    notifier.getVehicleDetails(uid: vehicleDetails.uid);
                  },
                ),
            ],
          ),
          Gap(16.h),
          Divider(
            color: ColorConstants.colorF1F1F1,
            thickness: 1.h,
            height: 0,
          ),
          if (vehicleDetails.checkinTime != null) ...[
            Gap(10.h),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              spacing: 40.w,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tr(context, 'checkInTime'),
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                    Text(
                      vehicleDetails.checkinTime ?? '-',
                      style: TextStyles.ts12w700c353535,
                      textDirection: TextDirection.ltr,
                    )
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tr(context, 'totalHrs'),
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                    Text(
                      (vehicleDetails.totalHrTime?.toInt() ?? 0) == 0
                          ? '-'
                          : convertSecondsToTime(
                              vehicleDetails.totalHrTime?.toInt() ?? 0),
                      style: TextStyles.ts12w700c353535,
                      textDirection: TextDirection.ltr,
                    )
                  ],
                ),
              ],
            ),
          ],
          // if (owner?.name != null &&
          //     owner!.name!.isNotEmpty) ...[
          Gap(25.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              ClipOval(
                child: CachedNetworkImage(
                  imageUrl: owner?.profileImage ?? 'https://',
                  height: 40.h,
                  width: 40.w,
                  fit: BoxFit.cover,
                  errorWidget: (context, url, error) {
                    return CircleAvatar(
                      backgroundImage: const AssetImage(
                        'assets/icons/profile_picture.png',
                      ), // Replace with your image path
                      radius: 20.w,
                    );
                  },
                ),
              ),
              Gap(5.w),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      owner?.name ?? '--',
                      style: TextStyles.ts16w600c44322D,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Row(
                      children: [
                        Text(
                          tr(context, 'owner'),
                          style: TextStyles.ts12w400c606060,
                        ),
                        const Spacer(),
                        InkWell(
                          onTap: () async {
                            String phone = owner?.phonenumber ?? '';
                            if (phone.isNotEmpty && phone != 'None') {
                              await makePhoneCall(owner?.phonenumber ?? '');
                            }
                          },
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                'call'.asIconSvg(),
                                height: 17.h,
                                width: 17.w,
                              ),
                              Gap(3.w),
                              Text(
                                formatPhoneCode(owner?.phonenumber ?? ''),
                                style: TextStyles.ts12w700c353535,
                                textDirection: TextDirection.ltr,
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ],
          ),
          // ],
          if (vehicleDetails.subscription != null &&
              vehicleDetails.subscription!.isNotEmpty)
            Column(
              spacing: 10.h,
              children: vehicleDetails.subscription!.map(
                (e) {
                  return SubscriptionDetailsWidget(subscription: e);
                },
              ).toList(),
            )
        ],
      ),
    );
  }
}
