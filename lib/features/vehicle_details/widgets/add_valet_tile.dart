import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/add_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class AddValetTile extends StatelessWidget {
  final void Function()? onTap;
  const AddValetTile({this.onTap, super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: Colors.white,
          border: Border.all(
            width: 1.w,
            color: ColorConstants.colorEAEAEA,
          ),
        ),
        padding: EdgeInsets.symmetric(vertical: 17.h),
        child: Column(
          children: [
            Text(
              tr(context, 'no_valet_booked'),
              style: TextStyles.ts12w400c4D4D4D,
            ),
            Gap(5.h),
            const AddButton(),
          ],
        ),
      ),
    );
  }
}
