import 'dart:io';

import 'package:albalad_operator_app/features/vehicle_details/provider/vehicle_details_state.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/models/vehicle_details.dart';
import 'package:albalad_operator_app/shared/services/vehicle_services.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'vehicle_details_provider.g.dart'; // generated file

@riverpod
class VehicleDetailsNotifier extends _$VehicleDetailsNotifier {
  @override
  VehicleDetailsState build() {
    return VehicleDetailsInitial();
  }

  Future<void> getVehicleDetails({String? uid, String? vehicleNumber}) async {
    state = VehicleDetailsLoading();
    try {
      final response = await VehicleServices().fetchVehicleDetails(
        uid: uid,
        vehicleNumber: vehicleNumber,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          Map<String, dynamic> records = data['records'];
          if (records.isNotEmpty) {
            final vehicle = VehicleDetails.fromJson(data['records']);
            state = VehicleDetailsSuccess(vehicle);
          } else {
            state = VehicleDetailsNotFound();
          }
        } else {
          state = VehicleDetailsError(data['message']);
        }
      } else if (response.statusCode == 404) {
        state = VehicleDetailsNotFound();
      } else {
        state = VehicleDetailsError('Failed to fetch vehicle details');
      }
    } on SocketException {
      state = VehicleDetailsError(
        appLocalization.translate('networkError'),
        isConnectionError: true,
      );
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = VehicleDetailsError(
          appLocalization.translate('networkError'),
          isConnectionError: true,
        );
        return;
      }
      state = VehicleDetailsError('Failed to fetch vehicle details');
    }
  }
}
