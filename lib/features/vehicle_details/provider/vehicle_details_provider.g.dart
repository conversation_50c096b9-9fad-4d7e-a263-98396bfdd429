// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_details_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$vehicleDetailsNotifierHash() =>
    r'81af22ca58bcb1e8e9cd8b6ce9ff86f436097c27';

/// See also [VehicleDetailsNotifier].
@ProviderFor(VehicleDetailsNotifier)
final vehicleDetailsNotifierProvider = AutoDisposeNotifierProvider<
    VehicleDetailsNotifier, VehicleDetailsState>.internal(
  VehicleDetailsNotifier.new,
  name: r'vehicleDetailsNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$vehicleDetailsNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$VehicleDetailsNotifier = AutoDisposeNotifier<VehicleDetailsState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
