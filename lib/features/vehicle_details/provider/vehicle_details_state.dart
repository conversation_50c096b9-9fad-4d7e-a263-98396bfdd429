import 'package:albalad_operator_app/shared/models/vehicle_details.dart';

abstract class VehicleDetailsState {}

class VehicleDetailsInitial extends VehicleDetailsState {}

class VehicleDetailsLoading extends VehicleDetailsState {}

class VehicleDetailsNotFound extends VehicleDetailsState {}

class VehicleDetailsSuccess extends VehicleDetailsState {
  final VehicleDetails vehicle;

  VehicleDetailsSuccess(this.vehicle);
}

class VehicleDetailsError extends VehicleDetailsState {
  final String message;
  final bool? isConnectionError;
  VehicleDetailsError(this.message, {this.isConnectionError = false});
}
