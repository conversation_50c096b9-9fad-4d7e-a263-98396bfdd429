import 'package:albalad_operator_app/features/clamp_violation/view/clamp_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/clamp_vehicle_screen.dart';
import 'package:albalad_operator_app/features/profile/provider/profile_provider.dart';
import 'package:albalad_operator_app/features/tow_violation/view/tow_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/tow_vehicle_screen.dart';
import 'package:albalad_operator_app/features/valet/view/assign_valet_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/provider/vehicle_details_provider.dart';
import 'package:albalad_operator_app/features/vehicle_details/provider/vehicle_details_state.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/skeleton_details_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/widgets/add_valet_tile.dart';
import 'package:albalad_operator_app/features/vehicle_details/widgets/add_violation_button.dart';
import 'package:albalad_operator_app/features/vehicle_details/widgets/add_violation_tile.dart';
import 'package:albalad_operator_app/features/vehicle_details/widgets/valet_tile.dart';
import 'package:albalad_operator_app/features/vehicle_details/widgets/vehicle_info_card.dart';
import 'package:albalad_operator_app/features/vehicle_details/widgets/violation_tile.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/assign_clamping_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/assign_towing_screen.dart';
import 'package:albalad_operator_app/features/violation/view/assign_violation_screen.dart';
import 'package:albalad_operator_app/providers/permission_providers.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/models/vehicle_details.dart';
import 'package:albalad_operator_app/shared/widgets/bottom_add_vehicle_button.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:albalad_operator_app/shared/widgets/inner_assign_button.dart';
import 'package:albalad_operator_app/shared/widgets/smart_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class VehicleDetailsScreen extends ConsumerWidget {
  static const route = '/vehicle_details';
  final String? vehicleNumber;
  final String? uid;
  final String? previousRoute;
  const VehicleDetailsScreen({
    this.vehicleNumber,
    this.uid,
    this.previousRoute,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hasValetPermission = ref.watch(valetPermissionProvider);
    final hasClampPermission = ref.watch(clampPermissionProvider);
    final hasTowPermission = ref.watch(towPermissionProvider);
    final route = previousRoute ?? VehicleDetailsScreen.route;
    final state = ref.watch(vehicleDetailsNotifierProvider);
    final notifier = ref.read(vehicleDetailsNotifierProvider.notifier);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (state is VehicleDetailsInitial) {
        notifier.getVehicleDetails(
          vehicleNumber: vehicleNumber,
          uid: uid,
        );
      }
    });

    return SmartScaffold(
      isInternetAvailable:
          !(state is VehicleDetailsError && state.isConnectionError == true),
      retryConnection: () {
        notifier.getVehicleDetails(
          vehicleNumber: vehicleNumber,
          uid: uid,
        );
      },
      appBar: InnerAppBar(
        title: Text(tr(context, 'vehicle_details')),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          notifier.getVehicleDetails(
            vehicleNumber: vehicleNumber,
            uid: uid,
          );
          return ref.invalidate(profileProvider);
        },
        child: Builder(builder: (context) {
          if (state is VehicleDetailsLoading ||
              state is VehicleDetailsInitial) {
            return SkeletonDetailsScreen();
          }
          if (state is VehicleDetailsError) {
            return Center(
              child: Text(
                state.message,
                style: TextStyles.ts14w400c4D4D4D,
                textAlign: TextAlign.center,
              ),
            );
          }
          if (state is VehicleDetailsNotFound) {
            return Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(
                    'not-found'.asIconSvg(),
                    height: 128.h,
                    width: 128.w,
                  ),
                  Gap(30.h),
                  Text(
                    tr(context, 'vehicleDataNotFound'),
                    style: TextStyles.ts28w700c94684E,
                  ),
                  Gap(8.h),
                  Text(
                    tr(context, 'vehicleNotFoundInDatabase'),
                    style: TextStyles.ts16w400c959595,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }
          if (state is VehicleDetailsSuccess) {
            final violationDetails = state.vehicle.violationDetails;
            String? nextAction = state.vehicle.nextAction;
            bool addButtonEnabled =
                violationDetails != null && nextAction != null;
            final violationGrancePeriodEnd =
                violationDetails?.violationGrancePeriodEnd;
            final gracePeriodSeconds = violationDetails?.gracePeriodSeconds;
            final gracePeriodIsOver = violationDetails?.gracePeriodIsOver;
            return ListView(
              padding: EdgeInsets.fromLTRB(16.w, 30.h, 16.w, 30.h),
              children: [
                VehicleInfoCard(
                  vehicleDetails: state.vehicle,
                  key: UniqueKey(),
                ),
                Gap(24.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      tr(context, 'violation_list'),
                      style: TextStyles.ts18w600c353535,
                    ),
                    if (addButtonEnabled) ...[
                      switch (nextAction) {
                        'clamp' => SizedBox(
                            height: 22.h,
                            child: InnerAssignButton(
                              title: tr(context, 'clamp'),
                              onPressed: () => clampVehicle(
                                canClamp: hasClampPermission,
                                context: context,
                                violationUid: violationDetails.uid ?? '',
                                clampRequestUid:
                                    violationDetails.violationRequestUid ?? '',
                                vehicleUid: state.vehicle.uid ?? '',
                              ),
                            ),
                          ),
                        'tow' => SizedBox(
                            height: 22.h,
                            child: InnerAssignButton(
                              title: tr(context, 'tow'),
                              onPressed: () => towVehicle(
                                canTow: hasTowPermission,
                                context: context,
                                violationUid: violationDetails.uid ?? '',
                                towRequestUid:
                                    violationDetails.violationRequestUid ?? '',
                                vehicleUid: state.vehicle.uid ?? '',
                              ),
                            ),
                          ),
                        (_) => AddViolationButton(
                            gracePeriodEndDateTime: violationGrancePeriodEnd,
                            totalGracePeriodSeconds: gracePeriodSeconds,
                            isGracePeriodActive: gracePeriodIsOver,
                            onPressed: () => addViolation(
                              context: context,
                              violationUid: violationDetails.uid ?? '',
                              vehicleUid: state.vehicle.uid ?? '',
                              previousRoute: previousRoute,
                              canClamp: hasClampPermission,
                              canTow: hasTowPermission,
                              nextAction: state.vehicle.nextAction,
                            ),
                          )
                      },
                    ]
                  ],
                ),
                Gap(10.h),
                if (state.vehicle.violationDetails != null)
                  ViolationTile(
                    violationDetails: state.vehicle.violationDetails!,
                    vehicleUid: state.vehicle.uid,
                    previousRoute: VehicleDetailsScreen.route,
                  )
                else
                  AddViolationTile(
                    vehicleUid: state.vehicle.uid ?? '',
                    previousRoute: previousRoute,
                  ),
                Gap(24.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      tr(context, 'valet_booking_list'),
                      style: TextStyles.ts18w600c353535,
                    ),
                  ],
                ),
                Gap(10.h),
                if (state.vehicle.valetDetails != null &&
                    state.vehicle.valetDetails!.isNotEmpty)
                  ValetTile(
                    valetDetails: state.vehicle.valetDetails!.first,
                    previousRoute: route,
                    vehicleUid: state.vehicle.uid,
                    makeYear: state.vehicle.makeYear?.toString(),
                    vehicleType: state.vehicle.vehicleType,
                    numberPlateType: state.vehicle.numberPlateType,
                    vehicleImage:
                        state.vehicle.vehicleImage?.firstOrNull?.image,
                    vehicleId: state.vehicle.vehicleId,
                    vehicleName: state.vehicle.vehicleName,
                    vehicleNumber: state.vehicle.number,
                  )
                else
                  AddValetTile(
                    onTap: () => assignValet(
                      vehicle: state.vehicle,
                      context: context,
                      hasPermission: hasValetPermission,
                    ),
                  ),
              ],
            );
          }
          return const SizedBox();
        }),
      ),
      bottomNavigationBar:
          state is VehicleDetailsNotFound ? BottomAddVehicleButton() : null,
    );
  }

  addViolation({
    required BuildContext context,
    required String violationUid,
    required String vehicleUid,
    required String? previousRoute,
    required bool canTow,
    required bool canClamp,
    required String? nextAction,
  }) {
    if (previousRoute == ClampVehicleListingScreen.route &&
        nextAction != 'assign_tow') {
      if (canClamp) {
        Navigator.pushNamed(
          context,
          AssignClampingScreen.route,
          arguments: AssignClampingScreen(
            vehicleUid: vehicleUid,
            previousRoute: route,
          ),
        );
      } else {
        showPermissionErrorMessage(
          context: context,
          message: tr(context, 'violation_assign_permission_error_message'),
        );
      }
      return;
    } else if (previousRoute == TowVehicleListingScreen.route) {
      if (canTow) {
        Navigator.pushNamed(
          context,
          AssignTowingScreen.route,
          arguments: AssignTowingScreen(
            vehicleUid: vehicleUid,
            previousRoute: route,
          ),
        );
      } else {
        showPermissionErrorMessage(
          context: context,
          message: tr(context, 'violation_assign_permission_error_message'),
        );
      }
      return;
    }

    if (nextAction == null || nextAction == 'assign_new_violation') {
      Navigator.pushNamed(
        context,
        AssignViolationScreen.route,
        arguments: AssignViolationScreen(
          vehicleUid: vehicleUid,
          previousRoute: previousRoute ?? VehicleDetailsScreen.route,
        ),
      );
    }
    if (nextAction == 'assign_clamping') {
      if (canClamp) {
        Navigator.pushNamed(
          context,
          AssignClampingScreen.route,
          arguments: AssignClampingScreen(
            vehicleUid: vehicleUid,
            previousRoute: route,
          ),
        );
      } else {
        showPermissionErrorMessage(
          context: context,
          message: tr(context, 'violation_assign_permission_error_message'),
        );
      }
    }
    if (nextAction == 'assign_tow') {
      if (canTow) {
        Navigator.pushNamed(
          context,
          AssignTowingScreen.route,
          arguments: AssignTowingScreen(
            vehicleUid: vehicleUid,
            previousRoute: route,
          ),
        );
      } else {
        showPermissionErrorMessage(
          context: context,
          message: tr(context, 'violation_assign_permission_error_message'),
        );
      }
    }
  }

  clampVehicle({
    required bool canClamp,
    required BuildContext context,
    required String violationUid,
    required String clampRequestUid,
    required String vehicleUid,
  }) {
    if (canClamp) {
      final arguments = ClampVehicleScreen(
        violationUid: violationUid,
        previousRoute: route,
        vehicleUid: vehicleUid,
      );
      Navigator.pushNamed(
        context,
        ClampVehicleScreen.route,
        arguments: arguments,
      );
    } else {
      showPermissionErrorMessage(
        context: context,
        message: tr(context, 'violation_assign_permission_error_message'),
      );
    }
  }

  towVehicle({
    required bool canTow,
    required BuildContext context,
    required String violationUid,
    required String towRequestUid,
    required String vehicleUid,
  }) {
    if (canTow) {
      final arguments = TowVehicleScreen(
        violationUid: violationUid,
        vehicleUid: vehicleUid,
        previousRoute: route,
      );
      Navigator.pushNamed(
        context,
        TowVehicleScreen.route,
        arguments: arguments,
      );
    } else {
      showPermissionErrorMessage(
        context: context,
        message: tr(context, 'violation_assign_permission_error_message'),
      );
    }
  }

  assignValet({
    required VehicleDetails vehicle,
    required BuildContext context,
    required bool hasPermission,
  }) {
    if (hasPermission) {
      final images = vehicle.vehicleImage ?? [];
      final arguments = AssignValetScreen(
        vehicleUid: vehicle.uid,
        vehicleId: vehicle.vehicleId,
        vehicleNumber: vehicle.number,
        vehicleName: vehicle.vehicleName,
        vehicleImage: images.firstOrNull?.image,
        makeYear: vehicle.makeYear?.toString(),
        vehicleType: vehicle.vehicleType,
        numberPlateType: vehicle.numberPlateType,
        previousRoute: route,
      );
      Navigator.pushNamed(
        context,
        AssignValetScreen.route,
        arguments: arguments,
      );
    } else {
      showPermissionErrorMessage(
        context: context,
        message: tr(context, 'valet_assign_permission_error_message'),
      );
    }
  }
}
