import 'package:albalad_operator_app/features/vehicle_details/widgets/skeleton_valet_tile.dart';
import 'package:albalad_operator_app/features/vehicle_details/widgets/skeleton_vehicle_info_card.dart';
import 'package:albalad_operator_app/features/vehicle_details/widgets/skeleton_violation_tile.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/add_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SkeletonDetailsScreen extends StatelessWidget {
  const SkeletonDetailsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: ListView(
        padding: EdgeInsets.fromLTRB(16.w, 30.h, 16.w, 0),
        children: [
          SkeletonVehicleInfoCard(),
          Gap(24.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                tr(context, 'violation_list'),
                style: TextStyles.ts18w600c353535,
              ),
              AddButton(),
            ],
          ),
          Gap(10.h),
          SkeletonViolationTile(),
          Gap(24.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                tr(context, 'valet_booking_list'),
                style: TextStyles.ts18w600c353535,
              ),
              AddButton(),
            ],
          ),
          Gap(10.h),
          const SkeletonValetTile(),
        ],
      ),
    );
  }
}
