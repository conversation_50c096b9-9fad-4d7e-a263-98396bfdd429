import 'dart:convert';

import 'package:albalad_operator_app/features/master/view/master_screen.dart';
import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/helper/secure_storage_helper.dart';
import 'package:albalad_operator_app/shared/models/logged_in_user.dart';
import 'package:albalad_operator_app/providers/global_providers.dart';
import 'package:albalad_operator_app/features/authentication/view/sign_in_screen.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/helper/shared_preference_helper.dart';
import 'package:albalad_operator_app/shared/services/fcm_services.dart';
import 'package:albalad_operator_app/shared/services/force_update_service.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/force_update_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SplashScreen extends ConsumerStatefulWidget {
  static const route = '/splash';
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  AppEnvironment _currentEnvironment = AppEnvironment.development;
  final bool _showEnvDialog = true;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      init();
    });
    Future.delayed(
      const Duration(seconds: 3),
      () async {
        final isUpdateAvailable = await ForceUpdateService.isUpdateAvailable();
        if (isUpdateAvailable) {
          if (!mounted) return;
          showModalBottomSheet(
            context: context,
            isDismissible: false,
            enableDrag: false,
            builder: (context) => const ForceUpdateDialog(),
          );
        } else {
          ApiConstants.initializeEnvironment();
          final userJson = await SecureStorageHelper.instance.getData('user');
          if (userJson != null) {
            final json = jsonDecode(userJson);
            if (json['uid'] != null) {
              LoggedInUser.fromJson(json);
              if (!mounted) return;
              FcmServices().refreshFcmToken();
              Navigator.pushNamedAndRemoveUntil(
                context,
                MasterScreen.route,
                (route) => false,
              );
            }
          } else {
            if (_showEnvDialog) {
              _showEnvironmentDialog();
            } else {
              _proceedWithEnvironment();
            }
          }
        }
      },
    );
    super.initState();
  }

  void _showEnvironmentDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Environment'),
          content: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: AppEnvironment.values.map((env) {
                  return RadioListTile<AppEnvironment>(
                    title: Text(env.toString().split('.').last),
                    value: env,
                    groupValue: _currentEnvironment,
                    onChanged: (AppEnvironment? value) {
                      if (value != null) {
                        setState(() {
                          _currentEnvironment = value;
                        });
                        this.setState(() {
                          _currentEnvironment = value;
                        });
                      }
                    },
                  );
                }).toList(),
              );
            },
          ),
          actions: [
            TextButton(
              child: const Text('Confirm'),
              onPressed: () {
                Navigator.of(context).pop();
                _proceedWithEnvironment();
              },
            ),
          ],
        );
      },
    );
  }

  void _proceedWithEnvironment() async {
    ApiConstants.setEnvironment(_currentEnvironment);
    await ApiConstants
        .initializeEnvironment(); // If you implemented persistence
    if (!mounted) return;
    Navigator.pushReplacementNamed(context, SignInScreen.route);
  }

  init() {
    final userLocale = SharedPreferenceHelper.instance.getData('locale');
    if (userLocale != null && userLocale.isNotEmpty) {
      ref.read(localeProvider.notifier).state = Locale(userLocale);
    } else {
      ref.read(localeProvider.notifier).state = getDeviceLocale();
    }
  }

  Locale getDeviceLocale() {
    return View.of(context).platformDispatcher.locale;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.backgroundColor,
      body: Stack(
        children: [
          Center(
            child: SvgPicture.asset(
              "logo_splash".asIconSvg(),
            ),
          ),
          Positioned(
            bottom: 45.h,
            left: 0,
            right: 0,
            child: const CustomGradientSpinner(),
          )
        ],
      ),
    );
  }
}
