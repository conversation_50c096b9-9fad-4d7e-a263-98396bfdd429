import 'package:albalad_operator_app/shared/models/vehicle_details.dart';
import 'package:albalad_operator_app/shared/services/vehicle_services.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'search_providers.g.dart';

abstract class SearchState {}

class SearchInitial extends SearchState {}

class SearchLoading extends SearchState {}

class SearchError extends SearchState {
  final String message;
  SearchError(this.message);
}

class SearchSuccess extends SearchState {
  SearchSuccess();
}

final vehicleServiceProvider = Provider((ref) => VehicleServices());

@riverpod
class SearchNotifier extends _$SearchNotifier {
  @override
  SearchState build() {
    return SearchInitial();
  }

  Future<VehicleDetails?> searchVehicle(String vehicleNumber) async {
    state = SearchLoading();
    try {
      final response = await ref
          .read(vehicleServiceProvider)
          .fetchVehicleDetails(vehicleNumber: vehicleNumber);

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success' && data['records'].isNotEmpty) {
          final vehicle = VehicleDetails.fromJson(data['records']);
          state = SearchSuccess();
          return vehicle;
        } else {
          state = SearchError(data['message']);
          return null;
        }
      }
      state = SearchError(response.data['message']);
      return null;
    } catch (e) {
      state = SearchError(e.toString());
      return null;
    }
  }
}
