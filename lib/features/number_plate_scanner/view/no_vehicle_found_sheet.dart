import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';

class NoVehicleFoundSheet extends StatelessWidget {
  final String vehicleNumber;
  const NoVehicleFoundSheet({required this.vehicleNumber, super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 50.h, 16.w, 30.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SvgPicture.asset(
            'not-found'.asIconSvg(),
            width: 94.17.w,
            height: 94.17.h,
          ),
          Sized<PERSON>ox(
            height: 18.h,
          ),
          Text(
            tr(context, 'search_vehicle_not_found'),
            style: TextStyles.ts30w700c44322D,
            textAlign: TextAlign.center,
          ),
          Text.rich(
            TextSpan(
              text:
                  '${tr(context, 'search_vehicle_not_found_description_one')} ',
              style: TextStyles.ts14w600c959595,
              children: [
                TextSpan(
                  text: vehicleNumber,
                  style: TextStyles.ts14w700c94684E,
                ),
                TextSpan(
                  text:
                      '. ${tr(context, 'search_vehicle_not_found_description_two')}',
                  style: TextStyles.ts14w600c959595,
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
          Gap(50.h),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: Text(tr(context, 'try_again')),
          ),
        ],
      ),
    );
  }
}
