import 'dart:io';

import 'package:albalad_operator_app/features/add_vehicle/view/add_vehicle_screen.dart';
import 'package:albalad_operator_app/features/number_plate_scanner/search_providers.dart';
import 'package:albalad_operator_app/features/number_plate_scanner/view/no_vehicle_found_sheet.dart';
import 'package:albalad_operator_app/features/valet/view/assign_valet_screen.dart';
import 'package:albalad_operator_app/features/valet/view/valet_operations_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_scalable_ocr/flutter_scalable_ocr.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class NumberPlateScanner extends ConsumerStatefulWidget {
  static const route = '/number-plate-scanner';
  final String previousRoute;
  const NumberPlateScanner({required this.previousRoute, super.key});

  @override
  ConsumerState<NumberPlateScanner> createState() => _NumberPlateScannerState();
}

class _NumberPlateScannerState extends ConsumerState<NumberPlateScanner> {
  String text = "";
  bool torchOn = false;
  int cameraSelection = 0;
  bool lockCamera = true;
  bool loading = false;
  final GlobalKey<ScalableOCRState> cameraKey = GlobalKey<ScalableOCRState>();
  TextEditingController numberPlateController = TextEditingController();

  String vehicleNumber = '';

  void setText(value) {
    String numberPlate = normalizePlate(value);
    if (isValidSaudiPlate(numberPlate)) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          numberPlateController.text = numberPlate;
        });
      });
      // cameraKey.currentState?.stopLiveFeed();
      // Navigator.pushReplacementNamed(
      //   context,
      //   VehicleDetailsScreen.route,
      //   arguments: VehicleDetailsScreen(
      //     vehicleNumber: addSpaceToPlate(numberPlate),
      //   ),
      // );
    }
    return;
  }

  bool isValidSaudiPlate(String plate) {
    final regex = RegExp(r'^\d{3,4}[A-Zأ-ي]{3}$');
    return regex.hasMatch(plate);
  }

  String normalizePlate(String plate) {
    return plate.toUpperCase().replaceAll(' ', '').replaceAll('|', '');
  }

  String addSpaceToPlate(String plate) {
    final regex = RegExp(r'^(\d{3,4})([A-Zأ-ي]{3})$');
    return plate.replaceAllMapped(regex, (match) => '${match[1]} ${match[2]}');
  }

  @override
  void dispose() {
    numberPlateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: InnerAppBar(
            title: Text(tr(context, 'scanNumberPlate')),
          ),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                !loading
                    ? Column(
                        children: [
                          ScalableOCR(
                            key: cameraKey,
                            torchOn: torchOn,
                            cameraSelection: cameraSelection,
                            lockCamera: lockCamera,
                            paintboxCustom: Paint()
                              ..style = PaintingStyle.stroke
                              ..strokeWidth = 4.0
                              ..color = const Color(0xFFE1DDD2),
                            boxLeftOff: 5,
                            boxBottomOff: 3,
                            boxRightOff: 10,
                            boxTopOff: 3,
                            boxHeight: 203.h,
                            getRawData: (value) {
                              // inspect(value);
                            },
                            getScannedText: (value) {
                              setText(value);
                            },
                          ),
                          Gap(16.h),
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10.r),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  blurRadius: 20,
                                  spreadRadius: 0,
                                  offset: const Offset(0, 0),
                                ),
                              ],
                            ),
                            margin: EdgeInsets.symmetric(horizontal: 16.w),
                            padding: EdgeInsets.symmetric(
                              horizontal: 16.w,
                              vertical: 12.h,
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    tr(context, 'scannedNumberPlate'),
                                    style: TextStyles.ts12w400c44322D,
                                  ),
                                ),
                                Expanded(
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(4.r),
                                    child: TextFormField(
                                      controller: numberPlateController,
                                      decoration: InputDecoration(
                                        fillColor: ColorConstants.colorE1DDD2
                                            .withValues(alpha: 0.2),
                                        filled: true,
                                        border: InputBorder.none,
                                        enabledBorder: InputBorder.none,
                                        focusedBorder: InputBorder.none,
                                      ),
                                    ),
                                  ),
                                ),
                                // Container(
                                //   decoration: BoxDecoration(
                                //     color: ColorConstants.colorE1DDD2
                                //         .withValues(alpha: 0.2),
                                //     borderRadius: BorderRadius.circular(4.r),
                                //   ),
                                //   padding: EdgeInsets.symmetric(
                                //     horizontal: 23.w,
                                //     vertical: 4.h,
                                //   ),
                                //   child: Text(
                                //     '900 AVZ',
                                //     style: TextStyles.ts14w600c44322D,
                                //   ),
                                // ),
                              ],
                            ),
                          ),
                        ],
                      )
                    : Padding(
                        padding: const EdgeInsets.all(17.0),
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          height: MediaQuery.of(context).size.height / 3,
                          width: MediaQuery.of(context).size.width,
                          child: const Center(
                            child: CustomGradientSpinner(),
                          ),
                        ),
                      ),
              ],
            ),
          ),
          bottomNavigationBar: SafeArea(
            bottom: true,
            child: Padding(
              padding: EdgeInsets.fromLTRB(
                  16.w, 0, 16.w, Platform.isAndroid ? 30.h : 0),
              child: ElevatedButton(
                onPressed: numberPlateController.text.isNotEmpty
                    ? searchVehicle
                    : null,
                child: Text(tr(context, 'search')),
              ),
            ),
          ),
        ),

        // Loading Overlay
        Consumer(
          builder: (context, ref, child) {
            final state = ref.watch(searchNotifierProvider);
            if (state is SearchLoading) {
              return Container(
                color: Colors.black
                    .withValues(alpha: 0.5), // Semi-transparent background
                child: Center(
                  child: CustomGradientSpinner(),
                ),
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }

  searchVehicle() async {
    if (widget.previousRoute == ValetOperationsScreen.route) {
      final notifier = ref.read(searchNotifierProvider.notifier);
      final vehicle = await notifier
          .searchVehicle(addSpaceToPlate(numberPlateController.text));
      if (!mounted) return;
      if (vehicle != null) {
        final images = vehicle.vehicleImage ?? [];
        final arguments = AssignValetScreen(
          vehicleUid: vehicle.uid,
          vehicleId: vehicle.vehicleId,
          vehicleNumber: vehicle.number,
          vehicleName: vehicle.vehicleName,
          vehicleImage: images.firstOrNull?.image,
          makeYear: vehicle.makeYear?.toString(),
          vehicleType: vehicle.vehicleType,
          numberPlateType: vehicle.numberPlateType,
          previousRoute: widget.previousRoute,
        );
        Navigator.pushNamed(
          context,
          AssignValetScreen.route,
          arguments: arguments,
        );
      } else {
        showModalBottomSheet(
          context: context,
          builder: (context) => NoVehicleFoundSheet(
            vehicleNumber: addSpaceToPlate(numberPlateController.text),
          ),
        );
      }
    } else if (widget.previousRoute == AddVehicleScreen.route) {
      Navigator.pop(context, addSpaceToPlate(numberPlateController.text));
    } else {
      cameraKey.currentState?.stopLiveFeed();
      Navigator.pushReplacementNamed(
        context,
        VehicleDetailsScreen.route,
        arguments: VehicleDetailsScreen(
          vehicleNumber: addSpaceToPlate(numberPlateController.text),
          previousRoute: widget.previousRoute,
        ),
      );
    }
  }
}
