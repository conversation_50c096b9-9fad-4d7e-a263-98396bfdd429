import 'package:albalad_operator_app/shared/models/violation_image.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

class ImagePreviewScreen extends HookWidget {
  // final String imageUrl;
  // final String uid;
  final List<ViolationImage> violationImages;
  final PageController pageController;
  final int initialIndex;
  const ImagePreviewScreen({
    // required this.imageUrl,
    // required this.uid,
    required this.pageController,
    this.violationImages = const [],
    this.initialIndex = 0,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final currentIndex = useState(initialIndex);
    return Scaffold(
      appBar: const InnerAppBar(),
      body: Container(
        decoration: BoxDecoration(
          color: Colors.black,
        ),
        height: MediaQuery.sizeOf(context).height,
        child: Stack(
          alignment: Alignment.bottomRight,
          children: [
            PhotoViewGallery.builder(
              itemCount: violationImages.length,
              builder: (context, index) {
                return PhotoViewGalleryPageOptions(
                  imageProvider: CachedNetworkImageProvider(
                    violationImages[index].image ?? 'https://',
                  ),
                  heroAttributes: PhotoViewHeroAttributes(
                    tag: violationImages[index].uid ?? '123',
                  ),
                );
              },
              loadingBuilder: (context, event) => const Center(
                child: CustomGradientSpinner(),
              ),
              pageController: pageController,
              onPageChanged: (index) => currentIndex.value = index,
            ),
            Container(
              padding: const EdgeInsets.all(20.0),
              child: Text(
                "Image ${currentIndex.value + 1} of ${violationImages.length}",
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12.0,
                ),
              ),
            )
          ],
        ),
      ),
      // body: Center(
      //   child: Hero(
      //     tag: uid,
      //     child: PhotoView(
      //       imageProvider: CachedNetworkImageProvider(imageUrl),
      //     ),
      //   ),
      // ),
    );
  }
}
