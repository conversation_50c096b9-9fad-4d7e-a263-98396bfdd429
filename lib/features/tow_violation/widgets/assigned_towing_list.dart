import 'package:albalad_operator_app/features/clamp_violation/view/clamp_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/providers/assigned_towing_list_provider.dart';
import 'package:albalad_operator_app/features/tow_violation/widgets/assigned_tow_request_card.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/not_found_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class AssignedTowingList extends ConsumerWidget {
  const AssignedTowingList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(assignedTowingListNotifierProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          tr(context, 'assigned_towing_request'),
          style: TextStyles.ts18w600c353535,
        ),
        Gap(7.h),
        if (state.items.isEmpty && !state.hasMore) ...[
          Gap(0.1.sh),
          NotFoundWidget(
            title: tr(context, 'no_request_assigned'),
            description: tr(context, 'no_request_assigned_description'),
          ),
        ] else if (state.items.isEmpty && state.isLoading) ...[
          const SkeletonParkingViolations(),
        ] else
          ListView.separated(
            shrinkWrap: true,
            physics: const PageScrollPhysics(),
            itemBuilder: (context, index) {
              if (index == state.items.length) {
                return const Center(child: CustomGradientSpinner());
              }
              final item = state.items[index];
              return AssignedTowRequestCard(towRequest: item);
            },
            separatorBuilder: (context, index) => SizedBox(height: 24.h),
            itemCount: state.items.length + (state.hasMore ? 1 : 0),
          )
      ],
    );
  }
}
