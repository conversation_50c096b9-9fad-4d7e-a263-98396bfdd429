import 'package:albalad_operator_app/features/tow_violation/models/assigned_tow_request.dart';
import 'package:albalad_operator_app/features/violation/view/parking_violation_details_screen.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';

class AssignedTowRequestCard extends StatelessWidget {
  final AssignedTowRequest towRequest;
  const AssignedTowRequestCard({
    required this.towRequest,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final vehicleImage = towRequest.vehicleImage?.firstOrNull?.image;
    return InkWell(
      onTap: () {
        final arguments = ParkingViolationDetailsScreen(
          violationUid: towRequest.violationUid ?? '',
          previousRoute: '',
        );
        Navigator.pushNamed(
          context,
          ParkingViolationDetailsScreen.route,
          arguments: arguments,
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              offset: const Offset(0, 0),
              blurRadius: 20,
              spreadRadius: 0,
            ),
          ],
        ),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text.rich(
                      TextSpan(
                        text: tr(context, 'id'),
                        style: TextStyles.ts12w400c4D4D4D,
                        children: [
                          TextSpan(
                            text: ' ${towRequest.violationId}',
                            style: TextStyles.ts12w700c353535,
                          ),
                        ],
                      ),
                    ),
                    Gap(9.w),
                    SizedBox(
                      height: 15.h,
                      child: VerticalDivider(
                        color: ColorConstants.colorF1F1F1,
                        thickness: 1.w,
                        width: 0,
                      ),
                    ),
                    Gap(9.w),
                    Expanded(
                      child: Text(
                        towRequest.violationName ?? '',
                        style: TextStyles.ts12w400c4D4D4D,
                      ),
                    ),
                    Gap(9.w),
                    Container(
                      decoration: BoxDecoration(
                        color: ColorConstants.colorFFF7E2,
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: 10.w,
                        vertical: 2.h,
                      ),
                      child: Text(
                        towRequest.paymentStatus ?? '',
                        style: TextStyles.ts12w500cE8B020,
                      ),
                    ),
                  ],
                ),
                Gap(10.h),
                Divider(
                  color: ColorConstants.colorF1F1F1,
                  thickness: 1.h,
                  height: 0,
                ),
                Gap(16.h),
                Row(
                  children: [
                    Container(
                      height: 35.h,
                      width: 35.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                        color: ColorConstants.colorF1EFE9,
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8.r),
                        child: CachedNetworkImage(
                          imageUrl: vehicleImage ?? 'https://',
                          memCacheWidth: 200,
                          fit: BoxFit.fill,
                          errorWidget: (context, url, error) =>
                              Image.asset('car'.asImagePng()),
                        ),
                      ),
                    ),
                    Gap(10.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          towRequest.vehicleNumberPlate ?? '',
                          style: TextStyles.ts14w600c181818,
                        ),
                        Text(
                          towRequest.vehicleName ?? '',
                          style: TextStyles.ts10w400cA1A09B,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            Gap(16.h),
            Divider(
              color: ColorConstants.colorF1F1F1,
              thickness: 1.h,
              height: 0,
            ),
            Gap(10.h),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        towRequest.violationStatus ?? '',
                        style: TextStyles.ts12w400c4D4D4D,
                      ),
                      Gap(5.h),
                      Text(
                        towRequest.enforcerName ?? '',
                        style: TextStyles.ts12w600c4D4D4D,
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          tr(context, 'violation_date_and_time'),
                          style: TextStyles.ts12w400c4D4D4D,
                        ),
                        Gap(5.h),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SvgPicture.asset(
                              'clock'.asIconSvg(),
                              height: 14.h,
                              width: 14.w,
                            ),
                            Gap(5.w),
                            Text(
                              towRequest.violationTime ?? '',
                              style: TextStyles.ts12w400c4D4D4D,
                              textDirection: TextDirection.ltr,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
