import 'package:albalad_operator_app/features/home/<USER>/home_search_field.dart';
import 'package:albalad_operator_app/features/number_plate_scanner/view/number_plate_scanner.dart';
import 'package:albalad_operator_app/features/search/provider/search_provider.dart';
import 'package:albalad_operator_app/features/search/view/search_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/providers/assigned_towing_list_provider.dart';
import 'package:albalad_operator_app/features/tow_violation/providers/towed_vehicle_list_provider.dart';
import 'package:albalad_operator_app/features/tow_violation/providers/towing_request_list_provider.dart';
import 'package:albalad_operator_app/features/tow_violation/widgets/assigned_towing_list.dart';
import 'package:albalad_operator_app/features/tow_violation/widgets/clamped_violation_listing.dart';
import 'package:albalad_operator_app/features/tow_violation/widgets/tow_request_listing.dart';
import 'package:albalad_operator_app/features/tow_violation/widgets/tow_vehicle_tab_bar.dart';
import 'package:albalad_operator_app/features/tow_violation/widgets/towed_vehicle_listing.dart';
import 'package:albalad_operator_app/features/violation/provider/tow_clamped_vehicle_listing_provider.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:albalad_operator_app/shared/widgets/smart_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class TowVehicleListingScreen extends HookConsumerWidget {
  static const String route = '/tow-vehicle-listing-screen';
  final int tabIndex;
  const TowVehicleListingScreen({this.tabIndex = 0, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final seletedTab = useState<int>(tabIndex);

    final notifiers = (
      clamped: ref.read(towClampedVehicleListingNotifierProvider.notifier),
      towed: ref.read(towedVehicleListNotifierProvider.notifier),
      request: ref.read(towingRequestListNotifierProvider.notifier),
      assigned: ref.read(assignedTowingListNotifierProvider.notifier),
    );

    final state = (
      clamped: ref.watch(towClampedVehicleListingNotifierProvider),
      towed: ref.watch(towedVehicleListNotifierProvider),
      request: ref.watch(towingRequestListNotifierProvider),
      assigned: ref.watch(assignedTowingListNotifierProvider),
    );

    // Trigger the API call when the screen is first built
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (state.clamped.items.isEmpty) {
          notifiers.clamped.fetchClampedVehicleList();
        }
        if (state.request.items.isEmpty) {
          notifiers.request.fetchTowingRequestList();
        }
        if (state.towed.items.isEmpty) {
          notifiers.towed.fetchTowedVehicleList();
        }
        if (state.assigned.items.isEmpty) {
          notifiers.assigned.fetchAssignedTowingList();
        }
      });
      return null;
    }, []);

    final connectionError = (
      clamped: state.clamped.isConnectionError,
      request: state.request.isConnectionError,
      towed: state.towed.isConnectionError,
      assigned: state.assigned.isConnectionError,
    );

    return SmartScaffold(
      isInternetAvailable: !(connectionError.clamped &&
          connectionError.request &&
          connectionError.towed &&
          connectionError.assigned),
      retryConnection: () {
        notifiers.clamped.reset();
        notifiers.towed.reset();
        notifiers.request.reset();
        notifiers.assigned.reset();
        Future.sync(
          () {
            notifiers.clamped.fetchClampedVehicleList();
            notifiers.towed.fetchTowedVehicleList();
            notifiers.request.fetchTowingRequestList();
            notifiers.assigned.fetchAssignedTowingList();
          },
        );
      },
      appBar: InnerAppBar(
        title: Text(tr(context, 'towVehicle')),
      ),
      body: NotificationListener<ScrollNotification>(
        onNotification: (scrollNotification) {
          if (scrollNotification is ScrollEndNotification &&
              scrollNotification.metrics.extentAfter == 0) {
            if (seletedTab.value == 0 && state.request.isLoading == false) {
              notifiers.request.fetchTowingRequestList();
            } else if (seletedTab.value == 1 &&
                state.towed.isLoading == false) {
              notifiers.towed.fetchTowedVehicleList();
            } else if (seletedTab.value == 2 &&
                state.clamped.isLoading == false) {
              notifiers.clamped.fetchClampedVehicleList();
            } else if (seletedTab.value == 3 &&
                state.assigned.isLoading == false) {
              notifiers.assigned.fetchAssignedTowingList();
            }
          }
          return false;
        },
        child: RefreshIndicator(
          onRefresh: () {
            notifiers.clamped.reset();
            notifiers.towed.reset();
            notifiers.request.reset();
            notifiers.assigned.reset();
            return Future.sync(
              () {
                notifiers.clamped.fetchClampedVehicleList();
                notifiers.towed.fetchTowedVehicleList();
                notifiers.request.fetchTowingRequestList();
                notifiers.assigned.fetchAssignedTowingList();
              },
            );
          },
          child: ListView(
            padding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 30.h,
            ),
            children: [
              HomeSearchField(
                hintText: tr(context, 'searchVehicleNumber'),
                readOnly: true,
                onPress: () {
                  final notifier = ref.read(searchNotifierProvider.notifier);
                  notifier.reset();
                  final arguments = SearchScreen(previousRoute: route);
                  Navigator.pushNamed(context, SearchScreen.route,
                      arguments: arguments);
                },
                onTapSuffix: () {
                  final arguments = NumberPlateScanner(previousRoute: route);
                  Navigator.of(context).pushNamed(
                    NumberPlateScanner.route,
                    arguments: arguments,
                  );
                },
              ),
              Gap(16.h),
              TowVehicleTabBar(
                value: seletedTab.value,
                onChanged: (value) => seletedTab.value = value,
              ),
              Gap(24.h),
              switch (seletedTab.value) {
                0 => const TowRequestListing(previousRoute: route),
                1 => const TowedVehicleListing(previousRoute: route),
                2 => const ClampedViolationListing(previousRoute: route),
                _ => const AssignedTowingList(),
              }
            ],
          ),
        ),
      ),
    );
  }
}
