import 'dart:io';

import 'package:albalad_operator_app/features/success_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/providers/assigned_towing_list_provider.dart';
import 'package:albalad_operator_app/features/tow_violation/providers/towing_request_list_provider.dart';
import 'package:albalad_operator_app/features/vehicle_details/provider/vehicle_details_provider.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/features/violation/models/enforcer.dart';
import 'package:albalad_operator_app/features/violation/models/violation_type.dart';
import 'package:albalad_operator_app/features/violation/provider/assign_towing_state.dart';
import 'package:albalad_operator_app/features/violation/provider/tow_clamped_vehicle_listing_provider.dart';
import 'package:albalad_operator_app/features/violation/provider/towing_provider.dart';
import 'package:albalad_operator_app/features/tow_violation/view/tow_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/violation/widgets/towing_enforcer_dropdown.dart';
import 'package:albalad_operator_app/features/violation/widgets/violation_type_dropdown.dart';
import 'package:albalad_operator_app/features/violation/violation_type.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/helper/dialog_helper.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AssignTowingScreen extends HookConsumerWidget {
  static const route = '/assign-towing';
  final String vehicleUid;
  final String previousRoute;
  const AssignTowingScreen({
    required this.previousRoute,
    required this.vehicleUid,
    super.key,
  });

  _onAssignTowingSuccess(BuildContext context, WidgetRef ref) {
    if (previousRoute == TowVehicleListingScreen.route) {
      final notifiers = (
        tow: ref.read(towClampedVehicleListingNotifierProvider.notifier),
        request: ref.read(towingRequestListNotifierProvider.notifier),
        assigned: ref.read(assignedTowingListNotifierProvider.notifier)
      );

      notifiers.tow.reset();
      notifiers.request.reset();
      notifiers.assigned.reset();

      notifiers.tow.fetchClampedVehicleList();
      notifiers.request.fetchTowingRequestList();
      notifiers.assigned.fetchAssignedTowingList();
    }

    if (previousRoute == VehicleDetailsScreen.route) {
      final notifier = ref.read(vehicleDetailsNotifierProvider.notifier);
      notifier.getVehicleDetails(uid: vehicleUid);
    }

    final args = SuccessScreen(
      title: tr(context, 'towing_assigned'),
      message: tr(context, 'towing_assigned_description'),
      onPressed: (ctxt) => Navigator.popUntil(
        ctxt,
        ModalRoute.withName(previousRoute),
      ),
    );
    Navigator.pushReplacementNamed(
      context,
      SuccessScreen.route,
      arguments: args,
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final selectedType = useState<ViolationType?>(null);
    final selectedEnforcer = useState<Enforcer?>(null);

    ref.listen(
      assignTowingNotifierProvider,
      (previous, next) {
        if (next is AssignTowingFailure) {
          DialogHelper.showErrorDialog(context: context, message: next.message);
        }
        if (next is AssignTowingSuccess) {
          _onAssignTowingSuccess(context, ref);
        }
      },
    );

    final notifier = ref.read(assignTowingNotifierProvider.notifier);
    final state = ref.watch(assignTowingNotifierProvider);

    return Scaffold(
      appBar: InnerAppBar(
        title: Text(tr(context, 'assign_towing')),
      ),
      body: Form(
        key: formKey,
        child: ListView(
          padding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 30.h,
          ),
          children: [
            ViolationTypeDropdown(
              violationtype: VIOLATIONTYPE.towing,
              value: selectedType.value,
              onChanged: (p0) => selectedType.value = p0,
            ),
            Gap(24.h),
            TowingEnforcerDropdown(
              enforcerType: 'towing',
              value: selectedEnforcer.value,
              onChanged: (p0) => selectedEnforcer.value = p0,
            ),
          ],
        ),
      ),
      bottomNavigationBar: SafeArea(
        bottom: true,
        child: Padding(
          padding: EdgeInsets.fromLTRB(
            16.w,
            0,
            16.w,
            Platform.isAndroid ? 30.h : 0,
          ),
          child: state is AssignTowingLoading
              ? SizedBox(
                  height: kBottomNavigationBarHeight,
                  child: const Center(
                    child: CustomGradientSpinner(),
                  ),
                )
              : ElevatedButton(
                  onPressed: () {
                    if (formKey.currentState?.validate() == true) {
                      notifier.submit(
                        vehicleUid: vehicleUid,
                        violationTypeUid: selectedType.value?.uid ?? '',
                        clampingOperatorUid: selectedEnforcer.value?.uid ?? '',
                      );
                    }
                  },
                  child: Text(tr(context, 'submit')),
                ),
        ),
      ),
    );
  }
}
