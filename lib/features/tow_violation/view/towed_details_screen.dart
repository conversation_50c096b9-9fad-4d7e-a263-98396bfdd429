import 'package:albalad_operator_app/features/clamp_violation/widgets/skeleton_clamped_details.dart';
import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/vehicle_info_chip.dart';
import 'package:albalad_operator_app/features/image_preview_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/models/towed_details.dart';
import 'package:albalad_operator_app/features/tow_violation/providers/towed_details_provider.dart';
import 'package:albalad_operator_app/features/tow_violation/view/release_tow_confirmation_sheet.dart';
import 'package:albalad_operator_app/features/tow_violation/view/update_location_confirmation_sheet.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/models/violation_image.dart';
import 'package:albalad_operator_app/shared/widgets/bottom_navigation_bar_elevated_button.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:albalad_operator_app/shared/widgets/smart_scaffold.dart';
import 'package:albalad_operator_app/shared/widgets/status_builder.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:skeletonizer/skeletonizer.dart';

class TowedDetailsScreen extends HookConsumerWidget {
  static const route = '/towed-details';
  final String uid;
  final String previousRoute;
  const TowedDetailsScreen(
      {required this.uid, required this.previousRoute, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(towedDetailsNotifierProvider);
    final notifier = ref.watch(towedDetailsNotifierProvider.notifier);

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifier.fetchTowedDetails(uid);
      });
      return null;
    }, []);

    return SmartScaffold(
      isInternetAvailable:
          !(state is TowedDetailsStateError && state.isConnectionError),
      retryConnection: () => notifier.fetchTowedDetails(uid),
      appBar: InnerAppBar(
        title: Text(tr(context, 'towed_vehicle_details')),
      ),
      body: Builder(builder: (context) {
        if (state is TowedDetailsStateLoading) {
          return const SkeletonClampedDetails();
        }
        if (state is TowedDetailsStateLoaded) {
          final details = state.towedDetails;
          String vehicleImage =
              details.vehicleImage?.firstOrNull?.image ?? 'https://';

          List<TowImages> towImages = details.towImages ?? [];
          return RefreshIndicator(
            onRefresh: () => notifier.fetchTowedDetails(uid),
            child: ListView(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16.r),
                    border: Border.all(
                      color: ColorConstants.colorEAEAEA,
                      width: 1.w,
                    ),
                  ),
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text.rich(
                            TextSpan(
                              text: tr(context, 'id'),
                              style: TextStyles.ts12w400c4D4D4D,
                              children: [
                                TextSpan(
                                  text: ' ${details.towId}',
                                  style: TextStyles.ts12w700c353535,
                                ),
                              ],
                            ),
                          ),
                          StatusBuilder(
                            status: details.status,
                          ),
                        ],
                      ),
                      Gap(15.h),
                      Divider(
                        height: 0,
                        thickness: 1.h,
                        color: ColorConstants.colorF1F1F1,
                      ),
                      Gap(15.h),
                      Row(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: ColorConstants.colorF1EFE9,
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            height: 35.h,
                            width: 35.w,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8.r),
                              child: CachedNetworkImage(
                                imageUrl: vehicleImage,
                                memCacheWidth: 200,
                                fit: BoxFit.fill,
                                errorWidget: (context, url, error) {
                                  return Image.asset('car'.asImagePng());
                                },
                              ),
                            ),
                          ),
                          Gap(10.w),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                details.vehicleNumberPlate ?? '',
                                style: TextStyles.ts14w600c181818,
                              ),
                              Text(
                                details.vehicleName ?? '',
                                style: TextStyles.ts10w400cA1A09B,
                              ),
                            ],
                          ),
                        ],
                      ),
                      Gap(16.h),
                      Row(
                        spacing: 8.w,
                        children: [
                          if (details.makeYear != null)
                            VehicleInfoChip(title: details.makeYear.toString()),
                          if (details.vehicleType != null)
                            VehicleInfoChip(title: details.vehicleType ?? ''),
                          if (details.numberPlateType != null)
                            VehicleInfoChip(
                                title: details.numberPlateType ?? ''),
                          if (details.coorparateVehicle == true)
                            VehicleInfoChip(
                              title: 'Corp. Vehicle',
                              shared: true,
                            ),
                        ],
                      ),
                      Gap(16.h),
                      Divider(
                        height: 0,
                        thickness: 1.h,
                        color: ColorConstants.colorF1F1F1,
                      ),
                      Gap(16.h),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              tr(context, 'violation_date_and_time'),
                              style: TextStyles.ts12w400c4D4D4D,
                            ),
                          ),
                          Expanded(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                SvgPicture.asset(
                                  'clock'.asIconSvg(),
                                  height: 14.h,
                                  width: 14.w,
                                ),
                                Gap(5.w),
                                Text(
                                  details.violationDateTime ?? '',
                                  style: TextStyles.ts12w400c4D4D4D,
                                  textDirection: TextDirection.ltr,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Gap(10.h),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              tr(context, 'towed_date_and_time'),
                              style: TextStyles.ts12w400c4D4D4D,
                            ),
                          ),
                          Expanded(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                SvgPicture.asset(
                                  'clock'.asIconSvg(),
                                  height: 14.h,
                                  width: 14.w,
                                ),
                                Gap(5.w),
                                Text(
                                  details.towedDateTime ?? '',
                                  style: TextStyles.ts12w400c4D4D4D,
                                  textDirection: TextDirection.ltr,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Gap(10.h),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              tr(context, 'towed_by'),
                              style: TextStyles.ts12w400c4D4D4D,
                            ),
                          ),
                          Expanded(
                            child: Text(
                              details.clampedBy ?? '',
                              style: TextStyles.ts12w400c4D4D4D,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.end,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Gap(20.h),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: ColorConstants.colorFFFFFF,
                    boxShadow: [
                      BoxShadow(
                        color:
                            ColorConstants.color000000.withValues(alpha: 0.1),
                        blurRadius: 20.0,
                        spreadRadius: 0,
                        offset: Offset(0, 0),
                      ),
                    ],
                  ),
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        tr(context, 'violation_description'),
                        style: TextStyles.ts18w600c353535,
                      ),
                      Gap(6.h),
                      Text(
                        details.description ?? '',
                        style: TextStyles.ts14w600c959595,
                      ),
                    ],
                  ),
                ),
                Gap(24.h),
                Text(
                  tr(context, 'violation_images'),
                  style: TextStyles.ts18w600c353535,
                ),
                Gap(6.h),
                Wrap(
                  spacing: 12.w,
                  runSpacing: 12.h,
                  children: towImages.map(
                    (e) {
                      return InkWell(
                        onTap: () {
                          int initialIndex = towImages.indexOf(e);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ImagePreviewScreen(
                                violationImages: towImages
                                    .map((e) => ViolationImage(
                                        image: e.image, uid: e.uid))
                                    .toList(),
                                pageController: PageController(
                                  initialPage: initialIndex,
                                ),
                                initialIndex: initialIndex,
                              ),
                            ),
                          );
                        },
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: CachedNetworkImage(
                            imageUrl: e.image ?? '',
                            height: 154.h,
                            width: (1.sw - 44.w) / 2,
                            fit: BoxFit.cover,
                            errorWidget: (context, url, error) {
                              return Image.asset(
                                "violation-sample".asImagePng(),
                                height: 154.h,
                                width: (1.sw - 44.w) / 2,
                                fit: BoxFit.cover,
                              );
                            },
                          ),
                        ),
                      );
                    },
                  ).toList(),
                ),
                Gap(24.h),
                Container(
                  decoration: BoxDecoration(
                    color: ColorConstants.colorF8F8F8,
                    borderRadius: BorderRadius.circular(14.r),
                  ),
                  padding:
                      EdgeInsets.symmetric(horizontal: 15.w, vertical: 16.h),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              tr(context, 'update_vehicle_location'),
                              style: TextStyles.ts13w600c44322D,
                            ),
                            Text(
                              tr(context,
                                  'update_vehicle_location_description'),
                              style: TextStyles.ts10w400c959595,
                            ),
                          ],
                        ),
                      ),
                      OutlinedButton(
                        onPressed: () => showModalBottomSheet(
                          context: context,
                          builder: (context) => UpdateLocationConfirmationSheet(
                              towId: details.uid ?? ''),
                        ),
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(
                            color: ColorConstants.color94684E,
                            width: 1.w,
                          ),
                          textStyle: TextStyles.ts12w600c94684E,
                          padding: EdgeInsets.symmetric(horizontal: 23.w),
                        ),
                        child: Text(tr(context, 'update')),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }
        return const SizedBox();
      }),
      bottomNavigationBar: Skeletonizer(
        enabled: state is TowedDetailsStateLoading,
        child: BottomNavigationBarElevatedButton(
          title: tr(context, 'release_vehicle'),
          onPressed: state is TowedDetailsStateLoaded &&
                  state.towedDetails.isSettled == true
              ? () {
                  showModalBottomSheet(
                    context: context,
                    isDismissible: false,
                    enableDrag: false,
                    builder: (context) => ReleaseTowConfirmationSheet(
                      vehicleUid: state.towedDetails.uid ?? '',
                      previousRoute: previousRoute,
                    ),
                  );
                }
              : null,
        ),
      ),
    );
  }
}
