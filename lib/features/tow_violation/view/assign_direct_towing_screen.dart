import 'dart:io';

import 'package:albalad_operator_app/features/success_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/providers/towing_request_list_provider.dart';
import 'package:albalad_operator_app/features/vehicle_details/provider/vehicle_details_provider.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/features/violation/models/enforcer.dart';
import 'package:albalad_operator_app/features/violation/models/violation_type.dart';
import 'package:albalad_operator_app/features/violation/provider/assign_direct_towing_provider.dart';
import 'package:albalad_operator_app/features/tow_violation/view/tow_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/violation/widgets/initial_image_upload_card.dart';
import 'package:albalad_operator_app/features/violation/widgets/towing_enforcer_dropdown.dart';
import 'package:albalad_operator_app/features/violation/widgets/upload_image_card.dart';
import 'package:albalad_operator_app/features/violation/widgets/violation_type_dropdown.dart';
import 'package:albalad_operator_app/features/violation/violation_type.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/helper/dialog_helper.dart';
import 'package:albalad_operator_app/shared/helper/image_picker_helper.dart';
import 'package:albalad_operator_app/shared/widgets/bottom_navigation_bar_elevated_button.dart';
import 'package:albalad_operator_app/shared/widgets/custom_text_form_field.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';

class AssignDirectTowingScreen extends HookConsumerWidget {
  static const route = '/assign-direct-towing';
  final String vehicleUid;
  final String previousRoute;
  const AssignDirectTowingScreen({
    required this.previousRoute,
    required this.vehicleUid,
    super.key,
  });

  _onAssignTowingSuccess(BuildContext context, WidgetRef ref) {
    if (previousRoute == TowVehicleListingScreen.route) {
      final notifier = ref.read(towingRequestListNotifierProvider.notifier);
      notifier.reset();
      notifier.fetchTowingRequestList();
    }
    if (previousRoute == VehicleDetailsScreen.route) {
      final notifier = ref.read(vehicleDetailsNotifierProvider.notifier);
      notifier.getVehicleDetails(uid: vehicleUid);
    }

    final args = SuccessScreen(
      title: tr(context, 'towing_assigned'),
      message: tr(context, 'direct_towing_assigned_description'),
      onPressed: (ctxt) => Navigator.popUntil(
        ctxt,
        ModalRoute.withName(previousRoute),
      ),
    );
    Navigator.pushReplacementNamed(
      context,
      SuccessScreen.route,
      arguments: args,
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final selectedType = useState<ViolationType?>(null);
    final selectedEnforcer = useState<Enforcer?>(null);
    final descriptionController = useTextEditingController();
    final uploadedImages = useState<List<XFile>>([]);
    final showImageError = useState<bool>(false);

    ref.listen(
      assignDirectTowingNotifierProvider,
      (previous, next) {
        if (next is AssignDirectTowingFailure) {
          DialogHelper.showErrorDialog(context: context, message: next.message);
        }
        if (next is AssignDirectTowingSuccess) {
          _onAssignTowingSuccess(context, ref);
        }
      },
    );

    final notifier = ref.read(assignDirectTowingNotifierProvider.notifier);
    final state = ref.watch(assignDirectTowingNotifierProvider);

    return Scaffold(
      appBar: InnerAppBar(
        title: Text(tr(context, 'assign_towing')),
      ),
      body: Form(
        key: formKey,
        child: ListView(
          padding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 30.h,
          ),
          children: [
            ViolationTypeDropdown(
              violationtype: VIOLATIONTYPE.towing,
              value: selectedType.value,
              onChanged: (p0) => selectedType.value = p0,
            ),
            Gap(24.h),
            TowingEnforcerDropdown(
              enforcerType: 'towing',
              value: selectedEnforcer.value,
              onChanged: (p0) => selectedEnforcer.value = p0,
            ),
            Gap(24.h),
            CustomTextFormField(
              controller: descriptionController,
              labelText: tr(context, 'violation_description_star'),
              maxLines: 5,
              hintText: tr(context, 'description_here'),
              autovalidateMode: AutovalidateMode.onUserInteraction,
              textCapitalization: TextCapitalization.sentences,
              validator: (p0) {
                if (p0 == null || p0.isEmpty) {
                  return tr(context, 'please_enter_violation_description');
                }
                return null;
              },
            ),
            Gap(24.h),
            Text(
              tr(context, 'upload_image_star'),
              style: TextStyles.ts12w400c505050,
            ),
            Gap(5.h),
            if (uploadedImages.value.isEmpty)
              InitialImageUploadCard(
                onTap: () async {
                  final imageFile = await ImagePickerHelper.pickImage(context);
                  if (imageFile != null) {
                    uploadedImages.value = [...uploadedImages.value, imageFile];
                  }
                },
                error: showImageError.value,
              )
            else
              Wrap(
                spacing: 12.w,
                runSpacing: 12.h,
                children: [
                  ...uploadedImages.value.map(
                    (e) {
                      return Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(10.r),
                            child: Image.file(
                              File(e.path),
                              height: 154.h,
                              width: (1.sw - 44.w) / 2,
                              fit: BoxFit.cover,
                            ),
                          ),
                          Positioned(
                            right: 8,
                            top: 8,
                            child: InkWell(
                              onTap: () => uploadedImages.value = [
                                ...uploadedImages.value
                                    .where((element) => element != e)
                              ],
                              child: Container(
                                width: 24.w,
                                height: 24.h,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: ColorConstants.colorF26464,
                                ),
                                alignment: Alignment.center,
                                child: Icon(
                                  Icons.delete_rounded,
                                  size: 18,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          )
                        ],
                      );
                    },
                  ),
                  UploadImageCard(
                    onTap: () async {
                      final imageFile =
                          await ImagePickerHelper.pickImage(context);
                      if (imageFile != null) {
                        uploadedImages.value = [
                          ...uploadedImages.value,
                          imageFile
                        ];
                      }
                    },
                  ),
                ],
              ),
            Gap(5.h),
            if (showImageError.value && uploadedImages.value.isEmpty)
              Text(
                tr(context, 'please_upload_image'),
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigationBarElevatedButton(
        title: tr(context, 'submit'),
        isLoading: state is AssignDirectTowingLoading,
        onPressed: () {
          if (formKey.currentState?.validate() == true) {
            notifier.submit(
              vehicleUid: vehicleUid,
              violationTypeUid: selectedType.value?.uid ?? '',
              towingOperatorUid: selectedEnforcer.value?.uid ?? '',
              description: descriptionController.text,
              violationImages: uploadedImages.value
                  .map((e) => e.path)
                  .toList(growable: false),
            );
          }
        },
      ),
      // bottomNavigationBar: SafeArea(
      //   bottom: true,
      //   child: Padding(
      //     padding: EdgeInsets.fromLTRB(
      //       16.w,
      //       0,
      //       16.w,
      //       Platform.isAndroid ? 30.h : 0,
      //     ),
      //     child: state is AssignDirectTowingLoading
      //         ? SizedBox(
      //             height: kBottomNavigationBarHeight,
      //             child: const Center(
      //               child: CustomGradientSpinner(),
      //             ),
      //           )
      //         : ElevatedButton(
      //             onPressed: () {
      //               if (formKey.currentState?.validate() == true) {
      //                 // notifier.submit(
      //                 //   violationUid: violationUid,
      //                 //   violationTypeUid: selectedType.value?.uid ?? '',
      //                 //   clampingOperatorUid: selectedEnforcer.value?.uid ?? '',
      //                 // );
      //               }
      //             },
      //             child: Text(tr(context, 'submit')),
      //           ),
      //   ),
      // ),
    );
  }
}
