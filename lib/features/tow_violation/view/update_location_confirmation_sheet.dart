import 'package:albalad_operator_app/features/tow_violation/providers/update_location_provider.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/elevated_secondary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class UpdateLocationConfirmationSheet extends HookConsumerWidget {
  final String towId;
  const UpdateLocationConfirmationSheet({required this.towId, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading = useState(false);

    final state = ref.watch(updateLocationNotifierProvider);
    ref.listen(updateLocationNotifierProvider, (previous, next) {
      if (next is UpdateLocationSuccess) {
        Navigator.pop(context);
        SnackBar snackBar = SnackBar(
          content: Text(tr(context, 'towed_location_updated_successfully')),
        );
        ScaffoldMessenger.of(context).showSnackBar(snackBar);
      }
    });
    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 50.h, 16.w, 30.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SvgPicture.asset(
            'info-circle'.asIconSvg(),
            height: 94.17.h,
            width: 94.17.w,
          ),
          Gap(18.h),
          Text(
            tr(context, 'confirm_location_update'),
            style: TextStyles.ts30w700c44322D,
            textAlign: TextAlign.center,
          ),
          Text(
            tr(context, 'confirm_location_update_description'),
            style: TextStyles.ts14w600c959595,
            textAlign: TextAlign.center,
          ),
          Gap(50.h),
          Row(
            spacing: 10.w,
            children: [
              Expanded(
                child: ElevatedSecondaryButton(
                  title: tr(context, 'cancel'),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
              Expanded(
                child: isLoading.value || state is UpdateLocationLoading
                    ? const Center(
                        child: CustomGradientSpinner(),
                      )
                    : ElevatedButton(
                        onPressed: () =>
                            _getCurrentLocation(context, ref, isLoading),
                        child: Text(tr(context, 'confirm')),
                      ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _getCurrentLocation(BuildContext context, WidgetRef ref,
      ValueNotifier<bool> isLoading) async {
    isLoading.value = true;
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      if (!context.mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Location services are disabled.')),
      );
      isLoading.value = false;
      return;
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        if (!context.mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Location permissions are denied.')),
        );
        isLoading.value = false;
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      if (!context.mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Location permissions are permanently denied.')),
      );
      isLoading.value = false;
      return;
    }
    final notifier = ref.watch(updateLocationNotifierProvider.notifier);
    Position position = await Geolocator.getCurrentPosition(
      locationSettings: LocationSettings(
        accuracy: LocationAccuracy.high,
      ),
    );
    isLoading.value = false;
    notifier.updateLocation(
      towUid: towId,
      lat: position.latitude.toString(),
      long: position.longitude.toString(),
    );
  }
}
