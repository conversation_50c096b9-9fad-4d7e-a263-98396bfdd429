import 'dart:io';

import 'package:albalad_operator_app/features/success_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/providers/assigned_towing_list_provider.dart';
import 'package:albalad_operator_app/features/tow_violation/providers/tow_vehicle_provider.dart';
import 'package:albalad_operator_app/features/tow_violation/providers/towed_vehicle_list_provider.dart';
import 'package:albalad_operator_app/features/tow_violation/providers/towing_request_list_provider.dart';
import 'package:albalad_operator_app/features/tow_violation/view/close_towing_sheet.dart';
import 'package:albalad_operator_app/features/tow_violation/view/tow_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/provider/vehicle_details_provider.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/features/violation/widgets/initial_image_upload_card.dart';
import 'package:albalad_operator_app/features/violation/widgets/upload_image_card.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/bottom_navigation_bar_elevated_button.dart';
import 'package:albalad_operator_app/shared/widgets/custom_text_form_field.dart';
import 'package:albalad_operator_app/shared/widgets/image_source_sheet.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

class TowVehicleScreen extends HookConsumerWidget {
  static const String route = '/tow-vehicle';
  final String violationUid;
  final String? previousRoute;
  final String vehicleUid;
  const TowVehicleScreen({
    required this.violationUid,
    this.previousRoute,
    required this.vehicleUid,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final descriptionController = useTextEditingController();
    final uploadedImages = useState<List<XFile>>([]);
    final showImageError = useState<bool>(false);

    ref.listen(towVehicleNotifierProvider, (previous, next) {
      if (next is TowVehicleSuccess) {
        if (previousRoute == VehicleDetailsScreen.route) {
          final notifier = ref.read(vehicleDetailsNotifierProvider.notifier);
          notifier.getVehicleDetails(uid: vehicleUid);
        } else {
          final notifiers = (
            request: ref.read(towingRequestListNotifierProvider.notifier),
            assigned: ref.read(assignedTowingListNotifierProvider.notifier),
            towed: ref.read(towedVehicleListNotifierProvider.notifier),
          );

          notifiers.request.reset();
          notifiers.towed.reset();
          notifiers.assigned.reset();

          notifiers.request.fetchTowingRequestList();
          notifiers.towed.fetchTowedVehicleList();
          notifiers.assigned.fetchAssignedTowingList();
        }
        final arguments = SuccessScreen(
          title: tr(context, 'towing_successful'),
          message: tr(context, 'towing_successful_message'),
          onPressed: (context) {
            if (previousRoute == VehicleDetailsScreen.route) {
              Navigator.popUntil(context, ModalRoute.withName(previousRoute!));
            } else {
              Navigator.popUntil(
                context,
                ModalRoute.withName(TowVehicleListingScreen.route),
              );
            }
          },
        );
        Navigator.pushReplacementNamed(
          context,
          SuccessScreen.route,
          arguments: arguments,
        );
      }
    });

    final notifier = ref.read(towVehicleNotifierProvider.notifier);
    final state = ref.watch(towVehicleNotifierProvider);

    return Scaffold(
      appBar: InnerAppBar(
        title: Text(tr(context, 'towVehicle')),
      ),
      body: Form(
        key: formKey,
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
          children: [
            CustomTextFormField(
              controller: descriptionController,
              labelText: tr(context, 'description_star'),
              maxLines: 5,
              hintText: tr(context, 'description_here'),
              autovalidateMode: AutovalidateMode.onUserInteraction,
              textCapitalization: TextCapitalization.sentences,
              validator: (p0) {
                if (p0 == null || p0.isEmpty) {
                  return tr(context, 'please_enter_description');
                }
                return null;
              },
            ),
            Gap(24.h),
            Text(
              tr(context, 'upload_image_star'),
              style: TextStyles.ts12w400c505050,
            ),
            Gap(5.h),
            if (uploadedImages.value.isEmpty)
              InitialImageUploadCard(
                onTap: () async {
                  final imageFile = await pickImage(context);
                  if (imageFile != null) {
                    uploadedImages.value = [...uploadedImages.value, imageFile];
                  }
                },
                error: showImageError.value,
              )
            else
              Wrap(
                spacing: 12.w,
                runSpacing: 12.h,
                children: [
                  ...uploadedImages.value.map(
                    (e) {
                      return Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(10.r),
                            child: Image.file(
                              File(e.path),
                              height: 154.h,
                              width: (1.sw - 44.w) / 2,
                              fit: BoxFit.cover,
                            ),
                          ),
                          Positioned(
                            right: 8,
                            top: 8,
                            child: InkWell(
                              onTap: () => uploadedImages.value = [
                                ...uploadedImages.value
                                    .where((element) => element != e)
                              ],
                              child: Container(
                                width: 24.w,
                                height: 24.h,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: ColorConstants.colorF26464,
                                ),
                                alignment: Alignment.center,
                                child: Icon(
                                  Icons.delete_rounded,
                                  size: 18,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          )
                        ],
                      );
                    },
                  ),
                  UploadImageCard(
                    onTap: () async {
                      final imageFile = await pickImage(context);
                      if (imageFile != null) {
                        uploadedImages.value = [
                          ...uploadedImages.value,
                          imageFile
                        ];
                      }
                    },
                  ),
                ],
              ),
            Gap(5.h),
            if (showImageError.value && uploadedImages.value.isEmpty)
              Text(
                tr(context, 'please_upload_image'),
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
            Gap(24.h),
            Container(
              decoration: BoxDecoration(
                color: ColorConstants.colorF8F8F8,
                gradient: RadialGradient(
                  colors: [
                    Colors.white,
                    Colors.white,
                    ColorConstants.colorF9F9FF,
                  ],
                ),
                borderRadius: BorderRadius.circular(14.r),
              ),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    tr(context, 'could_not_find_vehicle'),
                    style: TextStyles.ts14w600c44322D,
                  ),
                  Text(
                    tr(context, 'vehicle_not_found_description'),
                    style: TextStyles.ts12w400c959595,
                  ),
                  Gap(12.h),
                  Divider(
                    color: ColorConstants.colorF1F1F1,
                    height: 0,
                    thickness: 1.h,
                  ),
                  Gap(14.h),
                  Align(
                    alignment: Alignment.centerRight,
                    child: ElevatedButton(
                      onPressed: () {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          builder: (context) => CloseTowingSheet(
                            violationUid: violationUid,
                            previousRoute: previousRoute ?? '',
                            vehicleUid: vehicleUid,
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        textStyle: TextStyles.ts12w600wE1DDD2,
                        padding: EdgeInsets.symmetric(
                          vertical: 6.h,
                          horizontal: 10.w,
                        ),
                        minimumSize: Size(98.w, 22.h),
                      ),
                      child: Text(tr(context, 'close_violation')),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigationBarElevatedButton(
        title: tr(context, 'submit'),
        isLoading: state is TowVehicleLoading,
        onPressed: () {
          if (uploadedImages.value.isEmpty) {
            showImageError.value = true;
          } else {
            showImageError.value = false;
          }
          if (formKey.currentState?.validate() == true &&
              uploadedImages.value.isNotEmpty) {
            notifier.submitTowVehicle(
              vehicleUid: vehicleUid,
              description: descriptionController.text,
              towImages: uploadedImages.value,
            );
          }
          // if (beforeImageFile.value == null) {
          //   showBeforeImageError.value = true;
          // } else {
          //   showBeforeImageError.value = false;
          // }
          // if (afterImageFile.value == null) {
          //   showAfterImageError.value = true;
          // } else {
          //   showAfterImageError.value = false;
          // }
          // if (formKey.currentState?.validate() == true &&
          //     beforeImageFile.value != null &&
          //     afterImageFile.value != null) {
          //   notifier.submitClampVehicle(
          //     clampRequestUid: towRequestUid,
          //     description: descriptionController.text,
          //     beforeImage: beforeImageFile.value!.path,
          //     afterImage: afterImageFile.value!.path,
          //   );
          // }
        },
      ),
    );
  }

  Future<XFile?> pickImage(BuildContext context) async {
    final imageSource = await showModalBottomSheet(
      context: context,
      builder: (context) => ImageSourceSheet(),
    );
    if (imageSource == null) return null;
    final ImagePicker picker = ImagePicker();
    final XFile? image =
        await picker.pickImage(source: imageSource, maxWidth: 1000);
    if (image != null) {
      final croppedImage = await cropImage(image: image);
      if (croppedImage != null) {
        return XFile(croppedImage.path);
      }
      return null;
    }
    return null;
  }

  Future<CroppedFile?> cropImage({required XFile image}) async {
    return ImageCropper().cropImage(
      sourcePath: image.path,
      uiSettings: [
        AndroidUiSettings(
          statusBarColor: Colors.black,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: false,
        ),
        IOSUiSettings(),
      ],
    );
  }
}
