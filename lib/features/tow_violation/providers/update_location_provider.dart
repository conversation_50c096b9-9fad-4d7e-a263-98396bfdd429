import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'update_location_provider.g.dart';

abstract class UpdateLocationState {}

class UpdateLocationInitial extends UpdateLocationState {}

class UpdateLocationLoading extends UpdateLocationState {}

class UpdateLocationSuccess extends UpdateLocationState {}

class UpdateLocationFailure extends UpdateLocationState {
  final String? message;
  UpdateLocationFailure({this.message});
}

@riverpod
class UpdateLocationNotifier extends _$UpdateLocationNotifier {
  @override
  UpdateLocationState build() => UpdateLocationInitial();

  final Dio _dio = DioClient().dio;

  void updateLocation({
    required String towUid,
    required String lat,
    required String long,
  }) async {
    state = UpdateLocationLoading();
    try {
      final response = await _dio.post(
        ApiConstants.updateVehicleLocation,
        data: {
          "tow_uid": towUid,
          "latitude": lat,
          "longitude": long,
        },
        options: Options(
          headers: await ApiConstants.authHeaders(),
        ),
      );
      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          state = UpdateLocationSuccess();
          return;
        } else {
          state = UpdateLocationFailure(message: data['message']);
          return;
        }
      }
      state = UpdateLocationFailure(message: 'Something went wrong.');
    } on DioException catch (e) {
      state = UpdateLocationFailure(message: e.message);
    } catch (e) {
      state = UpdateLocationFailure(message: e.toString());
    }
  }
}
