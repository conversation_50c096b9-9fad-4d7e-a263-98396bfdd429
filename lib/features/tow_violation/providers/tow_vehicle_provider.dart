import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:dio/dio.dart';
import 'package:image_picker/image_picker.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'tow_vehicle_provider.g.dart';

abstract class TowVehicleState {}

class TowVehicleInitial extends TowVehicleState {}

class TowVehicleLoading extends TowVehicleState {}

class TowVehicleSuccess extends TowVehicleState {}

class TowVehicleError extends TowVehicleState {
  final String message;
  TowVehicleError(this.message);
}

@riverpod
class TowVehicleNotifier extends _$TowVehicleNotifier {
  @override
  TowVehicleState build() {
    return TowVehicleInitial();
  }

  final Dio _dio = DioClient().dio;

  Future<void> submitTowVehicle({
    required String vehicleUid,
    required String description,
    required List<XFile> towImages,
  }) async {
    state = TowVehicleLoading();
    try {
      FormData formData = FormData.fromMap({
        "vehicle_uid": vehicleUid,
        "description": description,
        "images":
            towImages.map((e) => MultipartFile.fromFileSync(e.path)).toList(),
      });

      final response = await _dio.post(
        ApiConstants.clampTowVehicle,
        data: formData,
        options: Options(headers: await ApiConstants.authFormDataHeaders()),
      );

      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          state = TowVehicleSuccess();
        } else {
          state = TowVehicleError(response.data['message']);
        }
      } else {
        state = TowVehicleError(response.data['message']);
      }
    } catch (e) {
      state = TowVehicleError(e.toString());
    }
  }
}
