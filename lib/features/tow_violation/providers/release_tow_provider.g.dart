// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'release_tow_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$releaseTowNotifierHash() =>
    r'f0d44748a1e7aa6b26e818b5a7766941c4dc0cc6';

/// See also [ReleaseTowNotifier].
@ProviderFor(ReleaseTowNotifier)
final releaseTowNotifierProvider =
    AutoDisposeNotifierProvider<ReleaseTowNotifier, ReleaseTowState>.internal(
  ReleaseTowNotifier.new,
  name: r'releaseTowNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$releaseTowNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ReleaseTowNotifier = AutoDisposeNotifier<ReleaseTowState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
