import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'release_tow_provider.g.dart';

abstract class ReleaseTowState {}

class ReleaseTowInitial extends ReleaseTowState {}

class ReleaseTowLoading extends ReleaseTowState {}

class ReleaseTowSuccess extends ReleaseTowState {}

class ReleaseTowFailure extends ReleaseTowState {
  final String? message;

  ReleaseTowFailure({this.message});
}

@riverpod
class ReleaseTowNotifier extends _$ReleaseTowNotifier {
  @override
  ReleaseTowState build() => ReleaseTowInitial();

  final Dio _dio = DioClient().dio;

  Future<void> releaseTow(String vehicleUid) async {
    state = ReleaseTowLoading();
    try {
      final response = await _dio.post(
        ApiConstants.releaseTowedVehicle,
        data: {
          'tow_vehicle_uid': vehicleUid,
        },
        options: Options(headers: await ApiConstants.authHeaders()),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          state = ReleaseTowSuccess();
          return;
        } else {
          state = ReleaseTowFailure();
          return;
        }
      }
      if (response.statusCode == 400) {
        final data = response.data;
        Map<String, dynamic> errors = data['errors'] ?? {};
        if (errors.isNotEmpty) {
          state = ReleaseTowFailure(
            message: errors.values.first.toString(),
          );
          return;
        }
      }
      state = ReleaseTowFailure();
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = ReleaseTowFailure(
          message: appLocalization.translate('networkError'),
        );
        return;
      }
      state = ReleaseTowFailure();
    }
  }
}
