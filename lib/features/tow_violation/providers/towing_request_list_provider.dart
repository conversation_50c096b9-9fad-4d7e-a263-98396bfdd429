import 'dart:async';
import 'dart:io';

import 'package:albalad_operator_app/features/tow_violation/models/towing_request_item.dart';
import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'towing_request_list_provider.g.dart';

class TowingRequestListState {
  final List<TowingRequestItem> items;
  final bool isLoading;
  final bool hasMore;
  final bool isConnectionError;
  final String? error;

  TowingRequestListState({
    required this.items,
    required this.isLoading,
    required this.hasMore,
    this.isConnectionError = false,
    this.error,
  });

  TowingRequestListState copyWith({
    List<TowingRequestItem>? items,
    bool? isLoading,
    bool? hasMore,
    String? error,
    bool? isConnectionError,
  }) {
    return TowingRequestListState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      error: error ?? this.error,
      isConnectionError: isConnectionError ?? this.isConnectionError,
    );
  }
}

@riverpod
class TowingRequestListNotifier extends _$TowingRequestListNotifier {
  @override
  TowingRequestListState build() {
    return TowingRequestListState(
      items: [],
      isLoading: false,
      hasMore: true,
      error: null,
    );
  }

  int _currentPage = 1;

  Future<void> fetchTowingRequestList() async {
    if (state.isLoading || !state.hasMore) return;
    state =
        state.copyWith(isLoading: true, error: null, isConnectionError: false);

    try {
      final violationServices = ref.read(violationServicesProvider);
      final response =
          await violationServices.fetchTowingRequestList(page: _currentPage);
      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          final List<dynamic> records = data['records'] ?? [];
          final List<TowingRequestItem> currentViolations =
              records.map((e) => TowingRequestItem.fromJson(e)).toList();
          state = state.copyWith(
            items: [...state.items, ...currentViolations],
            isLoading: false,
            hasMore: data['pagination']?['has_next'] == true,
          );
          _currentPage++;
        } else {
          state = state.copyWith(
            isLoading: false,
            hasMore: false,
          );
        }
      } else {
        state = state.copyWith(
          isLoading: false,
          hasMore: false,
        );
      }
    } on SocketException {
      _handleError(null, isConnectionError: true);
    } catch (e, stackTrace) {
      debugPrint('Error in fetchParkingViolationList: $e');
      debugPrint('StackTrace: $stackTrace');
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        _handleError(null, isConnectionError: true);
        return;
      }
      _handleError(e.toString());
    }
  }

  _handleError(String? error, {bool isConnectionError = false}) {
    state = state.copyWith(
      isLoading: false,
      hasMore: false,
      error: error,
      isConnectionError: isConnectionError,
    );
  }

  void reset() {
    _currentPage = 1;
    state = TowingRequestListState(
      items: [],
      isLoading: false,
      hasMore: true,
    );
  }
}
