// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'assigned_towing_list_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$assignedTowingListNotifierHash() =>
    r'608fa21acaa18490344dbacf9baffe794f248ee7';

/// See also [AssignedTowingListNotifier].
@ProviderFor(AssignedTowingListNotifier)
final assignedTowingListNotifierProvider = AutoDisposeNotifierProvider<
    AssignedTowingListNotifier, AssignedTowingListState>.internal(
  AssignedTowingListNotifier.new,
  name: r'assignedTowingListNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$assignedTowingListNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AssignedTowingListNotifier
    = AutoDisposeNotifier<AssignedTowingListState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
