// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'towed_details_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$towedDetailsNotifierHash() =>
    r'a63824cf87c5c5ac9d13d31f68f818a084ef3814';

/// See also [TowedDetailsNotifier].
@ProviderFor(TowedDetailsNotifier)
final towedDetailsNotifierProvider = AutoDisposeNotifierProvider<
    TowedDetailsNotifier, TowedDetailsState>.internal(
  TowedDetailsNotifier.new,
  name: r'towedDetailsNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$towedDetailsNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TowedDetailsNotifier = AutoDisposeNotifier<TowedDetailsState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
