import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'close_towing_provider.g.dart';

abstract class CloseTowingState {}

class CloseTowingInitial extends CloseTowingState {}

class CloseTowingLoading extends CloseTowingState {}

class CloseTowingSuccess extends CloseTowingState {}

class CloseTowingError extends CloseTowingState {
  final String message;
  CloseTowingError(this.message);
}

@riverpod
class CloseTowingNotifier extends _$CloseTowingNotifier {
  @override
  CloseTowingState build() {
    return CloseTowingInitial();
  }

  final Dio _dio = DioClient().dio;

  Future<void> submitCloseTowing({
    required String violationUid,
    required String closedReason,
  }) async {
    state = CloseTowingLoading();
    try {
      final response = await _dio.post(
        ApiConstants.closeTowingViolation,
        data: {
          'closed_reason': closedReason,
          'violation': violationUid,
        },
        options: Options(headers: await ApiConstants.authHeaders()),
      );
      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          state = CloseTowingSuccess();
        } else {
          state = CloseTowingError(response.data['message']);
        }
      } else {
        state = CloseTowingError(response.data['message']);
      }
    } catch (e) {
      state = CloseTowingError(e.toString());
    }
  }
}
