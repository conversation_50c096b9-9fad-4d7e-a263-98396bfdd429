// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'close_towing_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$closeTowingNotifierHash() =>
    r'22edd6c8a083c06a0c168f504871616b94845633';

/// See also [CloseTowingNotifier].
@ProviderFor(CloseTowingNotifier)
final closeTowingNotifierProvider =
    AutoDisposeNotifierProvider<CloseTowingNotifier, CloseTowingState>.internal(
  CloseTowingNotifier.new,
  name: r'closeTowingNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$closeTowingNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CloseTowingNotifier = AutoDisposeNotifier<CloseTowingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
