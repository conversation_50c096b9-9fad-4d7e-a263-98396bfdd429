// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'towed_vehicle_list_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$towedVehicleListNotifierHash() =>
    r'a72ed413b3bd783016aac097e448932af9059565';

/// See also [TowedVehicleListNotifier].
@ProviderFor(TowedVehicleListNotifier)
final towedVehicleListNotifierProvider = AutoDisposeNotifierProvider<
    TowedVehicleListNotifier, TowedVehicleListState>.internal(
  TowedVehicleListNotifier.new,
  name: r'towedVehicleListNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$towedVehicleListNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TowedVehicleListNotifier = AutoDisposeNotifier<TowedVehicleListState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
