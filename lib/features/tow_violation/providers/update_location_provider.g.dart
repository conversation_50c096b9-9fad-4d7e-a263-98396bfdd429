// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_location_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$updateLocationNotifierHash() =>
    r'4a19d6b54d45c66e16c359ebf470ba65ed06f1ef';

/// See also [UpdateLocationNotifier].
@ProviderFor(UpdateLocationNotifier)
final updateLocationNotifierProvider = AutoDisposeNotifierProvider<
    UpdateLocationNotifier, UpdateLocationState>.internal(
  UpdateLocationNotifier.new,
  name: r'updateLocationNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$updateLocationNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpdateLocationNotifier = AutoDisposeNotifier<UpdateLocationState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
