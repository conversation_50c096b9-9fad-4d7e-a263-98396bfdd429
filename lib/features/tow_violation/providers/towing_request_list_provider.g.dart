// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'towing_request_list_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$towingRequestListNotifierHash() =>
    r'56192937a28bc08e591413039bf060fc711ff43d';

/// See also [TowingRequestListNotifier].
@ProviderFor(TowingRequestListNotifier)
final towingRequestListNotifierProvider = AutoDisposeNotifierProvider<
    TowingRequestListNotifier, TowingRequestListState>.internal(
  TowingRequestListNotifier.new,
  name: r'towingRequestListNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$towingRequestListNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TowingRequestListNotifier
    = AutoDisposeNotifier<TowingRequestListState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
