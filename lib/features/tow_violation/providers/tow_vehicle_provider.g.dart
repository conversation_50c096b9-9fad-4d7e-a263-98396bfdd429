// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tow_vehicle_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$towVehicleNotifierHash() =>
    r'c5332806a9ce7fc4133bf39961af0a6f8dfc29df';

/// See also [TowVehicleNotifier].
@ProviderFor(TowVehicleNotifier)
final towVehicleNotifierProvider =
    AutoDisposeNotifierProvider<TowVehicleNotifier, TowVehicleState>.internal(
  TowVehicleNotifier.new,
  name: r'towVehicleNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$towVehicleNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TowVehicleNotifier = AutoDisposeNotifier<TowVehicleState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
