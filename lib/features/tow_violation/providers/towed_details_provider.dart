import 'package:albalad_operator_app/features/tow_violation/models/towed_details.dart';
import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'towed_details_provider.g.dart';

abstract class TowedDetailsState {}

class TowedDetailsStateInitial extends TowedDetailsState {}

class TowedDetailsStateLoading extends TowedDetailsState {}

class TowedDetailsStateLoaded extends TowedDetailsState {
  final TowedDetails towedDetails;

  TowedDetailsStateLoaded(this.towedDetails);
}

class TowedDetailsStateError extends TowedDetailsState {
  final String error;
  final bool isConnectionError;

  TowedDetailsStateError(this.error, {this.isConnectionError = false});
}

@riverpod
class TowedDetailsNotifier extends _$TowedDetailsNotifier {
  @override
  TowedDetailsState build() {
    return TowedDetailsStateInitial();
  }

  Future<void> fetchTowedDetails(String uid) async {
    state = TowedDetailsStateLoading();
    try {
      final violationServices = ref.read(violationServicesProvider);
      final towedDetails = await violationServices.fetchTowedDetails(uid);
      state = TowedDetailsStateLoaded(towedDetails);
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = TowedDetailsStateError(e.toString(), isConnectionError: true);
        return;
      }
      state = TowedDetailsStateError(e.toString());
    }
  }
}
