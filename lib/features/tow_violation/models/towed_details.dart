import 'package:albalad_operator_app/shared/models/vehicle_image.dart';

class TowedDetails {
  String? uid;
  String? towId;
  String? violationUid;
  String? status;
  String? vehicleNumberPlate;
  String? vehicleName;
  String? vehicleType;
  List<VehicleImage>? vehicleImage;
  int? makeYear;
  String? numberPlateType;
  String? towedDateTime;
  String? violationDateTime;
  bool? isSettled;
  String? clampedBy;
  String? description;
  bool? coorparateVehicle;
  List<TowImages>? towImages;

  TowedDetails({
    this.uid,
    this.towId,
    this.violationUid,
    this.status,
    this.vehicleNumberPlate,
    this.vehicleName,
    this.vehicleType,
    this.vehicleImage,
    this.makeYear,
    this.numberPlateType,
    this.towedDateTime,
    this.violationDateTime,
    this.isSettled,
    this.clampedBy,
    this.description,
    this.coorparateVehicle,
    this.towImages,
  });

  TowedDetails.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    towId = json['tow_id'];
    violationUid = json['violation_uid'];
    status = json['status'];
    vehicleNumberPlate = json['vehicle_number_plate'];
    vehicleName = json['vehicle_name'];
    vehicleType = json['vehicle_type'];
    if (json['vehicle_image'] != null) {
      vehicleImage = <VehicleImage>[];
      json['vehicle_image'].forEach((v) {
        vehicleImage!.add(VehicleImage.fromJson(v));
      });
    }
    makeYear = json['make_year'];
    numberPlateType = json['number_plate_type'];
    towedDateTime = json['towed_date_time'];
    violationDateTime = json['violation_date_time'];
    isSettled = json['is_settled'];
    clampedBy = json['clamped_by'];
    description = json['description'];
    coorparateVehicle = json['coorparate_vehicle'];
    if (json['tow_images'] != null) {
      towImages = <TowImages>[];
      json['tow_images'].forEach((v) {
        towImages!.add(TowImages.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['tow_id'] = towId;
    data['violation_uid'] = violationUid;
    data['status'] = status;
    data['vehicle_number_plate'] = vehicleNumberPlate;
    data['vehicle_name'] = vehicleName;
    data['vehicle_type'] = vehicleType;
    if (vehicleImage != null) {
      data['vehicle_image'] = vehicleImage!.map((v) => v.toJson()).toList();
    }
    data['make_year'] = makeYear;
    data['number_plate_type'] = numberPlateType;
    data['towed_date_time'] = towedDateTime;
    data['violation_date_time'] = violationDateTime;
    data['is_settled'] = isSettled;
    data['clamped_by'] = clampedBy;
    data['description'] = description;
    data['coorparate_vehicle'] = coorparateVehicle;
    if (towImages != null) {
      data['tow_images'] = towImages!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TowImages {
  String? uid;
  String? image;

  TowImages({this.uid, this.image});

  TowImages.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    image = json['image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['image'] = image;
    return data;
  }
}
