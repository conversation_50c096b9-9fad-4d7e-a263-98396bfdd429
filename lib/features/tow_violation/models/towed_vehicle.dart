import 'package:albalad_operator_app/shared/models/vehicle_image.dart';

class TowedVehicle {
  String? uid;
  String? towId;
  String? violationUid;
  String? status;
  String? vehicleNumberPlate;
  String? vehicleName;
  String? vehicleType;
  List<VehicleImage>? vehicleImage;
  int? makeYear;
  String? numberPlateType;
  String? towedDateTime;

  TowedVehicle({
    this.uid,
    this.towId,
    this.violationUid,
    this.status,
    this.vehicleNumberPlate,
    this.vehicleName,
    this.vehicleType,
    this.vehicleImage,
    this.makeYear,
    this.numberPlateType,
    this.towedDateTime,
  });

  TowedVehicle.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    towId = json['tow_id'];
    violationUid = json['violation_uid'];
    status = json['status'];
    vehicleNumberPlate = json['vehicle_number_plate'];
    vehicleName = json['vehicle_name'];
    vehicleType = json['vehicle_type'];
    if (json['vehicle_image'] != null) {
      vehicleImage = <VehicleImage>[];
      json['vehicle_image'].forEach((v) {
        vehicleImage!.add(VehicleImage.fromJson(v));
      });
    }
    makeYear = int.tryParse(json['make_year'].toString());
    numberPlateType = json['number_plate_type'];
    towedDateTime = json['towed_date_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['tow_id'] = towId;
    data['violation_uid'] = violationUid;
    data['status'] = status;
    data['vehicle_number_plate'] = vehicleNumberPlate;
    data['vehicle_name'] = vehicleName;
    data['vehicle_type'] = vehicleType;
    if (vehicleImage != null) {
      data['vehicle_image'] = vehicleImage!.map((v) => v.toJson()).toList();
    }
    data['make_year'] = makeYear;
    data['number_plate_type'] = numberPlateType;
    data['towed_date_time'] = towedDateTime;
    return data;
  }
}
