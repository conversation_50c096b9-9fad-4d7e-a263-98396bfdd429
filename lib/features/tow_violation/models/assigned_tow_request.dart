import 'package:albalad_operator_app/shared/models/vehicle_image.dart';

class AssignedTowRequest {
  String? uid;
  String? violationUid;
  String? violationId;
  String? violationName;
  String? vehicleType;
  int? makeYear;
  String? numberPlateType;
  String? paymentStatus;
  String? vehicleNumberPlate;
  String? vehicleName;
  List<VehicleImage>? vehicleImage;
  String? violationTime;
  String? violationStatus;
  int? gracePeriodSeconds;
  String? gracePeriodTime;
  String? enforcerName;
  String? assignedTime;
  String? enforcerImage;

  AssignedTowRequest({
    this.uid,
    this.violationUid,
    this.violationId,
    this.violationName,
    this.vehicleType,
    this.makeYear,
    this.numberPlateType,
    this.paymentStatus,
    this.vehicleNumberPlate,
    this.vehicleName,
    this.vehicleImage,
    this.violationTime,
    this.violationStatus,
    this.gracePeriodSeconds,
    this.gracePeriodTime,
    this.enforcerName,
    this.assignedTime,
    this.enforcerImage,
  });

  AssignedTowRequest.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    violationUid = json['violation_uid'];
    violationId = json['violation_id'];
    violationName = json['violation_name'];
    vehicleType = json['vehicle_type'];
    makeYear = json['make_year'];
    numberPlateType = json['number_plate_type'];
    paymentStatus = json['payment_status'];
    vehicleNumberPlate = json['vehicle_number_plate'];
    vehicleName = json['vehicle_name'];
    if (json['vehicle_image'] != null) {
      vehicleImage = <VehicleImage>[];
      json['vehicle_image'].forEach((v) {
        vehicleImage!.add(VehicleImage.fromJson(v));
      });
    }
    violationTime = json['violation_time'];
    violationStatus = json['violation_status'];
    gracePeriodSeconds = json['grace_period_seconds'];
    gracePeriodTime = json['grace_period_time'];
    enforcerName = json['enforcer_name'];
    assignedTime = json['assigned_time'];
    enforcerImage = json['enforcer_image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['violation_uid'] = violationUid;
    data['violation_id'] = violationId;
    data['violation_name'] = violationName;
    data['vehicle_type'] = vehicleType;
    data['make_year'] = makeYear;
    data['number_plate_type'] = numberPlateType;
    data['payment_status'] = paymentStatus;
    data['vehicle_number_plate'] = vehicleNumberPlate;
    data['vehicle_name'] = vehicleName;
    if (vehicleImage != null) {
      data['vehicle_image'] = vehicleImage!.map((v) => v.toJson()).toList();
    }
    data['violation_time'] = violationTime;
    data['violation_status'] = violationStatus;
    data['grace_period_seconds'] = gracePeriodSeconds;
    data['grace_period_time'] = gracePeriodTime;
    data['enforcer_name'] = enforcerName;
    data['assigned_time'] = assignedTime;
    data['enforcer_image'] = enforcerImage;
    return data;
  }
}
