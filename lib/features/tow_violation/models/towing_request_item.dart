import 'package:albalad_operator_app/shared/models/vehicle_image.dart';

class TowingRequestItem {
  String? uid;
  String? violationUid;
  String? violationId;
  String? violationName;
  String? vehicleType;
  int? makeYear;
  String? numberPlateType;
  String? paymentStatus;
  String? vehicleNumberPlate;
  String? vehicleName;
  String? vehicleUid;
  List<VehicleImage>? vehicleImage;
  String? violationTime;
  String? violationStatus;

  TowingRequestItem({
    this.uid,
    this.violationUid,
    this.violationId,
    this.violationName,
    this.vehicleType,
    this.makeYear,
    this.numberPlateType,
    this.paymentStatus,
    this.vehicleNumberPlate,
    this.vehicleName,
    this.vehicleUid,
    this.vehicleImage,
    this.violationTime,
    this.violationStatus,
  });

  TowingRequestItem.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    violationUid = json['violation_uid'];
    violationId = json['violation_id'];
    violationName = json['violation_name'];
    vehicleType = json['vehicle_type'];
    makeYear = int.tryParse(json['make_year'].toString());
    numberPlateType = json['number_plate_type'];
    paymentStatus = json['payment_status'];
    vehicleNumberPlate = json['vehicle_number_plate'];
    vehicleName = json['vehicle_name'];
    vehicleUid = json['vehicle_uid'];
    if (json['vehicle_image'] != null) {
      vehicleImage = <VehicleImage>[];
      json['vehicle_image'].forEach((v) {
        vehicleImage!.add(VehicleImage.fromJson(v));
      });
    }
    violationTime = json['violation_time'];
    violationStatus = json['violation_status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['violation_uid'] = violationUid;
    data['violation_id'] = violationId;
    data['violation_name'] = violationName;
    data['vehicle_type'] = vehicleType;
    data['make_year'] = makeYear;
    data['number_plate_type'] = numberPlateType;
    data['payment_status'] = paymentStatus;
    data['vehicle_number_plate'] = vehicleNumberPlate;
    data['vehicle_name'] = vehicleName;
    data['vehicle_uid'] = vehicleUid;
    if (vehicleImage != null) {
      data['vehicle_image'] = vehicleImage!.map((v) => v.toJson()).toList();
    }
    data['violation_time'] = violationTime;
    data['violation_status'] = violationStatus;
    return data;
  }
}
