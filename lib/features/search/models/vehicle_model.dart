class VehicleModel {
  String? id;
  String? licenseNumberFirst;
  String? licenseNumberLast;
  String? vehicleName;
  String? vehicleId;
  String? year;
  String? color;
  String? vehicleType;
  String? plateType;
  String? vehicleOwnership;
  String? vehicleUsage;
  String? image;
  Violation? violation;
  ParkingReservation? parkingReservation;

  VehicleModel({
    this.id,
    this.licenseNumberFirst,
    this.licenseNumberLast,
    this.vehicleName,
    this.vehicleId,
    this.year,
    this.color,
    this.vehicleType,
    this.plateType,
    this.vehicleOwnership,
    this.vehicleUsage,
    this.image,
    this.violation,
    this.parkingReservation,
  });

  VehicleModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    licenseNumberFirst = json['license_number_first'];
    licenseNumberLast = json['license_number_last'];
    vehicleName = json['vehicle_name'];
    vehicleId = json['vehicle_id'];
    year = json['year'];
    color = json['color'];
    vehicleType = json['vehicle_type'];
    plateType = json['plate_type'];
    vehicleOwnership = json['vehicle_ownership'];
    vehicleUsage = json['vehicle_usage'];
    image = json['image'];
    violation = json['violation'] != null
        ? Violation.fromJson(json['violation'])
        : null;
    parkingReservation = json['parking_reservation'] != null
        ? ParkingReservation.fromJson(json['parking_reservation'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['license_number_first'] = licenseNumberFirst;
    data['license_number_last'] = licenseNumberLast;
    data['vehicle_name'] = vehicleName;
    data['vehicle_id'] = vehicleId;
    data['year'] = year;
    data['color'] = color;
    data['vehicle_type'] = vehicleType;
    data['plate_type'] = plateType;
    data['vehicle_ownership'] = vehicleOwnership;
    data['vehicle_usage'] = vehicleUsage;
    data['image'] = image;
    if (violation != null) {
      data['violation'] = violation!.toJson();
    }
    if (parkingReservation != null) {
      data['parking_reservation'] = parkingReservation!.toJson();
    }
    return data;
  }
}

class Violation {
  String? uid;
  String? name;
  String? status;
  GracePeriod? gracePeriod;
  int? gracePeriodSeconds;
  ViolationType? violationType;

  Violation({
    this.uid,
    this.name,
    this.status,
    this.gracePeriod,
    this.gracePeriodSeconds,
    this.violationType,
  });

  Violation.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    name = json['name'];
    status = json['status'];
    gracePeriod = json['grace_period'] != null
        ? GracePeriod.fromJson(json['grace_period'])
        : null;
    gracePeriodSeconds = json['grace_period_seconds'];
    violationType = json['violation_type'] != null
        ? ViolationType.fromJson(json['violation_type'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['name'] = name;
    data['status'] = status;
    if (gracePeriod != null) {
      data['grace_period'] = gracePeriod!.toJson();
    }
    data['grace_period_seconds'] = gracePeriodSeconds;
    if (violationType != null) {
      data['violation_type'] = violationType!.toJson();
    }
    return data;
  }
}

class GracePeriod {
  String? time;
  bool? gracePeriodEnd;

  GracePeriod({this.time, this.gracePeriodEnd});

  GracePeriod.fromJson(Map<String, dynamic> json) {
    time = json['time'];
    gracePeriodEnd = json['grace_period_end'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['time'] = time;
    data['grace_period_end'] = gracePeriodEnd;
    return data;
  }
}

class ViolationType {
  int? id;
  String? name;

  ViolationType({this.id, this.name});

  ViolationType.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class ParkingReservation {
  String? location;
  String? startTime;
  String? endTime;
  String? totalHours;
  TimeType? timeType;

  ParkingReservation({
    this.location,
    this.startTime,
    this.endTime,
    this.totalHours,
    this.timeType,
  });

  ParkingReservation.fromJson(Map<String, dynamic> json) {
    location = json['location'];
    startTime = json['start_time'];
    endTime = json['end_time'];
    totalHours = json['total_hours'];
    timeType =
        json['time_type'] != null ? TimeType.fromJson(json['time_type']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['location'] = location;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    data['total_hours'] = totalHours;
    if (timeType != null) {
      data['time_type'] = timeType!.toJson();
    }
    return data;
  }
}

class TimeType {
  int? key;
  String? name;

  TimeType({this.key, this.name});

  TimeType.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['key'] = key;
    data['name'] = name;
    return data;
  }
}
