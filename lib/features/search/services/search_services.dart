import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:dio/dio.dart';

class SearchServices {
  final Dio dio = DioClient().dio;
  Future<Response> fetchVehicles(CancelToken? cancelToken,
      {String query = '', int page = 1}) async {
    // Cancel the previous request if it exists
    // cancelToken?.cancel(); // Cancel previous request
    // Create a new CancelToken for the current request
    // cancelToken = CancelToken();
    String apiURL = '${ApiConstants.vehicleSearch}?page=$page&search=$query';
    return await dio.get(
      apiURL,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
      cancelToken: cancelToken,
    );
  }
}
