import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/vehicle_info_chip.dart';
import 'package:albalad_operator_app/features/search/models/vehicle_model.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/widgets/vehicle_parking_timer.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_date_formatter/date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class SearchVehicleCard extends StatelessWidget {
  final VehicleModel vehicle;
  final String previousRoute;
  const SearchVehicleCard(
      {required this.vehicle, required this.previousRoute, super.key});

  @override
  Widget build(BuildContext context) {
    final startTime = vehicle.parkingReservation?.startTime ??
        '2025-01-30T08:27:20.351288+03:00';
    final endTime = vehicle.parkingReservation?.endTime ??
        '2025-01-30T08:27:20.351288+03:00';
    final parkingTime = calculateSecondsFromNow(startTime, endTime);
    return InkWell(
      onTap: () {
        final args = VehicleDetailsScreen(
          uid: vehicle.id,
          previousRoute: previousRoute,
        );
        Navigator.pushNamed(
          context,
          VehicleDetailsScreen.route,
          arguments: args,
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              offset: const Offset(0, 0),
              blurRadius: 20,
              spreadRadius: 0,
            ),
          ],
        ),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (vehicle.vehicleId != null)
                        Text.rich(
                          TextSpan(
                            text: tr(context, 'id'),
                            style: TextStyles.ts12w400c4D4D4D,
                            children: [
                              TextSpan(
                                text: ' ${vehicle.vehicleId}',
                                style: TextStyles.ts12w700c353535,
                              ),
                            ],
                          ),
                        ),
                      Gap(10.h),
                      Row(
                        children: [
                          Container(
                            height: 35.h,
                            width: 35.w,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                              color: ColorConstants.colorF1EFE9,
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8.r),
                              child: CachedNetworkImage(
                                imageUrl: vehicle.image ?? 'https://',
                                memCacheWidth: 200,
                                fit: BoxFit.fill,
                                errorWidget: (context, url, error) =>
                                    Image.asset('car'.asImagePng()),
                              ),
                            ),
                          ),
                          Gap(10.w),
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${vehicle.licenseNumberFirst} ${vehicle.licenseNumberLast}',
                                  style: TextStyles.ts14w600c181818,
                                ),
                                Text(
                                  vehicle.vehicleName ?? '',
                                  style: TextStyles.ts10w400cA1A09B,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Gap(16.h),
                      Wrap(
                        spacing: 8.w,
                        runSpacing: 8.h,
                        children: [
                          if (vehicle.year != null && vehicle.year!.isNotEmpty)
                            VehicleInfoChip(
                              title: '${vehicle.year}',
                            ),
                          if (vehicle.vehicleType != null)
                            VehicleInfoChip(
                              title: vehicle.vehicleType ?? '',
                            ),
                          // if (vehicle.isSharedVehicle == true)
                          //   VehicleInfoChip(
                          //     title: tr(context, 'shared'),
                          //     shared: true,
                          //   ),
                          if (vehicle.plateType != null)
                            VehicleInfoChip(
                              title: vehicle.plateType ?? '',
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
                // if (vehicle.coorparateVehicle != null)
                //   Container(
                //     decoration: BoxDecoration(
                //       borderRadius: BorderRadius.circular(20.r),
                //       color: ColorConstants.colorEAFFEC,
                //     ),
                //     padding: EdgeInsets.symmetric(
                //       horizontal: 10.w,
                //       vertical: 4.h,
                //     ),
                //     child: Text(
                //       vehicle.coorparateVehicle ?? '',
                //       style: TextStyles.ts12w500c32993E,
                //     ),
                //   )
                // else
                if (vehicle.parkingReservation != null)
                  VehicleParkingTimer(
                    totalTimeInSeconds: parkingTime.totalSeconds,
                    remainingTimeInSeconds: parkingTime.remainingSeconds,
                  ),
              ],
            ),
            Gap(16.h),
            Divider(
              color: ColorConstants.colorF1F1F1,
              thickness: 1.h,
              height: 0,
            ),
            Gap(10.h),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tr(context, 'checkInTime'),
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                    Localizations.override(
                      context: context,
                      locale: const Locale('en'),
                      child: Text(
                        vehicle.parkingReservation != null
                            ? DateFormatter.formatStringDate(
                                date:
                                    vehicle.parkingReservation?.startTime ?? '',
                                inputFormat: 'yyyy-MM-ddTHH:mm:ss',
                                outputFormat: 'hh.mm a',
                              )
                            : '-',
                        style: TextStyles.ts12w700c353535,
                      ),
                    )
                  ],
                ),
                Gap(20.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tr(context, 'totalHrs'),
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                    Text(
                      vehicle.parkingReservation != null
                          ? convertSecondsToTime(parkingTime.totalSeconds)
                          : "-",
                      style: TextStyles.ts12w700c353535,
                      textDirection: TextDirection.ltr,
                    )
                  ],
                ),
                if (vehicle.violation?.status != null)
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      mainAxisSize: MainAxisSize.min,
                      spacing: 3.w,
                      children: [
                        SvgPicture.asset(
                          'danger-bold'.asIconSvg(),
                          height: 14.h,
                          width: 14.w,
                        ),
                        Flexible(
                          child: Text(
                            vehicle.violation?.status ?? '',
                            style: TextStyles.ts12w400c4D4D4D,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
