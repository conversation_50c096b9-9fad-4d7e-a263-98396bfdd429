import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/skeleton_current_parked.dart';
import 'package:albalad_operator_app/features/home/<USER>/home_search_field.dart';
import 'package:albalad_operator_app/features/number_plate_scanner/view/number_plate_scanner.dart';
import 'package:albalad_operator_app/features/search/provider/search_provider.dart';
import 'package:albalad_operator_app/features/search/view/search_vehicle_card.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/bottom_add_vehicle_button.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:albalad_operator_app/shared/widgets/smart_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SearchScreen extends HookConsumerWidget {
  static const route = '/search';
  final String? previousRoute;
  const SearchScreen({this.previousRoute, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchController = useTextEditingController();
    final notifier = ref.read(searchNotifierProvider.notifier);
    final state = ref.watch(searchNotifierProvider);

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (state.items.isEmpty) {
          notifier.fetchVehicles();
        }
      });
      return null;
    }, []);

    return SmartScaffold(
      isInternetAvailable: !state.isConnectionError,
      retryConnection: () {
        notifier.reset();
        notifier.fetchVehicles();
      },
      appBar: InnerAppBar(
        title: Text(tr(context, 'search')),
      ),
      body: NotificationListener<ScrollNotification>(
        onNotification: (scrollNotification) {
          if (scrollNotification is ScrollEndNotification &&
              scrollNotification.metrics.extentAfter == 0) {
            notifier.fetchVehicles();
          }
          return false;
        },
        child: RefreshIndicator(
          onRefresh: () {
            notifier.reset();
            return notifier.fetchVehicles();
          },
          child: ListView(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            children: [
              Gap(30.h),
              HomeSearchField(
                autofocus: true,
                controller: searchController,
                hintText: tr(context, 'searchVehicleNumber'),
                onChanged: (query) => notifier.searchVehicle(query: query),
                onTapSuffix: () {
                  final arguments =
                      NumberPlateScanner(previousRoute: previousRoute ?? route);
                  Navigator.of(context).pushNamed(
                    NumberPlateScanner.route,
                    arguments: arguments,
                  );
                },
              ),
              Gap(25.h),
              if (state.items.isEmpty && !state.hasMore) ...[
                Gap(0.2.sh),
                Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Text(
                      tr(context, 'searchedVehiclesNotFound'),
                      style: TextStyles.ts14w400c4D4D4D,
                      textAlign: TextAlign.center,
                    ),
                  ),
                )
              ] else if (state.items.isEmpty && state.isLoading) ...[
                const SkeletonCurrentParked(),
              ] else
                ListView.separated(
                  shrinkWrap: true,
                  physics: const PageScrollPhysics(),
                  itemBuilder: (context, index) {
                    if (index == state.items.length) {
                      return const Center(child: CustomGradientSpinner());
                    }
                    final item = state.items[index];
                    return SearchVehicleCard(
                      vehicle: item,
                      previousRoute: previousRoute ?? route,
                    );
                  },
                  separatorBuilder: (context, index) => SizedBox(height: 24.h),
                  itemCount: state.items.length + (state.hasMore ? 1 : 0),
                ),
              Gap(25.h),
            ],
          ),
        ),
      ),
      bottomNavigationBar: BottomAddVehicleButton(),
    );
  }
}
