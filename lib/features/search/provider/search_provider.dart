import 'dart:async';
import 'dart:io';

import 'package:albalad_operator_app/features/search/models/vehicle_model.dart';
import 'package:albalad_operator_app/features/search/provider/search_vehicle_state.dart';
import 'package:albalad_operator_app/features/search/services/search_services.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'search_provider.g.dart';

@riverpod
class SearchNotifier extends _$SearchNotifier {
  @override
  SearchVehicleState build() {
    return SearchVehicleState(
      items: [],
      isLoading: false,
      hasMore: true,
      error: null,
    );
  }

  int _currentPage = 1;

  Timer? _debounce;

  String _searchQuery = '';

  CancelToken? cancelToken;

  void searchVehicle({String query = ''}) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    _debounce = Timer(const Duration(milliseconds: 500), () {
      cancelToken?.cancel();
      cancelToken = CancelToken();
      _currentPage = 1;
      state = state.copyWith(items: [], isLoading: false, hasMore: true);
      _searchQuery = query;
      fetchVehicles();
    });
  }

  Future<void> fetchVehicles() async {
    if (state.isLoading || !state.hasMore) return;

    state = state.copyWith(
      isLoading: true,
      error: null,
      isConnectionError: false,
    );

    try {
      final response = await SearchServices().fetchVehicles(
        cancelToken,
        page: _currentPage,
        query: _searchQuery,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          final List<dynamic> vehicles = data['records'];
          final List<VehicleModel> vehileList =
              vehicles.map((e) => VehicleModel.fromJson(e)).toList();
          state = state.copyWith(
            items: [...state.items, ...vehileList],
            isLoading: false,
            hasMore: data['has_next'] == true,
          );
          _currentPage++;
        } else {
          state = state.copyWith(
            isLoading: false,
            hasMore: false,
          );
        }
      } else {
        state = state.copyWith(
          isLoading: false,
          hasMore: false,
        );
      }
    } on SocketException {
      state = state.copyWith(
        isLoading: false,
        hasMore: false,
        isConnectionError: true,
      );
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print(e);
        print(stackTrace);
      }
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = state.copyWith(
          isLoading: false,
          hasMore: false,
          isConnectionError: true,
        );
      }
    }
  }

  void reset() {
    _currentPage = 1;
    state = SearchVehicleState(items: [], isLoading: false, hasMore: true);
  }
}
