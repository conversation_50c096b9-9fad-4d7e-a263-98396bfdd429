import 'package:albalad_operator_app/features/search/models/vehicle_model.dart';

class SearchVehicleState {
  final List<VehicleModel> items;
  final bool isLoading;
  final bool hasMore;
  final bool isConnectionError;
  final String? error;

  SearchVehicleState({
    required this.items,
    required this.isLoading,
    required this.hasMore,
    this.isConnectionError = false,
    this.error,
  });

  SearchVehicleState copyWith({
    List<VehicleModel>? items,
    bool? isLoading,
    bool? hasMore,
    bool? isConnectionError,
    String? error,
  }) {
    return SearchVehicleState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      isConnectionError: isConnectionError ?? this.isConnectionError,
      error: error ?? this.error,
    );
  }
}
