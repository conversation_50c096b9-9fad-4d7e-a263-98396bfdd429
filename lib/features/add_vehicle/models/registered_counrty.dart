class RegisteredCountry {
  String? uid;
  String? name;
  String? flag;
  String? iso3;
  String? phonecode;

  RegisteredCountry({
    this.uid,
    this.name,
    this.flag,
    this.iso3,
    this.phonecode,
  });

  RegisteredCountry.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    name = json['name'];
    flag = json['flag'];
    iso3 = json['iso3'];
    phonecode = json['phonecode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['name'] = name;
    data['flag'] = flag;
    data['iso3'] = iso3;
    data['phonecode'] = phonecode;
    return data;
  }
}
