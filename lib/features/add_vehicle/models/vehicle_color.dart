class VehicleColor {
  String? uid;
  String? name;
  String? colorCode;

  VehicleColor({this.uid, this.name, this.colorCode});

  VehicleColor.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    name = json['name'];
    colorCode = json['color_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['name'] = name;
    data['color_code'] = colorCode;
    return data;
  }
}
