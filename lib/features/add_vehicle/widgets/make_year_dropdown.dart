import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class MakeYearDropdown extends StatelessWidget {
  final TextEditingController makeController;
  final int? selectedYear;
  final ValueChanged<int> onChanged;
  const MakeYearDropdown(
      {required this.makeController,
      required this.selectedYear,
      required this.onChanged,
      super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          tr(context, 'make_year'),
          style: TextStyles.ts12w400c505050,
        ),
        Gap(5.h),
        GestureDetector(
          onTap: () async {
            final int currentYear = DateTime.now().year;
            final List<int> years =
                List.generate(48, (index) => currentYear - index);
            years.sort();

            List<Map<String, dynamic>> yearRanges =
                convertToYearRanges(years, 16);

            int? year = await showModalBottomSheet(
              context: context,
              builder: (context) => CustomYearPicker(
                yearRanges: yearRanges,
                selectedYear: selectedYear ?? currentYear,
              ),
            );
            if (year != null) {
              makeController.text = year.toString();
              onChanged(year);
            }
          },
          child: AbsorbPointer(
            child: TextFormField(
              controller: makeController,
              decoration: InputDecoration(
                hintText: tr(context, 'select_year'),
                suffixIcon: Icon(Icons.keyboard_arrow_down_rounded),
              ),
            ),
          ),
        ),
      ],
    );
  }

  List<Map<String, dynamic>> convertToYearRanges(
      List<int> years, int rangeSize) {
    List<Map<String, dynamic>> result = [];
    for (int i = 0; i < years.length; i += rangeSize) {
      int start = years[i];
      int end = (i + rangeSize - 1 < years.length)
          ? years[i + rangeSize - 1]
          : years.last;
      result.add(
        {
          "title": "$start-$end",
          "years": years.sublist(
              i, (i + rangeSize < years.length) ? i + rangeSize : years.length)
        },
      );
    }
    return result;
  }
}

class CustomYearPicker extends HookWidget {
  final List<Map<String, dynamic>> yearRanges;
  final int selectedYear;
  const CustomYearPicker(
      {required this.yearRanges, required this.selectedYear, super.key});

  @override
  Widget build(BuildContext context) {
    final PageController pageController =
        usePageController(initialPage: yearRanges.length - 1);
    return SizedBox(
      height: 342.h,
      child: PageView.builder(
        controller: pageController,
        itemCount: yearRanges.length,
        itemBuilder: (context, index) {
          List<int> years = yearRanges[index]["years"];
          String title = yearRanges[index]["title"];
          bool hasPrevious = index > 0;
          bool hasNext = index < yearRanges.length - 1;
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      onPressed: hasPrevious
                          ? () => pageController.previousPage(
                                duration: Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              )
                          : null,
                      icon: SvgPicture.asset(
                        'arrow-left-profile'.asIconSvg(),
                        colorFilter: ColorFilter.mode(
                          hasPrevious
                              ? ColorConstants.color94684E
                              : Colors.grey,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    Text(title, style: TextStyles.ts18w600c44322D),
                    IconButton(
                      onPressed: hasNext
                          ? () => pageController.nextPage(
                                duration: Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              )
                          : null,
                      icon: Transform.flip(
                        flipX: true,
                        child: SvgPicture.asset(
                          'arrow-left-profile'.asIconSvg(),
                          colorFilter: ColorFilter.mode(
                            hasNext ? ColorConstants.color94684E : Colors.grey,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                Gap(30.h),
                Wrap(
                  runSpacing: 19.h,
                  children: years.map(
                    (e) {
                      return InkWell(
                        onTap: () => Navigator.pop(context, e),
                        child: Container(
                          width: (1.sw - 32.w) / 4,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            color: e == selectedYear
                                ? ColorConstants.colorE1DDD2
                                : Colors.transparent,
                          ),
                          alignment: Alignment.center,
                          padding: EdgeInsets.symmetric(vertical: 5.h),
                          child: Text(
                            e.toString(),
                            style: TextStyles.ts16w600c44322D,
                          ),
                        ),
                      );
                    },
                  ).toList(),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
