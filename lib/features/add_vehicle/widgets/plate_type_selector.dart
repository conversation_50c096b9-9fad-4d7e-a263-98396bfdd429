import 'package:albalad_operator_app/features/add_vehicle/models/vehicle_plate_type.dart';
import 'package:albalad_operator_app/features/add_vehicle/providers/providers.dart';
import 'package:albalad_operator_app/features/add_vehicle/widgets/skeleton_plate_type.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class PlateTypeSelector extends ConsumerWidget {
  final ValueChanged<VehiclePlateType> onSelected;
  final VehiclePlateType? selectedPlateType;
  const PlateTypeSelector(
      {required this.onSelected, this.selectedPlateType, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final plateProvider = ref.watch(vehiclePlateTypeProvider);
    return plateProvider.when(
      data: (data) {
        return FormField(
          autovalidateMode: AutovalidateMode.onUserInteraction,
          validator: (value) {
            if (selectedPlateType == null) {
              return tr(context, 'please_select_plate_type');
            }
            return null;
          },
          builder: (field) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  tr(context, 'select_plate_type_star'),
                  style: TextStyles.ts12w400c505050,
                ),
                Gap(15.h),
                Wrap(
                  spacing: 10.w,
                  runSpacing: 10.h,
                  children: data.map((e) {
                    return InkWell(
                      onTap: () => onSelected(e),
                      child: PlateTypeChoice(
                        color: e.colorCode ?? '',
                        name: e.name ?? '',
                        isSelected: e == selectedPlateType,
                      ),
                    );
                  }).toList(),
                ),
                if (field.hasError) ...[
                  Gap(10.h),
                  Text(
                    field.errorText!,
                    style: TextStyles.error(context),
                  ),
                ],
              ],
            );
          },
        );
      },
      error: (error, stackTrace) => const SizedBox(),
      loading: () => SkeletonPlateType(),
    );
  }
}

class PlateTypeChoice extends StatelessWidget {
  final String color;
  final String name;
  final bool isSelected;
  const PlateTypeChoice(
      {required this.color,
      required this.name,
      this.isSelected = false,
      super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ColorConstants.colorF1EFE9,
        borderRadius: BorderRadius.circular(150.r),
        border: isSelected
            ? Border.all(color: ColorConstants.color94684E, width: 1.5)
            : null,
      ),
      padding: EdgeInsets.symmetric(vertical: 3.h),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Gap(4.w),
          Container(
            decoration: BoxDecoration(
              color: hexToColor(color),
              shape: BoxShape.circle,
              border: isSelected
                  ? Border.all(
                      color: ColorConstants.colorBDB7B7,
                      width: 1.5,
                    )
                  : Border.all(
                      color: Colors.white,
                    ),
            ),
            width: 26.w,
            height: 26.h,
          ),
          Gap(8.w),
          Text(
            name,
            style: TextStyles.ts12w600c44322D,
          ),
          Gap(12.w),
        ],
      ),
    );
  }
}
