import 'package:albalad_operator_app/features/add_vehicle/widgets/plate_type_selector.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SkeletonPlateType extends StatelessWidget {
  const SkeletonPlateType({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            tr(context, 'select_plate_type_star'),
            style: TextStyles.ts12w400c505050,
          ),
          Gap(15.h),
          Wrap(
            spacing: 10.w,
            runSpacing: 10.h,
            children: [
              PlateTypeChoice(
                color: "#FFFFFF",
                name: "Private",
              ),
              PlateTypeChoice(
                color: "#FFFFFF",
                name: "Taxi",
              ),
              PlateTypeChoice(
                color: "#FFFFFF",
                name: "Diplomatic",
              ),
              PlateTypeChoice(
                color: "#FFFFFF",
                name: "Transportation",
              ),
            ],
          ),
        ],
      ),
    );
  }
}
