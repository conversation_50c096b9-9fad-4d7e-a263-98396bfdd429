import 'package:albalad_operator_app/features/add_vehicle/models/vehicle_color.dart';
import 'package:albalad_operator_app/features/add_vehicle/providers/providers.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_searchable_dropdown_with_leading.dart';
import 'package:albalad_operator_app/shared/widgets/skeleton_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class VehicleColorDropdown extends ConsumerWidget {
  final VehicleColor? value;
  final AutovalidateMode? autovalidateMode;
  final Color? fillColor;
  final void Function(VehicleColor?)? onChanged;
  const VehicleColorDropdown({
    required this.value,
    required this.onChanged,
    this.autovalidateMode,
    this.fillColor,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorProvider = ref.watch(vehicleColorProvider);
    return colorProvider.when(
      data: (colors) {
        return CustomSearchableDropdownWithLeading<VehicleColor>(
          items: colors,
          displayBuilder: (p0) => p0.name ?? '',
          leadingBuiler: (p0) => Container(
            width: 18.w,
            height: 18.h,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: ColorConstants.colorCCB5A7, width: 1.w),
              color: hexToColor(p0.colorCode ?? '#000000'),
            ),
          ),
          onChanged: onChanged,
          fillColor: fillColor,
          labelText: tr(context, 'color'),
          title: tr(context, 'color'),
          hintText: tr(context, 'select_color'),
          searchHintText: tr(context, 'search_color'),
          autovalidateMode: autovalidateMode,
          value: value,
        );
      },
      error: (error, stackTrace) => SkeletonDropdown(enabled: true),
      loading: () => const SkeletonDropdown(),
    );
  }
}
