import 'dart:io';

import 'package:albalad_operator_app/features/violation/widgets/initial_image_upload_card.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/helper/image_picker_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';

class VehicleImageUpload extends StatelessWidget {
  final ValueChanged<XFile?> onImageUploaded;
  final XFile? image;
  const VehicleImageUpload(
      {required this.onImageUploaded, required this.image, super.key});

  @override
  Widget build(BuildContext context) {
    if (image != null) {
      double width = (1.sw - 32.w) / 2;
      return Container(
        alignment: Alignment.centerLeft,
        width: width,
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10.r),
              child: Image.file(
                File(image!.path),
                height: 154.h,
                width: width,
                fit: BoxFit.cover,
              ),
            ),
            Positioned(
              right: 8,
              top: 8,
              child: InkWell(
                onTap: () => onImageUploaded(null),
                child: Container(
                  width: 24.w,
                  height: 24.h,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: ColorConstants.colorF26464,
                  ),
                  alignment: Alignment.center,
                  child: Icon(
                    Icons.delete_rounded,
                    size: 18,
                    color: Colors.white,
                  ),
                ),
              ),
            )
          ],
        ),
      );
    }
    return InitialImageUploadCard(onTap: () async {
      onImageUploaded(await ImagePickerHelper.pickImage(context));
    });
  }
}
