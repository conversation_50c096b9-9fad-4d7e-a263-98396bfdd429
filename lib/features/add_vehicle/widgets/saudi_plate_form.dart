import 'package:albalad_operator_app/features/add_vehicle/view/add_vehicle_screen.dart';
import 'package:albalad_operator_app/features/number_plate_scanner/view/number_plate_scanner.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/helper/number_plate_helper.dart';
import 'package:albalad_operator_app/shared/widgets/custom_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class SaudiPlateForm extends StatelessWidget {
  final TextEditingController saudiPlateEnFirstController;
  final TextEditingController saudiPlateEnSecondController;
  final TextEditingController saudiPlateArFirstController;
  final TextEditingController saudiPlateArSecondController;
  const SaudiPlateForm({
    required this.saudiPlateEnFirstController,
    required this.saudiPlateEnSecondController,
    required this.saudiPlateArFirstController,
    required this.saudiPlateArSecondController,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return FormField(
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: (value) {
        if (saudiPlateEnFirstController.text.isEmpty ||
            saudiPlateEnSecondController.text.isEmpty) {
          return tr(context, 'please_enter_license_plate_number');
        }
        if (saudiPlateEnFirstController.text !=
            convertToEnglish(saudiPlateArFirstController.text)) {
          return tr(context, 'english_and_arabic_plate_letters_should_match');
        }
        return null;
      },
      builder: (field) {
        // bool isEmpty = saudiPlateEnFirstController.text.isEmpty ||
        //     saudiPlateEnSecondController.text.isEmpty;
        // bool isMatch = saudiPlateEnFirstController.text ==
        //     convertToEnglish(saudiPlateArFirstController.text);
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              onTap: () async {
                final arguments = NumberPlateScanner(
                  previousRoute: AddVehicleScreen.route,
                );
                final result = await Navigator.pushNamed(
                    context, NumberPlateScanner.route,
                    arguments: arguments);
                if (result != null) {
                  List<String> plate = result.toString().split(' ');
                  String plateFirst = plate[0];
                  String plateLast = plate[1];
                  saudiPlateEnFirstController.text = plateFirst;
                  saudiPlateEnSecondController.text = plateLast;
                  saudiPlateArFirstController.text =
                      convertToArabic(plateFirst);
                  String reversed = plateLast.split('').reversed.join();
                  saudiPlateArSecondController.text =
                      convertToArabic(reversed.replaceAll('', ' '));
                }
              },
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: ColorConstants.primaryColor.withValues(alpha: 0.2),
                    width: 1.w,
                  ),
                  borderRadius: BorderRadius.circular(10.r),
                  color: Colors.white,
                ),
                padding: EdgeInsets.symmetric(vertical: 14.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Image.asset(
                      'assets/images/scan-plate.png',
                      width: 120.89.w,
                      height: 69.h,
                    ),
                    Gap(10.h),
                    Text(
                      tr(context, 'scan_plate'),
                      textAlign: TextAlign.center,
                      style: TextStyles.ts14w600CECECE,
                    ),
                  ],
                ),
              ),
            ),
            Gap(25.h),
            Center(
              child: Text(
                tr(context, 'or'),
                textAlign: TextAlign.center,
                style: TextStyles.ts12w400c4D4D4D,
              ),
            ),
            Gap(25.h),
            Text(
              tr(context, 'license_plate_number_star'),
              style: TextStyles.ts12w400c505050,
            ),
            Gap(18.h),
            Row(
              spacing: 10.w,
              children: [
                Expanded(
                  child: CustomTextFormField(
                    hintText: 'Eg-7403',
                    controller: saudiPlateEnFirstController,
                    keyboardType: TextInputType.number,
                    maxLength: 4,
                    fillColor: Colors.white,
                    onChanged: (value) {
                      saudiPlateArFirstController.text = convertToArabic(value);
                      if (value.length == 4) {
                        FocusScope.of(context).nextFocus();
                      }
                    },
                  ),
                ),
                Expanded(
                  child: CustomTextFormField(
                    hintText: 'Eg-TNJ',
                    controller: saudiPlateEnSecondController,
                    maxLength: 3,
                    fillColor: Colors.white,
                    textCapitalization: TextCapitalization.characters,
                    inputFormatters: [
                      // Only allow English letters
                      FilteringTextInputFormatter.allow(RegExp(r'[A-Za-z]')),
                    ],
                    onChanged: (value) {
                      // String reversed = value.split('').reversed.join();
                      saudiPlateArSecondController.text =
                          convertToArabic(value.replaceAll('', ' '));
                      if (value.length == 3) {
                        FocusScope.of(context).unfocus();
                      }
                    },
                  ),
                ),
              ],
            ),
            Gap(13.h),
            Row(
              spacing: 10.w,
              children: [
                Expanded(
                  child: CustomTextFormField(
                    hintText: '۷٤۰۳',
                    fillColor: Colors.white,
                    controller: saudiPlateArFirstController,
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      saudiPlateEnFirstController.text =
                          convertToEnglish(value);
                    },
                  ),
                ),
                Expanded(
                  child: CustomTextFormField(
                    hintText: 'ح ن ط',
                    fillColor: Colors.white,
                    controller: saudiPlateArSecondController,
                    inputFormatters: [
                      // Only allow Arabic letters and spaces
                      FilteringTextInputFormatter.allow(
                        RegExp(r'[\u0600-\u06FF\s]'),
                      ),
                    ],
                    onChanged: (value) {
                      // Automatically insert spaces between letters
                      String newValue = value.replaceAll(' ', '');
                      String spaced = newValue.split('').join(' ');
                      if (value != spaced) {
                        saudiPlateArSecondController.value = TextEditingValue(
                          text: spaced,
                          selection:
                              TextSelection.collapsed(offset: spaced.length),
                        );
                      }
                      // Convert Arabic to English and update saudiPlateEnSecondController
                      String englishValue = convertToEnglish(newValue);
                      saudiPlateEnSecondController.text = englishValue;
                    },
                  ),
                ),
              ],
            ),
            if (field.hasError) ...[
              Gap(10.h),
              Text(
                field.errorText!,
                style: TextStyles.error(context),
              )
            ],
          ],
        );
      },
    );
  }
}
