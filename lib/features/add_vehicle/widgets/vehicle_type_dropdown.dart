import 'package:albalad_operator_app/features/add_vehicle/models/vehicle_type.dart';
import 'package:albalad_operator_app/features/add_vehicle/providers/providers.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_searchable_dropdown.dart';
import 'package:albalad_operator_app/shared/widgets/skeleton_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class VehicleTypeDropdown extends ConsumerWidget {
  final VehicleType? value;
  final AutovalidateMode? autovalidateMode;
  final Color? fillColor;
  final void Function(VehicleType?)? onChanged;
  const VehicleTypeDropdown({
    required this.value,
    required this.onChanged,
    this.autovalidateMode,
    this.fillColor,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final typeProvider = ref.watch(vehicleTypeProvider);
    return typeProvider.when(
      data: (types) {
        return CustomSearchableDropdown<VehicleType>(
          items: types,
          displayBuilder: (p0) => p0.name ?? '',
          onChanged: onChanged,
          fillColor: fillColor,
          labelText: tr(context, 'vehicle_type_star'),
          title: tr(context, 'vehicle_type'),
          hintText: tr(context, 'select_type'),
          searchHintText: tr(context, 'search_type'),
          autovalidateMode: autovalidateMode,
          value: value,
          validator: (p0) {
            if (p0 == null || p0.isEmpty) {
              return tr(context, 'please_select_vehicle_type');
            }
            return null;
          },
        );
      },
      error: (error, stackTrace) => SkeletonDropdown(enabled: true),
      loading: () => const SkeletonDropdown(),
    );
  }
}
