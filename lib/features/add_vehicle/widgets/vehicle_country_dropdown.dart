import 'package:albalad_operator_app/features/add_vehicle/models/registered_counrty.dart';
import 'package:albalad_operator_app/features/add_vehicle/providers/providers.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_searchable_dropdown.dart';
import 'package:albalad_operator_app/shared/widgets/skeleton_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class VehicleCountryDropdown extends ConsumerWidget {
  final RegisteredCountry? value;
  final AutovalidateMode? autovalidateMode;
  final Color? fillColor;
  final void Function(RegisteredCountry?)? onChanged;
  const VehicleCountryDropdown({
    required this.value,
    required this.onChanged,
    this.autovalidateMode,
    this.fillColor,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final countryProvider = ref.watch(vehicleCountryProvider);
    return countryProvider.when(
      data: (countries) {
        return CustomSearchableDropdown<RegisteredCountry>(
          items: countries,
          displayBuilder: (p0) => p0.name ?? '',
          onChanged: onChanged,
          fillColor: fillColor,
          labelText: tr(context, 'vehicle_registered_country_star'),
          title: tr(context, 'vehicle_registered_country'),
          hintText: tr(context, 'select_country'),
          searchHintText: tr(context, 'search_country'),
          autovalidateMode: autovalidateMode,
          value: value,
          validator: (p0) {
            if (p0 == null || p0.isEmpty) {
              return tr(context, 'please_select_vehicle_registered_country');
            }
            return null;
          },
        );
      },
      error: (error, stackTrace) => SkeletonDropdown(enabled: true),
      loading: () => const SkeletonDropdown(),
    );
  }
}
