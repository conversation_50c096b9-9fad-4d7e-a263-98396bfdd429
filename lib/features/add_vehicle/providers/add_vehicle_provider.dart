import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'add_vehicle_provider.g.dart';

abstract class AddVehicleState {}

class AddVehicleInitial extends AddVehicleState {}

class AddVehicleLoading extends AddVehicleState {}

class AddVehicleSuccess extends AddVehicleState {
  final String vehicleUid;

  AddVehicleSuccess(this.vehicleUid);
}

class AddVehicleError extends AddVehicleState {
  final String message;

  AddVehicleError(this.message);
}

@riverpod
class AddVehicleNotifier extends _$AddVehicleNotifier {
  @override
  AddVehicleState build() {
    return AddVehicleInitial();
  }

  //vehicle_name:Ford Bronco
//country:79561fc7-85e3-460d-834f-b86eca7249b1
//year:1980
//color:509ad292-6d8b-452a-b406-e00c4185c335
//vehicle_type:a1c07723-35bc-4d43-8525-d81edc896569
//plate_type:fdacfa93-a905-4bde-8517-aaf70edd7ce8
//license_plate_no_first_en:5578
//license_plate_no_first_ar:٥٥٧٨
//license_plate_no_last_en:KGA
//license_plate_no_last_ar:كعا
//vehicle_ownership:c16ad534-8222-46d6-a1b2-59ed9c8f6570
//is_primary_vehicle:true

  final _dio = DioClient().dio;

  Future<void> addVehicle({
    required String vehicleName,
    required String plateType,
    required String country,
    required String vehicleType,
    required String licensePlateNoFirstEn,
    required String licensePlateNoFirstAr,
    required String licensePlateNoLastEn,
    required String licensePlateNoLastAr,
    required String plateNumber,
    String? makeYear,
    String? color,
    String? contactNumber,
    String? carImagePath,
    bool isSaudi = false,
  }) async {
    state = AddVehicleLoading();
    try {
      Map<String, dynamic> body = {
        "vehicle_name": vehicleName,
        "country": country,
        "vehicle_type": vehicleType,
        "plate_type": plateType,
      };
      if (makeYear != null) {
        body["year"] = makeYear;
      }
      if (color != null) {
        body["color"] = color;
      }
      if (isSaudi) {
        body["license_plate_no_first_en"] = licensePlateNoFirstEn;
        body["license_plate_no_first_ar"] = licensePlateNoFirstAr;
        body["license_plate_no_last_en"] = licensePlateNoLastEn;
        body["license_plate_no_last_ar"] = licensePlateNoLastAr;
      } else {
        body["license_plate_no_first_en"] = plateNumber;
      }
      if (contactNumber != null && contactNumber.isNotEmpty) {
        body["contact_no"] = contactNumber;
      }
      if (carImagePath != null) {
        body["vehicle_image"] = await MultipartFile.fromFile(carImagePath);
      }
      FormData formData = FormData.fromMap(body);
      final response = await _dio.post(
        ApiConstants.vehicle,
        options: Options(
          headers: await ApiConstants.authFormDataHeaders(),
        ),
        data: formData,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          state = AddVehicleSuccess(data['uid']);
          return;
        }
      }
      if (response.statusCode == 400) {
        final data = response.data;
        Map<String, dynamic> errors = data['errors'] ?? {};
        if (errors.isNotEmpty) {
          state = AddVehicleError(errors.values.first);
          return;
        }
      }
      state = AddVehicleError(appLocalization.translate('somethingWentWrong'));
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = AddVehicleError(appLocalization.translate('networkError'));
        return;
      }
      state = AddVehicleError(e.toString());
    }
  }
}
