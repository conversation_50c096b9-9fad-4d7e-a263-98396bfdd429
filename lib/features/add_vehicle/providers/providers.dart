import 'package:albalad_operator_app/features/add_vehicle/models/registered_counrty.dart';
import 'package:albalad_operator_app/features/add_vehicle/models/vehicle_color.dart';
import 'package:albalad_operator_app/features/add_vehicle/models/vehicle_plate_type.dart';
import 'package:albalad_operator_app/features/add_vehicle/models/vehicle_type.dart';
import 'package:albalad_operator_app/shared/services/vehicle_services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Define a Riverpod provider for the VehicleServices class
final vehicleServicesProvider = Provider<VehicleServices>((ref) {
  return VehicleServices();
});

final vehiclePlateTypeProvider =
    FutureProvider.autoDispose<List<VehiclePlateType>>((ref) async {
  final vehicleServices = ref.read(vehicleServicesProvider);
  return await vehicleServices.fetchVehiclePlateTypes();
});

final selectedCountryProvider =
    StateProvider<RegisteredCountry?>((ref) => null);
final vehicleCountryProvider =
    FutureProvider.autoDispose<List<RegisteredCountry>>((ref) async {
  final vehicleServices = ref.read(vehicleServicesProvider);
  final countries = await vehicleServices.fetchRegisteredCountry();

  // Find Saudi Country
  final saudiCountry = countries.firstWhere(
    (country) => country.phonecode == "966",
  );

  // Set the default selected country
  ref.read(selectedCountryProvider.notifier).state = saudiCountry;

  return countries;
});

final vehicleColorProvider =
    FutureProvider.autoDispose<List<VehicleColor>>((ref) async {
  final vehicleServices = ref.read(vehicleServicesProvider);
  return await vehicleServices.fetchVehicleColors();
});

final vehicleTypeProvider =
    FutureProvider.autoDispose<List<VehicleType>>((ref) async {
  final vehicleServices = ref.read(vehicleServicesProvider);
  return await vehicleServices.fetchVehicleTypes();
});
