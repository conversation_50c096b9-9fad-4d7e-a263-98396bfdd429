// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_vehicle_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$addVehicleNotifierHash() =>
    r'b5653b6f92a4144410cff6ded4b5c14aef2fcc46';

/// See also [AddVehicleNotifier].
@ProviderFor(AddVehicleNotifier)
final addVehicleNotifierProvider =
    AutoDisposeNotifierProvider<AddVehicleNotifier, AddVehicleState>.internal(
  AddVehicleNotifier.new,
  name: r'addVehicleNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$addVehicleNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AddVehicleNotifier = AutoDisposeNotifier<AddVehicleState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
