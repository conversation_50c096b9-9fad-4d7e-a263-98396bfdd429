import 'package:albalad_operator_app/features/add_vehicle/models/registered_counrty.dart';
import 'package:albalad_operator_app/features/add_vehicle/models/vehicle_color.dart';
import 'package:albalad_operator_app/features/add_vehicle/models/vehicle_plate_type.dart';
import 'package:albalad_operator_app/features/add_vehicle/models/vehicle_type.dart';
import 'package:albalad_operator_app/features/add_vehicle/providers/add_vehicle_provider.dart';
import 'package:albalad_operator_app/features/add_vehicle/providers/providers.dart';
import 'package:albalad_operator_app/features/add_vehicle/view/confirm_vehicle_plate_screen.dart';
import 'package:albalad_operator_app/features/add_vehicle/widgets/make_year_dropdown.dart';
import 'package:albalad_operator_app/features/add_vehicle/widgets/plate_type_selector.dart';
import 'package:albalad_operator_app/features/add_vehicle/widgets/saudi_plate_form.dart';
import 'package:albalad_operator_app/features/add_vehicle/widgets/vehicle_color_dropdown.dart';
import 'package:albalad_operator_app/features/add_vehicle/widgets/vehicle_country_dropdown.dart';
import 'package:albalad_operator_app/features/add_vehicle/widgets/vehicle_image_upload.dart';
import 'package:albalad_operator_app/features/add_vehicle/widgets/vehicle_type_dropdown.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/helper/dialog_helper.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/custom_text_form_field.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:albalad_operator_app/shared/widgets/mobile_number_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:phone_numbers_parser/phone_numbers_parser.dart';

class AddVehicleScreen extends HookConsumerWidget {
  static const route = '/add_vehicle';
  const AddVehicleScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final selectedPlateType = useState<VehiclePlateType?>(null);
    final selectedCountry = useState<RegisteredCountry?>(null);

    final saudiPlateEnFirstController = useTextEditingController();
    final saudiPlateArFirstController = useTextEditingController();
    final saudiPlateEnSecondController = useTextEditingController();
    final saudiPlateArSecondController = useTextEditingController();

    final otherPlateController = useTextEditingController();
    final vehicleNameController = useTextEditingController();

    final makeYearController = useTextEditingController();
    final selectedMakeYear = useState<int?>(null);
    final selectedVehicleColor = useState<VehicleColor?>(null);
    final selectedVehicleType = useState<VehicleType?>(null);
    final phoneCode = useState('+966');
    final maxPhoneLength = useState(9);
    final phoneController = useTextEditingController();
    final selectedCarImage = useState<XFile?>(null);

    ref.listen(selectedCountryProvider, (previous, next) {
      if (next != null && selectedCountry.value == null) {
        selectedCountry.value = next;
      }
    });

    ref.listen(
      addVehicleNotifierProvider,
      (previous, next) {
        if (next is AddVehicleSuccess) {
          final arguments = VehicleDetailsScreen(uid: next.vehicleUid);
          Navigator.pushReplacementNamed(context, VehicleDetailsScreen.route,
              arguments: arguments);
        }
        if (next is AddVehicleError) {
          DialogHelper.showErrorDialog(context: context, message: next.message);
        }
      },
    );

    final notifier = ref.read(addVehicleNotifierProvider.notifier);
    final state = ref.watch(addVehicleNotifierProvider);
    return Scaffold(
      appBar: InnerAppBar(
        title: Text(tr(context, 'add_new_vehicle')),
      ),
      body: Form(
        key: formKey,
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            Container(
              color: ColorConstants.colorFAFAFA,
              padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 42.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  PlateTypeSelector(
                    onSelected: (value) => selectedPlateType.value = value,
                    selectedPlateType: selectedPlateType.value,
                  ),
                  Gap(24.h),
                  VehicleCountryDropdown(
                    value: selectedCountry.value,
                    onChanged: (p0) => selectedCountry.value = p0,
                    fillColor: Colors.white,
                  ),
                  Gap(34.h),
                  if (selectedCountry.value?.phonecode == "966")
                    SaudiPlateForm(
                      saudiPlateEnFirstController: saudiPlateEnFirstController,
                      saudiPlateEnSecondController:
                          saudiPlateEnSecondController,
                      saudiPlateArFirstController: saudiPlateArFirstController,
                      saudiPlateArSecondController:
                          saudiPlateArSecondController,
                    )
                  else
                    CustomTextFormField(
                      labelText: tr(context, 'license_plate_number_star'),
                      hintText: tr(context, 'license_plate_number'),
                      textCapitalization: TextCapitalization.characters,
                      controller: otherPlateController,
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return tr(
                              context, 'please_enter_license_plate_number');
                        }
                        return null;
                      },
                    ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(16.w, 38.h, 16.w, 20.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  CustomTextFormField(
                    labelText: tr(context, 'vehicle_name_star'),
                    hintText: tr(context, 'vehicle_name_hint_text'),
                    textCapitalization: TextCapitalization.words,
                    controller: vehicleNameController,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    validator: (p0) {
                      if (p0 == null || p0.isEmpty) {
                        return tr(context, 'please_enter_vehicle_name');
                      }
                      return null;
                    },
                  ),
                  Gap(24.h),
                  MakeYearDropdown(
                    makeController: makeYearController,
                    selectedYear: selectedMakeYear.value,
                    onChanged: (value) => selectedMakeYear.value = value,
                  ),
                  Gap(24.h),
                  VehicleColorDropdown(
                    value: selectedVehicleColor.value,
                    onChanged: (p0) => selectedVehicleColor.value = p0,
                  ),
                  Gap(24.h),
                  VehicleTypeDropdown(
                    value: selectedVehicleType.value,
                    onChanged: (p0) => selectedVehicleType.value = p0,
                  ),
                  Gap(24.h),
                  Text(
                    tr(context, 'car_image'),
                    style: TextStyles.ts12w400c505050,
                  ),
                  Gap(5.h),
                  VehicleImageUpload(
                    onImageUploaded: (value) => selectedCarImage.value = value,
                    image: selectedCarImage.value,
                  ),
                  Gap(24.h),
                  Text(
                    tr(context, 'contact_number'),
                    style: TextStyles.ts12w400c505050,
                  ),
                  Gap(5.h),
                  MobileNumberField(
                    controller: phoneController,
                    phoneCode: phoneCode.value,
                    hintText: tr(context, 'enter_mobile_number'),
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    onPhoneCodeChanged: (p0) {
                      phoneCode.value = p0.phoneCode;
                      maxPhoneLength.value =
                          getPhoneNumberLength(p0.countryCode);
                    },
                    maxLength: maxPhoneLength.value,
                    validator: (p0) {
                      if (p0 != null && p0.isNotEmpty) {
                        final frPhone =
                            PhoneNumber.parse('${phoneCode.value}$p0');
                        final validMobile =
                            frPhone.isValid(type: PhoneNumberType.mobile);
                        if (!validMobile) {
                          return tr(
                              context, 'please_enter_valid_mobile_number');
                        }
                      }
                      return null;
                    },
                  ),
                  Gap(24.h),
                  if (state is AddVehicleLoading)
                    const Center(
                      child: CustomGradientSpinner(),
                    )
                  else
                    ElevatedButton(
                      onPressed: () async {
                        if (formKey.currentState?.validate() == true) {
                          if (selectedPlateType.value != null) {
                            String vehicleName =
                                vehicleNameController.text.trim();
                            String plateType =
                                selectedPlateType.value?.uid ?? '';
                            String country = selectedCountry.value?.uid ?? '';
                            String vehicleType =
                                selectedVehicleType.value?.uid ?? '';
                            bool isSaudi =
                                selectedCountry.value?.phonecode == "966";
                            String licensePlateNoFirstEn =
                                saudiPlateEnFirstController.text;
                            String licensePlateNoLastEn =
                                saudiPlateEnSecondController.text;
                            String licensePlateNoFirstAr =
                                saudiPlateArFirstController.text;
                            String licensePlateNoLastAr =
                                saudiPlateArSecondController.text;
                            String plateNumber = otherPlateController.text;
                            String? makeYear;
                            String? color;
                            String? contactNumber;
                            String? carImagePath;

                            if (selectedMakeYear.value != null) {
                              makeYear = selectedMakeYear.value.toString();
                            }
                            if (selectedVehicleColor.value != null) {
                              color = selectedVehicleColor.value?.uid;
                            }
                            if (phoneController.text.isNotEmpty) {
                              contactNumber =
                                  '${phoneCode.value.startsWith('+') ? '' : '+'}${phoneCode.value}${phoneController.text}';
                            }
                            if (selectedCarImage.value != null) {
                              carImagePath = selectedCarImage.value?.path;
                            }
                            if (isSaudi) {
                              final arguments = ConfirmVehiclePlateScreen(
                                englishNumbers: licensePlateNoFirstEn,
                                arabicNumbers: licensePlateNoFirstAr,
                                englishLetters: licensePlateNoLastEn,
                                arabicLetters: licensePlateNoLastAr,
                              );
                              final isConfirm = await Navigator.pushNamed(
                                  context, ConfirmVehiclePlateScreen.route,
                                  arguments: arguments);
                              if (isConfirm != true) {
                                return;
                              }
                            }
                            notifier.addVehicle(
                              vehicleName: vehicleName,
                              plateType: plateType,
                              country: country,
                              vehicleType: vehicleType,
                              licensePlateNoFirstEn: licensePlateNoFirstEn,
                              licensePlateNoFirstAr: licensePlateNoFirstAr,
                              licensePlateNoLastEn: licensePlateNoLastEn,
                              licensePlateNoLastAr: licensePlateNoLastAr,
                              plateNumber: plateNumber,
                              makeYear: makeYear,
                              color: color,
                              contactNumber: contactNumber,
                              carImagePath: carImagePath,
                              isSaudi: isSaudi,
                            );
                          }
                        }
                      },
                      child: Text(tr(context, 'save_vehicle')),
                    ),
                  Gap(30.h),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
