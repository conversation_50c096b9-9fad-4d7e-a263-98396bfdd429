import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/elevated_secondary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class ConfirmVehiclePlateScreen extends StatelessWidget {
  static const route = '/confirm-vehicle-plate';
  final String englishNumbers;
  final String arabicNumbers;
  final String englishLetters;
  final String arabicLetters;
  const ConfirmVehiclePlateScreen(
      {required this.englishNumbers,
      required this.arabicNumbers,
      required this.englishLetters,
      required this.arabicLetters,
      super.key});

  @override
  Widget build(BuildContext context) {
    double bottom = MediaQuery.of(context).padding.bottom;
    if (bottom == 0) {
      bottom = 20.h;
    }
    return Scaffold(
      backgroundColor: ColorConstants.colorE1DDD2,
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Stack(
          children: [
            Positioned(
              bottom: bottom,
              left: 0,
              right: 0,
              child: Column(
                children: [
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context, true),
                    child: Text(tr(context, 'confirm_and_save')),
                  ),
                  Gap(10.h),
                  ElevatedSecondaryButton(
                    title: tr(context, 'back_to_edit'),
                    onPressed: () => Navigator.pop(context, false),
                  ),
                ],
              ),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  tr(context, 'confirm_vehicle_license_plate'),
                  style: TextStyles.ts30w700c44322D,
                  textAlign: TextAlign.center,
                ),
                Gap(12.h),
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: ColorConstants.color181818,
                      width: 1.5.w,
                    ),
                    color: Colors.white,
                  ),
                  margin: EdgeInsets.symmetric(horizontal: 60.w),
                  child: Row(
                    children: [
                      Gap(12.w),
                      Column(
                        children: [
                          Text(
                            englishNumbers,
                            style: TextStyles.ts16w600c181818,
                          ),
                          Text(
                            arabicNumbers,
                            style: TextStyles.ts16w600c181818,
                          ),
                        ],
                      ),
                      const Spacer(),
                      Image.asset(
                        'ksa-logo'.asImagePng(),
                        height: 46.h,
                      ),
                      const Spacer(),
                      Column(
                        children: [
                          Text(
                            englishLetters,
                            style: TextStyles.ts16w600c181818,
                          ),
                          Text(
                            arabicLetters,
                            style: TextStyles.ts16w600c181818,
                          ),
                        ],
                      ),
                      Gap(12.w),
                      SizedBox(
                        height: 48.h,
                        child: VerticalDivider(
                          width: 1.w,
                          color: ColorConstants.color181818,
                        ),
                      ),
                      Image.asset(
                        'saudi-plate-logo'.asImagePng(),
                        height: 48.h,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
