import 'dart:convert';
import 'dart:developer';

import 'package:albalad_operator_app/features/profile/models/profile_model.dart';
import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/helper/shared_preference_helper.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class ProfileServices {
  final Dio dio = DioClient().dio;
  Future<Response> fetchProfile() async {
    final response = await dio.get(
      ApiConstants.profile,
      options: Options(headers: await ApiConstants.authHeaders()),
    );
    return response;
  }
}

class ProfileRepository {
  static const String _profileKey = 'user_profile';

  Future<ProfileModel?> getProfileFromCache() async {
    final profileString = SharedPreferenceHelper.instance.getData(_profileKey);
    if (profileString != null) {
      return ProfileModel.fromJson(jsonDecode(profileString));
    }
    return null;
  }

  Future<void> saveProfileToCache(ProfileModel profile) async {
    SharedPreferenceHelper.instance
        .saveData(_profileKey, jsonEncode(profile.toJson()));
  }

  Future<ProfileModel?> fetchProfile() async {
    try {
      final response = await ProfileServices().fetchProfile();
      if (kDebugMode) {
        log(jsonEncode(response.data), name: 'Profile Response');
      }
      if (response.statusCode == 200) {
        return ProfileModel.fromJson(response.data);
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
