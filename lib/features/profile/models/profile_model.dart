class ProfileModel {
  String? uid;
  String? name;
  String? email;
  String? phone;
  String? language;
  String? designation;
  List<String>? permission;
  List<AssignedLocation>? assignedLocation;
  AssignedShiftTime? assignedShiftTime;
  String? profilePictureUrl;

  ProfileModel({
    this.uid,
    this.name,
    this.email,
    this.phone,
    this.language,
    this.designation,
    this.permission,
    this.assignedLocation,
    this.assignedShiftTime,
    this.profilePictureUrl,
  });

  ProfileModel.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    name = json['name'];
    email = json['email'];
    phone = json['phone'];
    language = json['language'];
    designation = json['designation'];
    permission = json['permission'].cast<String>();
    if (json['assigned_location'] != null) {
      assignedLocation = <AssignedLocation>[];
      json['assigned_location'].forEach((v) {
        assignedLocation!.add(AssignedLocation.fromJson(v));
      });
    }
    assignedShiftTime = json['assigned_shift_time'] != null
        ? AssignedShiftTime.fromJson(json['assigned_shift_time'])
        : null;
    profilePictureUrl = json['profile_picture_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['name'] = name;
    data['email'] = email;
    data['phone'] = phone;
    data['language'] = language;
    data['designation'] = designation;
    data['permission'] = permission;
    if (assignedLocation != null) {
      data['assigned_location'] =
          assignedLocation!.map((v) => v.toJson()).toList();
    }
    if (assignedShiftTime != null) {
      data['assigned_shift_time'] = assignedShiftTime!.toJson();
    }
    data['profile_picture_url'] = profilePictureUrl;
    return data;
  }
}

class AssignedLocation {
  String? uid;
  String? locationUid;
  String? name;
  String? address;

  AssignedLocation({this.uid, this.locationUid, this.name, this.address});

  AssignedLocation.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    locationUid = json['location_uid'];
    name = json['name'];
    address = json['address'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['location_uid'] = locationUid;
    data['name'] = name;
    data['address'] = address;
    return data;
  }
}

class AssignedShiftTime {
  String? uid;
  String? name;
  String? startTime;
  String? endTime;

  AssignedShiftTime({this.uid, this.name, this.startTime, this.endTime});

  AssignedShiftTime.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    name = json['name'];
    startTime = json['start_time'];
    endTime = json['end_time'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['name'] = name;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    return data;
  }
}
