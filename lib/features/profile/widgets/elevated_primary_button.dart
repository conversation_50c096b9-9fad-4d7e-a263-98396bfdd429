import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ElevatedPrimaryButton extends StatelessWidget {
  final String title;
  final void Function()? onPressed;
  const ElevatedPrimaryButton(
      {required this.title, required this.onPressed, super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: ColorConstants.primaryColor,
        textStyle: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.w500,
        ),
        foregroundColor: ColorConstants.backgroundColor,
      ),
      child: Text(title),
    );
  }
}
