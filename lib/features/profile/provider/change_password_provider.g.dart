// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'change_password_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$changePasswordNotifierHash() =>
    r'b37169c649f467deb60cbcd17c70512ca90d96ee';

/// See also [ChangePasswordNotifier].
@ProviderFor(ChangePasswordNotifier)
final changePasswordNotifierProvider = AutoDisposeNotifierProvider<
    ChangePasswordNotifier, ChangePasswordState>.internal(
  ChangePasswordNotifier.new,
  name: r'changePasswordNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$changePasswordNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ChangePasswordNotifier = AutoDisposeNotifier<ChangePasswordState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
