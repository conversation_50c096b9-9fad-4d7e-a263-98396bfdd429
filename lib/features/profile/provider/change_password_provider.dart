import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:encrypt/encrypt.dart' as aes;

part 'change_password_provider.g.dart';

abstract class ChangePasswordState {}

class ChangePasswordInitial extends ChangePasswordState {}

class ChangePasswordLoading extends ChangePasswordState {}

class ChangePasswordSuccess extends ChangePasswordState {}

class ChangePasswordError extends ChangePasswordState {
  final String? currentPasswordError;
  final String? newPasswordError;
  final String? confirmPasswordError;
  final String? message;

  ChangePasswordError({
    this.newPasswordError,
    this.currentPasswordError,
    this.confirmPasswordError,
    this.message,
  });
}

@riverpod
class ChangePasswordNotifier extends _$ChangePasswordNotifier {
  @override
  ChangePasswordState build() {
    return ChangePasswordInitial();
  }

  final Dio _dio = DioClient().dio;

  Future<String?> fetchEncryptionKey() async {
    try {
      final response = await _dio.get(
        ApiConstants.encryptionSecretKey,
      );
      if (response.statusCode == 200) {
        final json = response.data;
        if (json['result'] == 'success') {
          return json['secret_key'];
        }
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionError ||
          e.type == DioExceptionType.connectionTimeout) {
        throw appLocalization.translate('networkError');
      }
    }
    return null;
  }

  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    state = ChangePasswordLoading();
    try {
      // Fetch the encryption key asynchronously, which will be used to encrypt the data.
      final secretKey = await fetchEncryptionKey();
      final normalizedKey = normalizeKey(secretKey.toString());

      // Convert the fetched key to a format suitable for AES encryption.
      // final key = aes.Key.fromUtf8(secretKey.toString());
      final key = aes.Key.fromUtf8(normalizedKey);

      // Generate an Initialization Vector (IV) with a fixed length of 16 bytes.
      // Note: AES in ECB mode does not use an IV; this is just a placeholder.
      final iv = aes.IV.fromSecureRandom(16);

      // Create an instance of the AES encrypter with the specified key and encryption mode (ECB in this case).
      final encrypter = aes.Encrypter(aes.AES(key, mode: aes.AESMode.ecb));

      // Encrypt the password using the encrypter and the provided IV.
      // Note: ECB mode does not require an IV, so it is effectively ignored here.
      final encryptedCurrentPassword =
          encrypter.encrypt(currentPassword, iv: iv);
      final encryptedNewPassword = encrypter.encrypt(newPassword, iv: iv);
      final encryptedConfirmPassword =
          encrypter.encrypt(confirmPassword, iv: iv);

      final response = await _dio.post(
        ApiConstants.changePassword,
        options: Options(
          headers: await ApiConstants.authHeaders(),
        ),
        data: {
          'current_password': encryptedCurrentPassword.base64,
          'new_password': encryptedNewPassword.base64,
          'confirm_password': encryptedConfirmPassword.base64,
          "secret_key": normalizedKey,
          "encryption": "True",
        },
      );
      if (response.statusCode == 200) {
        final json = response.data;
        if (json['status'] == 'success') {
          state = ChangePasswordSuccess();
          return;
        }
      }
      if (response.statusCode == 400) {
        final json = response.data;
        Map<String, dynamic> errors = json['errors'] ?? {};
        if (errors.containsKey('current_password')) {
          state = ChangePasswordError(
              currentPasswordError: errors['current_password']);
          return;
        }
      }
      state = ChangePasswordError(
          message: appLocalization.translate('somethingWentWrong'));
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = ChangePasswordError(
          message: appLocalization.translate('networkError'),
        );
        return;
      }
      state = ChangePasswordError(message: e.toString());
    }
  }

  String normalizeKey(String key) {
    const int keyLength = 16; // 16 for AES-128, 24 for AES-192, 32 for AES-256
    if (key.length > keyLength) {
      return key.substring(0, keyLength);
    } else if (key.length < keyLength) {
      return key.padRight(keyLength, '0');
    }
    return key;
  }

  resetState() {
    state = ChangePasswordInitial();
  }
}
