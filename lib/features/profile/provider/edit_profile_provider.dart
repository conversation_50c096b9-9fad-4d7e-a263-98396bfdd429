import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'edit_profile_provider.g.dart';

abstract class EditProfileState {}

class EditProfileInitial extends EditProfileState {}

class EditProfileLoading extends EditProfileState {}

class EditProfileSuccess extends EditProfileState {}

class EditProfilePicLoading extends EditProfileState {}

class EditProfilePicSuccess extends EditProfileState {}

class EditProfileError extends EditProfileState {
  final String message;

  EditProfileError(this.message);
}

@riverpod
class EditProfileNotifier extends _$EditProfileNotifier {
  @override
  EditProfileState build() {
    return EditProfileInitial();
  }

  uploadProfilePicture({required ImageSource source}) async {
    final picker = ImagePicker();
    final pickedImage = await picker.pickImage(source: source);
    if (pickedImage != null) {
      final croppedImage = await cropImage(image: pickedImage);
      if (croppedImage != null) {
        submitProfilePicture(XFile(croppedImage.path));
      }
    }
  }

  final Dio _dio = DioClient().dio;
  submitProfilePicture(XFile xFile) async {
    state = EditProfilePicLoading();
    try {
      FormData formData = FormData.fromMap({
        'profile_picture': await MultipartFile.fromFile(
          xFile.path,
          filename: xFile.path.split('/').last,
        ),
      });

      final response = await _dio.post(
        ApiConstants.profileUpdate,
        options: Options(
          headers: await ApiConstants.authFormDataHeaders(),
        ),
        data: formData,
      );

      if (response.statusCode == 200) {
        final json = response.data;
        if (json['status'] == 'success') {
          state = EditProfilePicSuccess();
          return;
        }
      }
      state = EditProfileError(appLocalization.translate('somethingWentWrong'));
    } catch (e, stackTrace) {
      debugPrint('Error in submitProfilePicture: $e');
      debugPrint('StackTrace: $stackTrace');
      state = EditProfileError(e.toString());
    }
  }

  Future<CroppedFile?> cropImage({required XFile image}) async {
    return ImageCropper().cropImage(
      sourcePath: image.path,
      uiSettings: [
        AndroidUiSettings(
          statusBarColor: Colors.black,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: false,
        ),
        IOSUiSettings(),
      ],
    );
  }

  updateProfile({required String fullName}) async {
    state = EditProfileLoading();
    try {
      final response = await _dio.post(
        ApiConstants.profileUpdate,
        options: Options(
          headers: await ApiConstants.authHeaders(),
        ),
        data: {
          'name': fullName,
        },
      );
      if (response.statusCode == 200) {
        final json = response.data;
        if (json['status'] == 'success') {
          state = EditProfileSuccess();
          return;
        }
      } else if (response.statusCode == 500) {
        state =
            EditProfileError(appLocalization.translate('somethingWentWrong'));
        return;
      }
      state = EditProfileError(appLocalization.translate('somethingWentWrong'));
    } catch (e, stackTrace) {
      debugPrint('Error in updateProfile: $e');
      debugPrint('StackTrace: $stackTrace');
      state = EditProfileError(e.toString());
    }
  }
}
