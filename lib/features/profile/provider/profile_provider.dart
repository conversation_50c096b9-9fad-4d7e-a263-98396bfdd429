import 'package:albalad_operator_app/features/profile/models/profile_model.dart';
import 'package:albalad_operator_app/features/profile/services/profile_services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ProfileNotifier extends StateNotifier<AsyncValue<ProfileModel?>> {
  final ProfileRepository repository;

  ProfileNotifier(this.repository) : super(const AsyncValue.loading()) {
    _loadProfile();
  }

  /// Manually refresh the profile data
  Future<void> refresh() async {
    if (mounted) {
      state = const AsyncValue.loading();
      await _loadProfile();
    }
  }

  Future<void> _loadProfile() async {
    // Load from cache first
    final cachedProfile = await repository.getProfileFromCache();
    if (cachedProfile != null && mounted) {
      state = AsyncValue.data(cachedProfile);
    }

    // Fetch from network
    try {
      final networkProfile = await repository.fetchProfile();
      if (networkProfile != null && mounted) {
        state = AsyncValue.data(networkProfile);
        await repository.saveProfileToCache(networkProfile);
      }
    } catch (error) {
      if (mounted) {
        state = AsyncValue.error(error, StackTrace.current);
      }
    }
  }
}

final profileProvider = StateNotifierProvider.autoDispose<ProfileNotifier,
    AsyncValue<ProfileModel?>>(
  (ref) => ProfileNotifier(ProfileRepository()),
);
