import 'package:albalad_operator_app/features/profile/models/profile_model.dart';
import 'package:albalad_operator_app/features/profile/services/profile_services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ProfileNotifier extends StateNotifier<AsyncValue<ProfileModel?>> {
  final ProfileRepository repository;

  ProfileNotifier(this.repository) : super(const AsyncValue.loading()) {
    _loadProfile();
  }

  Future<void> _loadProfile() async {
    // Load from cache first
    final cachedProfile = await repository.getProfileFromCache();
    if (cachedProfile != null) {
      state = AsyncValue.data(cachedProfile);
    }

    // Fetch from network
    try {
      final networkProfile = await repository.fetchProfile();
      if (networkProfile != null) {
        state = AsyncValue.data(networkProfile);
        await repository.saveProfileToCache(networkProfile);
      }
    } catch (error) {
      state = AsyncValue.error(error, StackTrace.current);
    }
  }
}

final profileProvider =
    StateNotifierProvider<ProfileNotifier, AsyncValue<ProfileModel?>>(
  (ref) => ProfileNotifier(ProfileRepository()),
);
