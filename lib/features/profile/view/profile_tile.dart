import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class ProfileTile extends StatelessWidget {
  final String iconSVG;
  final String title;
  final Widget? trailing;
  final void Function()? onTap;
  const ProfileTile({
    required this.iconSVG,
    required this.title,
    this.trailing,
    this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              offset: Offset(0, 0),
              color: Colors.black.withValues(alpha: 0.04),
              blurRadius: 20,
              spreadRadius: 0,
            ),
          ],
        ),
        height: 60.h,
        padding: EdgeInsets.only(
          left: 16.w,
          right: 14.w,
        ),
        child: Row(
          children: [
            SvgPicture.asset(
              iconSVG.asIconSvg(),
              height: 24.h,
              width: 24.w,
            ),
            Gap(15.w),
            Expanded(
              child: Text(
                title,
                style: TextStyles.ts15w400c181818,
              ),
            ),
            if (trailing != null) trailing!,
          ],
        ),
      ),
    );
  }
}
