import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/elevated_secondary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';

class LogoutConfirmation extends StatelessWidget {
  const LogoutConfirmation({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 50.h, 16.w, 30.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            'info-circle'.asIconSvg(),
            width: 94.w,
            height: 94.h,
          ),
          Gap(10.h),
          Text(
            tr(context, 'logout'),
            textAlign: TextAlign.center,
            style: TextStyles.ts30w700c44322D,
          ),
          Text(
            tr(context, 'logout_confirmation'),
            style: TextStyles.ts14w500c959595,
            textAlign: TextAlign.center,
          ),
          Gap(50.h),
          Row(
            spacing: 10.w,
            children: [
              Expanded(
                child: ElevatedSecondaryButton(
                  onPressed: () => Navigator.pop(context),
                  title: tr(context, 'cancel'),
                ),
              ),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context, true),
                  child: Text(tr(context, 'logout')),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
