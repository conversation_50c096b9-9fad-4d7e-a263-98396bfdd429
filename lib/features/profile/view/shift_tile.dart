import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class ShiftTile extends StatelessWidget {
  final Widget? icon;
  final String? title;
  final String? value;
  final TextDirection? textDirection;
  const ShiftTile({
    this.icon,
    this.title,
    this.value,
    this.textDirection,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.fromLTRB(10.w, 10.h, 10.w, 15.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              offset: Offset(0, 0),
              color: Colors.black.withValues(alpha: 0.02),
              blurRadius: 5.7,
              spreadRadius: 0,
            ),
          ],
          color: ColorConstants.colorF8F8F8,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 42.h,
              width: 42.w,
              decoration: BoxDecoration(
                color: ColorConstants.color94684E.withValues(alpha: 0.09),
                shape: BoxShape.circle,
              ),
              alignment: Alignment.center,
              child: icon,
            ),
            Gap(8.h),
            if (title != null)
              Text(
                title!,
                style: TextStyles.ts12w400c4C4C4C,
              ),
            if (value != null)
              Text(
                value!,
                style: TextStyles.ts14w700c94684E,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
                textDirection: textDirection,
              ),
          ],
        ),
      ),
    );
  }
}
