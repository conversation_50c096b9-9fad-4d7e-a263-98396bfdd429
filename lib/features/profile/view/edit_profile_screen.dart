import 'dart:io';

import 'package:albalad_operator_app/features/master/view/master_screen.dart';
import 'package:albalad_operator_app/features/profile/provider/edit_profile_provider.dart';
import 'package:albalad_operator_app/features/profile/provider/profile_provider.dart';
import 'package:albalad_operator_app/features/profile/view/change_password_screen.dart';
import 'package:albalad_operator_app/features/profile/view/profile_image_view.dart';
import 'package:albalad_operator_app/features/success_screen.dart';
import 'package:albalad_operator_app/providers/global_providers.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/custom_text_form_field.dart';
import 'package:albalad_operator_app/shared/widgets/image_source_sheet.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';

class EditProfileScreen extends HookConsumerWidget {
  static const String route = '/edit-profile';

  const EditProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final fullNameController = useTextEditingController();
    final passwordController = useTextEditingController();

    final nameFilled = useState(false);

    ref.listen(
      editProfileNotifierProvider,
      (previous, next) {
        if (next is EditProfileSuccess) {
          ref.invalidate(profileProvider);
          final argument = SuccessScreen(
            title: tr(context, 'update_successful'),
            message: tr(context, 'profile_update_success_message'),
            onPressed: (context) {
              Navigator.popUntil(
                context,
                ModalRoute.withName(MasterScreen.route),
              );
            },
          );
          Navigator.pushNamed(
            context,
            SuccessScreen.route,
            arguments: argument,
          );
        }
      },
    );

    final formNotifier = ref.read(editProfileNotifierProvider.notifier);
    final formState = ref.watch(editProfileNotifierProvider);
    final profileAsync = ref.watch(profileProvider);

    final isRTL = Directionality.of(context) == TextDirection.rtl;
    return Scaffold(
      appBar: AppBar(
        title: Text('Edit Profile'),
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: Transform.flip(
            flipX: isRTL,
            child: SvgPicture.asset('arrow-left-profile'.asIconSvg()),
          ),
          color: ColorConstants.color94684E,
        ),
        titleTextStyle: TextStyle(
          fontSize: 18.sp,
          color: ColorConstants.primaryColor,
          fontWeight: FontWeight.w500,
        ),
        backgroundColor: ColorConstants.colorE1DDD2,
        actions: [
          IconButton(
            onPressed: () {
              ref.read(masterProvider.notifier).state = 0;
              Navigator.popUntil(
                context,
                ModalRoute.withName(MasterScreen.route),
              );
            },
            icon: SvgPicture.asset('home-button-icon'.asIconSvg()),
          ),
        ],
      ),
      body: ListView(
        physics: const ClampingScrollPhysics(),
        padding: EdgeInsets.zero,
        children: [
          Gap(20.h),
          profileAsync.when(
            data: (profile) {
              if (nameFilled.value == false) {
                fullNameController.text = profile?.name ?? '';
                nameFilled.value = true;
              }
              return Column(
                children: [
                  ProfileImageAvatar(
                    profileImage: profile?.profilePictureUrl ?? 'https://',
                  ),
                  Gap(15.h),
                  Text(
                    profile?.name ?? '',
                    textAlign: TextAlign.center,
                    style: TextStyles.ts22w600c44322D,
                  ),
                ],
              );
            },
            error: (err, stack) => const SizedBox(),
            loading: () => _loading(),
          ),
          Padding(
            padding: EdgeInsets.fromLTRB(16.w, 37.h, 16.w, 0),
            child: SizedBox(
              child: Column(
                children: [
                  Form(
                    key: formKey,
                    child: CustomTextFormField(
                      controller: fullNameController,
                      labelText: tr(context, 'full_name_star'),
                      keyboardType: TextInputType.emailAddress,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'[a-zA-Z0-9\s]'),
                        )
                      ],
                      validator: (name) {
                        if (name == null || name.trim().isEmpty) {
                          return tr(context, 'please_enter_full_name');
                        }
                        return null;
                      },
                    ),
                  ),
                  Gap(24.h),
                  CustomTextFormField(
                    controller: passwordController,
                    labelText: tr(context, 'password_star'),
                    obscureText: true,
                    hintText: '************',
                    readOnly: true,
                    onTap: () => Navigator.pushNamed(
                        context, ChangePasswordScreen.route),
                    suffixIcon: Padding(
                      padding: EdgeInsets.fromLTRB(
                        isRTL ? 14.w : 0,
                        14.h,
                        isRTL ? 0 : 14.w,
                        14.h,
                      ),
                      child: Text(
                        tr(context, 'change_password'),
                        style: TextStyles.ts14w600CECECE,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        bottom: true,
        child: Padding(
          padding: EdgeInsets.fromLTRB(
            16.w,
            0,
            16.w,
            Platform.isAndroid ? 30.h : 0,
          ),
          child: formState is EditProfileLoading
              ? SizedBox(
                  height: kBottomNavigationBarHeight,
                  child: const Center(child: CustomGradientSpinner()),
                )
              : ElevatedButton(
                  onPressed: () {
                    if (formKey.currentState?.validate() == true) {
                      formNotifier.updateProfile(
                          fullName: fullNameController.text);
                    }
                  },
                  child: Text(tr(context, 'save_changes')),
                ),
        ),
      ),
    );
  }

  _loading() {
    return Column(
      children: [
        ProfileImageView(
          profileImage: 'https://',
        ),
        Gap(15.h),
        Text(
          'Saif Al Balushi',
          textAlign: TextAlign.center,
          style: TextStyles.ts22w600c44322D,
        ),
      ],
    );
  }
}

class ProfileImageAvatar extends ConsumerWidget {
  final String profileImage;
  const ProfileImageAvatar({required this.profileImage, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formNotifier = ref.read(editProfileNotifierProvider.notifier);
    final formState = ref.watch(editProfileNotifierProvider);

    ref.listen(editProfileNotifierProvider, (previous, next) {
      if (next is EditProfilePicSuccess) {
        ref.invalidate(profileProvider);
      }
    });

    return Center(
      child: SizedBox(
        height: 110.h,
        width: 110.w,
        child: Stack(
          children: [
            Container(
              height: 110.h,
              width: 110.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: ColorConstants.colorCCB5A7,
                border: Border.all(
                  color: ColorConstants.colorCCB5A7,
                  width: 1.38.w,
                ),
              ),
              child: ClipOval(
                child: CachedNetworkImage(
                  imageUrl: profileImage,
                  fit: BoxFit.cover,
                  errorWidget: (context, url, error) {
                    return InkWell(
                      onTap: () => _uploadProfilePicture(context, formNotifier),
                      child: Image.asset('assets/icons/profile_picture.png'),
                    );
                  },
                ),
              ),
            ),
            if (formState is EditProfilePicLoading)
              Container(
                height: 110.h,
                width: 110.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: ColorConstants.colorCCB5A7.withValues(alpha: 0.5),
                  border: Border.all(
                    color: ColorConstants.colorCCB5A7,
                    width: 1.38.w,
                  ),
                ),
                child: const Center(
                  child: CustomGradientSpinner(),
                ),
              ),
            Positioned(
              bottom: 8.h,
              right: 3.w,
              child: InkWell(
                onTap: () => _uploadProfilePicture(context, formNotifier),
                child: Container(
                  decoration: BoxDecoration(
                    color: ColorConstants.colorCCB5A7,
                    shape: BoxShape.circle,
                  ),
                  width: 26.w,
                  height: 26.h,
                  alignment: Alignment.center,
                  child: SvgPicture.asset(
                    'camera'.asIconSvg(),
                    width: 16.w,
                    height: 16.h,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  _uploadProfilePicture(
      BuildContext context, EditProfileNotifier formNotifier) async {
    ImageSource? source = await showModalBottomSheet(
      context: context,
      builder: (context) => const ImageSourceSheet(),
    );
    if (source != null) {
      await formNotifier.uploadProfilePicture(source: source);
    }
  }
}
