import 'package:albalad_operator_app/features/profile/provider/edit_profile_provider.dart';
import 'package:albalad_operator_app/features/profile/provider/profile_provider.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/image_source_sheet.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';

class ProfileImageView extends ConsumerWidget {
  final String profileImage;
  const ProfileImageView({required this.profileImage, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formNotifier = ref.read(editProfileNotifierProvider.notifier);
    final formState = ref.watch(editProfileNotifierProvider);

    ref.listen(editProfileNotifierProvider, (previous, next) {
      if (next is EditProfilePicSuccess) {
        ref.invalidate(profileProvider);
      }
    });

    return SizedBox(
      height: 130.h,
      child: Stack(
        children: [
          Container(
            height: 75.h,
            color: ColorConstants.colorE1DDD2,
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Stack(
              children: [
                Container(
                  height: 110.h,
                  width: 110.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: ColorConstants.colorCCB5A7,
                    border: Border.all(
                      color: ColorConstants.colorCCB5A7,
                      width: 1.38.w,
                    ),
                  ),
                  child: ClipOval(
                    child:
                        _buildProfileImage(profileImage, context, formNotifier),
                  ),
                ),
                if (formState is EditProfilePicLoading)
                  Container(
                    height: 110.h,
                    width: 110.w,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: ColorConstants.colorCCB5A7.withValues(alpha: 0.5),
                      border: Border.all(
                        color: ColorConstants.colorCCB5A7,
                        width: 1.38.w,
                      ),
                    ),
                    child: const Center(
                      child: CustomGradientSpinner(),
                    ),
                  ),
                Positioned(
                  bottom: 8.h,
                  right: 3.w,
                  child: InkWell(
                    onTap: () => _uploadProfilePicture(context, formNotifier),
                    child: Container(
                      decoration: BoxDecoration(
                        color: ColorConstants.colorCCB5A7,
                        shape: BoxShape.circle,
                      ),
                      width: 26.w,
                      height: 26.h,
                      alignment: Alignment.center,
                      child: SvgPicture.asset(
                        'camera'.asIconSvg(),
                        width: 16.w,
                        height: 16.h,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  _uploadProfilePicture(
      BuildContext context, EditProfileNotifier formNotifier) async {
    ImageSource? source = await showModalBottomSheet(
      context: context,
      builder: (context) => const ImageSourceSheet(),
    );
    if (source != null) {
      await formNotifier.uploadProfilePicture(source: source);
    }
  }

  /// Helper method to build profile image with proper URL validation
  Widget _buildProfileImage(String profileImage, BuildContext context,
      EditProfileNotifier formNotifier) {
    // Check if URL is valid
    if (profileImage.isEmpty ||
        profileImage == 'https://' ||
        Uri.tryParse(profileImage)?.hasAbsolutePath != true) {
      // Return default image if URL is invalid
      return InkWell(
        onTap: () => _uploadProfilePicture(context, formNotifier),
        child: Image.asset(
          'assets/icons/profile_picture.png',
        ),
      );
    }

    // Return CachedNetworkImage for valid URLs
    return CachedNetworkImage(
      imageUrl: profileImage,
      fit: BoxFit.cover,
      errorWidget: (context, url, error) {
        return InkWell(
          onTap: () => _uploadProfilePicture(context, formNotifier),
          child: Image.asset(
            'assets/icons/profile_picture.png',
          ),
        );
      },
    );
  }
}
