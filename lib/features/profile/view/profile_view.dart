import 'package:albalad_operator_app/features/profile/provider/profile_provider.dart';
import 'package:albalad_operator_app/features/profile/view/profile_image_view.dart';
import 'package:albalad_operator_app/features/profile/view/shift_tile.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ProfileView extends ConsumerWidget {
  const ProfileView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final profileAsync = ref.watch(profileProvider);
    return profileAsync.when(
      data: (profile) {
        return Column(
          children: [
            ProfileImageView(
              profileImage: profile?.profilePictureUrl ?? 'https://',
            ),
            Gap(15.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Text(
                profile?.name ?? '',
                textAlign: TextAlign.center,
                style: TextStyles.ts22w600c44322D,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Text(
              profile?.designation ?? '',
              textAlign: TextAlign.center,
              style: TextStyles.ts14w400c959595,
            ),
            Gap(24.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: IntrinsicHeight(
                child: Row(
                  spacing: 10.w,
                  children: [
                    ShiftTile(
                      icon: SvgPicture.asset(
                        'shift-location'.asIconSvg(),
                        height: 18.h,
                        width: 18.w,
                      ),
                      title: tr(context, 'location'),
                      value: profile?.assignedLocation?.firstOrNull?.name,
                    ),
                    ShiftTile(
                      icon: SvgPicture.asset(
                        'sun'.asIconSvg(),
                        height: 18.h,
                        width: 18.w,
                      ),
                      title: profile?.assignedShiftTime?.name,
                      value:
                          '${getShiftTime(profile?.assignedShiftTime?.startTime)}-${getShiftTime(profile?.assignedShiftTime?.endTime)}',
                      textDirection: TextDirection.ltr,
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
      error: (err, stack) => const SizedBox(),
      loading: () => _loading(),
    );
  }

  String getShiftTime(String? shiftTime) {
    if (shiftTime == null) return '';
    return formatShiftTime(shiftTime);
  }

  String formatShiftTime(String startTime) {
    List<String> parts = startTime.split(" ");
    String meridian = parts[1];
    List<String> timeComponents = parts[0].split(":");
    String hour = int.parse(timeComponents[0]).toString();
    String minutes = timeComponents[1];
    return minutes == "00" ? "$hour $meridian" : "$hour:$minutes $meridian";
  }

  _loading() {
    return Skeletonizer(
      enabled: true,
      child: Column(
        children: [
          ProfileImageView(profileImage: 'https://'),
          Gap(15.h),
          Text(
            'Saif Al Balushi',
            textAlign: TextAlign.center,
            style: TextStyles.ts22w600c44322D,
          ),
          Text(
            'Parking Operator',
            textAlign: TextAlign.center,
            style: TextStyles.ts14w400c959595,
          ),
          Gap(24.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              spacing: 10.w,
              children: [
                ShiftTile(
                  icon: SvgPicture.asset(
                    'shift-location'.asIconSvg(),
                    height: 18.h,
                    width: 18.w,
                  ),
                  title: "Location",
                  value: 'Al Zahab',
                ),
                ShiftTile(
                  icon: SvgPicture.asset(
                    'sun'.asIconSvg(),
                    height: 18.h,
                    width: 18.w,
                  ),
                  title: 'Morning Shift',
                  value: '10AM-12PM',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
