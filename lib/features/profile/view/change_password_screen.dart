import 'dart:io';

import 'package:albalad_operator_app/features/profile/provider/change_password_provider.dart';
import 'package:albalad_operator_app/features/profile/view/edit_profile_screen.dart';
import 'package:albalad_operator_app/features/success_screen.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/helper/dialog_helper.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/custom_text_form_field.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:albalad_operator_app/shared/widgets/password_eye_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ChangePasswordScreen extends HookConsumerWidget {
  static const String route = '/change-password';
  const ChangePasswordScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final currentPasswordController = useTextEditingController();
    final newPasswordController = useTextEditingController();
    final confirmPasswordController = useTextEditingController();

    final showCurrentPassword = useState(false);
    final showNewPassword = useState(false);
    final showConfirmPassword = useState(false);

    ref.listen(
      changePasswordNotifierProvider,
      (previous, next) {
        if (next is ChangePasswordSuccess) {
          final argument = SuccessScreen(
            title: tr(context, 'password_updated'),
            message: tr(context, 'password_updated_message'),
            onPressed: (context) {
              Navigator.popUntil(
                context,
                ModalRoute.withName(EditProfileScreen.route),
              );
            },
          );
          Navigator.pushNamed(context, SuccessScreen.route,
              arguments: argument);
        }
        if (next is ChangePasswordError && next.message != null) {
          DialogHelper.showErrorDialog(
            context: context,
            message: next.message!,
          );
        }
      },
    );

    final formNotifier = ref.read(changePasswordNotifierProvider.notifier);
    final formState = ref.watch(changePasswordNotifierProvider);

    String? currentPasswordError;
    String? newPasswordError;
    String? confirmPasswordError;

    if (formState is ChangePasswordError) {
      currentPasswordError = formState.currentPasswordError;
      newPasswordError = formState.newPasswordError;
      confirmPasswordError = formState.confirmPasswordError;
    }

    return Scaffold(
      appBar: InnerAppBar(
        title: Text(tr(context, 'change_password')),
      ),
      body: Form(
        key: formKey,
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
          children: [
            CustomTextFormField(
              controller: currentPasswordController,
              labelText: tr(context, 'current_password_star'),
              hintText: tr(context, 'enter_your_current_password'),
              obscureText: !showCurrentPassword.value,
              errorText: currentPasswordError,
              suffixIcon: PasswordEyeButton(
                showPassword: showCurrentPassword.value,
                onTap: () {
                  showCurrentPassword.value = !showCurrentPassword.value;
                },
              ),
              validator: (currentPassword) {
                if (currentPassword == null || currentPassword.isEmpty) {
                  return tr(context, 'please_enter_your_current_password');
                }
                return null;
              },
            ),
            Gap(24.h),
            CustomTextFormField(
              controller: newPasswordController,
              labelText: tr(context, 'new_password_star'),
              hintText: tr(context, 'enter_your_new_password'),
              obscureText: !showNewPassword.value,
              errorText: newPasswordError,
              suffixIcon: PasswordEyeButton(
                showPassword: showNewPassword.value,
                onTap: () {
                  showNewPassword.value = !showNewPassword.value;
                },
              ),
              validator: (newPassword) {
                if (newPassword == null || newPassword.isEmpty) {
                  return tr(context, 'please_enter_your_new_password');
                }
                if (newPassword == currentPasswordController.text) {
                  return tr(context, 'new_password_must_be_different');
                }
                return validatePassword(newPassword, context);
              },
            ),
            Gap(24.h),
            CustomTextFormField(
              controller: confirmPasswordController,
              labelText: tr(context, 'confirm_new_password_star'),
              hintText: tr(context, 'confirm_your_new_password'),
              obscureText: !showConfirmPassword.value,
              errorText: confirmPasswordError,
              suffixIcon: PasswordEyeButton(
                showPassword: showConfirmPassword.value,
                onTap: () {
                  showConfirmPassword.value = !showConfirmPassword.value;
                },
              ),
              validator: (confirmPassword) {
                if (confirmPassword == null || confirmPassword.isEmpty) {
                  return tr(context, 'please_confirm_your_new_password');
                }
                if (confirmPassword != newPasswordController.text) {
                  return tr(context, 'passwordsDontMatch');
                }
                return null;
              },
            ),
          ],
        ),
      ),
      bottomNavigationBar: SafeArea(
        bottom: true,
        child: Padding(
          padding: EdgeInsets.fromLTRB(
            16.w,
            0,
            16.w,
            Platform.isAndroid ? 30.h : 0,
          ),
          child: formState is ChangePasswordLoading
              ? SizedBox(
                  height: kBottomNavigationBarHeight,
                  child: const Center(child: CustomGradientSpinner()),
                )
              : ElevatedButton(
                  onPressed: () {
                    formNotifier.resetState();
                    if (formKey.currentState?.validate() == true) {
                      formNotifier.changePassword(
                        currentPassword: currentPasswordController.text,
                        newPassword: newPasswordController.text,
                        confirmPassword: confirmPasswordController.text,
                      );
                    }
                  },
                  child: Text(tr(context, 'save')),
                ),
        ),
      ),
    );
  }

  String? validatePassword(String password, BuildContext context) {
    if (password.length < 8) {
      return tr(context, 'passwordMustbeAtLeast8CharactersLong');
    }
    if (!RegExp(r'[A-Z]').hasMatch(password)) {
      return tr(context, 'passwordMustHaveAtLeastOneUppercase');
    }
    if (!RegExp(r'[a-z]').hasMatch(password)) {
      return tr(context, 'passwordMustHaveAtLeastOneLowercase');
    }
    if (!RegExp(r'\d').hasMatch(password)) {
      return tr(context, 'passwordMustHaveAtLeastOneNumber');
    }
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) {
      return tr(context, 'passwordMustHaveAtLeastOneSpecialCharacter');
    }
    return null;
  }
}
