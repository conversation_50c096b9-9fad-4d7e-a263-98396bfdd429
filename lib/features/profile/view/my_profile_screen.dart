import 'package:albalad_operator_app/features/authentication/services/auth_services.dart';
import 'package:albalad_operator_app/features/authentication/view/sign_in_screen.dart';
import 'package:albalad_operator_app/features/profile/provider/profile_provider.dart';
import 'package:albalad_operator_app/features/profile/view/edit_profile_screen.dart';
import 'package:albalad_operator_app/features/profile/view/logout_confirmation.dart';
import 'package:albalad_operator_app/features/profile/view/profile_tile.dart';
import 'package:albalad_operator_app/features/profile/view/profile_view.dart';
import 'package:albalad_operator_app/features/support_center/view/contact_us_screen.dart';
import 'package:albalad_operator_app/features/support_center/view/faq_screen.dart';
import 'package:albalad_operator_app/features/support_center/view/privacy_policy_screen.dart';
import 'package:albalad_operator_app/features/support_center/view/terms_and_conditions_screen.dart';
import 'package:albalad_operator_app/providers/global_providers.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/helper/secure_storage_helper.dart';
import 'package:albalad_operator_app/shared/helper/shared_preference_helper.dart';
import 'package:albalad_operator_app/shared/widgets/language_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class MyProfileScreen extends ConsumerWidget {
  static const String route = '/my-profile';
  const MyProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen(masterProvider, (previous, next) {
      if (next == 3) {
        ref.invalidate(profileProvider);
      }
    });
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    return Scaffold(
      appBar: AppBar(
        title: Text(tr(context, 'my_profile')),
        centerTitle: true,
        leading: IconButton(
          onPressed: () {
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            } else {
              ref.read(masterProvider.notifier).state = 0;
            }
          },
          icon: Transform.flip(
            flipX: isRTL,
            child: SvgPicture.asset('arrow-left-profile'.asIconSvg()),
          ),
          color: ColorConstants.color94684E,
        ),
        backgroundColor: ColorConstants.colorE1DDD2,
        titleTextStyle: TextStyle(
          fontSize: 18.sp,
          color: ColorConstants.primaryColor,
          fontWeight: FontWeight.w600,
        ),
        actions: [
          IconButton(
            onPressed: () {
              Navigator.pushNamed(
                context,
                EditProfileScreen.route,
              );
            },
            icon: SvgPicture.asset('edit'.asIconSvg()),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async => ref.invalidate(profileProvider),
        child: ListView(
          physics: const ClampingScrollPhysics(),
          padding: EdgeInsets.zero,
          children: [
            const ProfileView(),
            Gap(25.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                children: [
                  ProfileTile(
                    iconSVG: 'global',
                    title: tr(context, 'language'),
                    trailing: ProfileLanguageButton(),
                  ),
                  Gap(10.h),
                  ProfileTile(
                    iconSVG: 'document-text',
                    title: tr(context, 'terms_and_conditions'),
                    onTap: () => Navigator.pushNamed(
                      context,
                      TermsAndConditionsScreen.route,
                    ),
                  ),
                  Gap(10.h),
                  ProfileTile(
                    iconSVG: 'document-text',
                    title: tr(context, 'privacy_policies'),
                    onTap: () => Navigator.pushNamed(
                      context,
                      PrivacyPolicyScreen.route,
                    ),
                  ),
                  Gap(10.h),
                  // ProfileTile(
                  //   iconSVG: 'notification-profile',
                  //   title: tr(context, 'notification_settings'),
                  //   onTap: () => Navigator.pushNamed(
                  //     context,
                  //     NotificationSettingsScreen.route,
                  //   ),
                  // ),
                  // Gap(10.h),
                  ProfileTile(
                    iconSVG: 'message-question',
                    title: tr(context, 'faqs'),
                    onTap: () {
                      isFAQLoaded = false;
                      Navigator.pushNamed(
                        context,
                        FaqScreen.route,
                      );
                    },
                  ),
                  Gap(10.h),
                  ProfileTile(
                    iconSVG: 'call-calling',
                    title: tr(context, 'contact_us'),
                    onTap: () => Navigator.pushNamed(
                      context,
                      ContactUsScreen.route,
                    ),
                  ),
                  Gap(10.h),
                  ProfileTile(
                    iconSVG: 'logout',
                    title: tr(context, 'logout'),
                    onTap: () => _logout(context, ref),
                  ),
                ],
              ),
            ),
            Gap(30.h),
          ],
        ),
      ),
    );
  }

  _logout(BuildContext context, WidgetRef ref) async {
    final confirmLogout = await showModalBottomSheet(
      context: context,
      builder: (context) => LogoutConfirmation(),
    );
    if (confirmLogout == true) {
      AuthServices().signout();
      ref.read(masterProvider.notifier).state = 0;
      SecureStorageHelper.instance.clearAll();
      SharedPreferenceHelper.instance.clearAll();
      if (!context.mounted) return;
      Navigator.pushNamedAndRemoveUntil(
          context, SignInScreen.route, (route) => false);
    }
  }
}
