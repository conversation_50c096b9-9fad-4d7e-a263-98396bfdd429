import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';

class InitialImageUploadCard extends StatelessWidget {
  final void Function()? onTap;
  final bool error;
  const InitialImageUploadCard(
      {required this.onTap, this.error = false, super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            width: 1.w,
            color: error
                ? Theme.of(context).colorScheme.error
                : ColorConstants.primaryColor.withValues(alpha: 0.2),
          ),
          borderRadius: BorderRadius.circular(10.r),
          color: Colors.white,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 16.w,
          vertical: 14.h,
        ),
        child: Column(
          children: [
            Text(
              tr(context, 'upload_a_picture'),
              style: TextStyles.ts14w600CECECE,
            ),
            Gap(2.h),
            Container(
              height: 46.h,
              width: 46.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: ColorConstants.colorF9FAFB,
                border: Border.all(
                  width: 0.3,
                  color: ColorConstants.colorEAECF0,
                ),
              ),
              alignment: Alignment.center,
              child: SvgPicture.asset(
                'image'.asIconSvg(),
                height: 20.h,
                width: 20.w,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
