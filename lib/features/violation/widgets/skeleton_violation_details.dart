import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SkeletonViolationDetails extends StatelessWidget {
  const SkeletonViolationDetails({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: ListView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(
                width: 1.w,
                color: ColorConstants.colorEAEAEA,
              ),
            ),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              children: [
                Row(
                  children: [
                    Text.rich(
                      TextSpan(
                        text: tr(context, 'id'),
                        style: TextStyles.ts12w400c4D4D4D,
                        children: [
                          TextSpan(
                            text: ' 2586547',
                            style: TextStyles.ts12w700c353535,
                          ),
                        ],
                      ),
                    ),
                    Gap(9.w),
                    SizedBox(
                      height: 15.h,
                      child: VerticalDivider(
                        color: ColorConstants.colorF1F1F1,
                        thickness: 1.w,
                        width: 0,
                      ),
                    ),
                    Gap(9.w),
                    Text(
                      'Parking Violation',
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                    const Spacer(),
                    Container(
                      decoration: BoxDecoration(
                        color: ColorConstants.colorFFF7E2,
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: 10.w,
                        vertical: 2.h,
                      ),
                      child: Text(
                        'Not Settled',
                        style: TextStyles.ts12w500cE8B020,
                      ),
                    ),
                  ],
                ),
                Gap(16.h),
                Divider(
                  color: ColorConstants.colorF1F1F1,
                  thickness: 1.w,
                  height: 0,
                ),
                Gap(16.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      tr(context, 'violation_date_and_time'),
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                    Row(
                      children: [
                        SvgPicture.asset(
                          'clock'.asIconSvg(),
                          height: 14.h,
                          width: 14.w,
                        ),
                        Gap(5.w),
                        Text(
                          '20-11-2024, 12:00:00',
                          style: TextStyles.ts12w400c4D4D4D,
                        ),
                      ],
                    ),
                  ],
                ),
                Gap(10.h),
                violationTile(
                    title: tr(context, 'fined_amount'), value: '600 SAR'),
                Gap(10.h),
                violationTile(
                    title: tr(context, 'grace_period'), value: '7 min'),
                Gap(10.h),
                violationTile(
                    title: tr(context, 'violation_reported_by'),
                    value: 'Suleiman Al Zeyoudi'),
                Gap(10.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      tr(context, 'next_action_in'),
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                    Text(
                      '00:00:00',
                      style: TextStyles.ts12w600c4D4D4D,
                    )
                  ],
                )
              ],
            ),
          ),
          Gap(24.h),
          Text(
            tr(context, 'violation_description'),
            style: TextStyles.ts18w600c353535,
          ),
          Gap(6.h),
          Text(
            'The car is parked incorrectly, with its wheels crossing the parking lines, taking up space and affecting nearby spots.',
            style: TextStyles.ts14w500c959595,
          ),
          Gap(30.h),
          Text(
            tr(context, 'violation_images'),
            style: TextStyles.ts18w600c353535,
          ),
          Gap(16.h),
          Wrap(
            spacing: 12.w,
            runSpacing: 12.h,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(10.r),
                child: Image.asset(
                  "violation-sample".asImagePng(),
                  height: 154.h,
                  width: (1.sw - 44.w) / 2,
                  fit: BoxFit.cover,
                ),
              ),
              ClipRRect(
                borderRadius: BorderRadius.circular(10.r),
                child: Image.asset(
                  "violation-sample".asImagePng(),
                  height: 154.h,
                  width: (1.sw - 44.w) / 2,
                  fit: BoxFit.cover,
                ),
              )
            ],
          )
        ],
      ),
    );
  }

  Widget violationTile({required String title, required String value}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: TextStyles.ts12w400c4D4D4D,
        ),
        Text(
          value,
          style: TextStyles.ts12w400c4D4D4D,
        )
      ],
    );
  }
}
