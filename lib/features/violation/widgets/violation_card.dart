import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/vehicle_info_chip.dart';
import 'package:albalad_operator_app/features/vehicle_details/widgets/vehicle_parking_timer.dart';
import 'package:albalad_operator_app/features/violation/view/violation_details_screen.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/models/current_violation.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class ViolationCard extends StatelessWidget {
  final CurrentViolation violation;
  final String previousRoute;
  const ViolationCard(
      {required this.violation, required this.previousRoute, super.key});

  int getRemainingTimeInSeconds(String? graceEndTime) {
    if (graceEndTime == null || graceEndTime.isEmpty) return 0;
    final endTime = DateTime.parse(graceEndTime);
    final now = DateTime.now();
    final remainingTime = endTime.difference(now);
    if (remainingTime.isNegative) {
      return 0;
    }
    return remainingTime.inSeconds;
  }

  @override
  Widget build(BuildContext context) {
    String? vehicleImage = violation.vehicleImage?.firstOrNull?.image;
    return InkWell(
      onTap: () {
        final arguments = ViolationDetailsScreen(
          violationUid: violation.uid ?? '',
          previousRoute: previousRoute,
        );
        Navigator.pushNamed(
          context,
          ViolationDetailsScreen.route,
          arguments: arguments,
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              offset: const Offset(0, 0),
              blurRadius: 20,
              spreadRadius: 0,
            ),
          ],
        ),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text.rich(
                            TextSpan(
                              text: tr(context, 'id'),
                              style: TextStyles.ts12w400c4D4D4D,
                              children: [
                                TextSpan(
                                  text: ' ${violation.violationId}',
                                  style: TextStyles.ts12w700c353535,
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 9.w,
                            child: VerticalDivider(
                              thickness: 1,
                              color: ColorConstants.colorF1F1F1,
                            ),
                          ),
                          Flexible(
                            child: Text(
                              violation.violationType ?? '',
                              style: TextStyles.ts12w400c4D4D4D,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      Gap(10.h),
                      Row(
                        children: [
                          Container(
                            height: 35.h,
                            width: 35.w,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                              color: ColorConstants.colorF1EFE9,
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8.r),
                              child: CachedNetworkImage(
                                imageUrl: vehicleImage ?? 'https://',
                                memCacheWidth: 100,
                                fit: BoxFit.fill,
                                errorWidget: (context, url, error) =>
                                    Image.asset('car'.asImagePng()),
                              ),
                            ),
                          ),
                          Gap(10.w),
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  violation.vehicleNumber ?? '',
                                  style: TextStyles.ts14w600c181818,
                                ),
                                Text(
                                  violation.vehicleName ?? '',
                                  style: TextStyles.ts10w400cA1A09B,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Gap(16.h),
                      Wrap(
                        spacing: 8.w,
                        runSpacing: 8.h,
                        children: [
                          if (violation.makeYear != null)
                            VehicleInfoChip(
                              title: violation.makeYear.toString(),
                            ),
                          if (violation.vehicleType != null)
                            VehicleInfoChip(
                              title: violation.vehicleType ?? '',
                            ),
                          // if (parkedVehicle.isSharedVehicle == true)
                          //   VehicleInfoChip(
                          //     title: tr(context, 'shared'),
                          //     shared: true,
                          //   ),
                          if (violation.numberPlateType != null)
                            VehicleInfoChip(
                              title: violation.numberPlateType ?? '',
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
                if (violation.gracePeriodIsOver != true)
                  VehicleParkingTimer(
                    totalTimeInSeconds: violation.gracePeriodSeconds ?? 0,
                    remainingTimeInSeconds: getRemainingTimeInSeconds(
                      violation.violationGrancePeriodEnd,
                    ),
                  ),
              ],
            ),
            Gap(16.h),
            Divider(
              color: ColorConstants.colorF1F1F1,
              thickness: 1.h,
              height: 0,
            ),
            Gap(10.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  tr(context, 'violation_date_and_time'),
                  style: TextStyles.ts12w400c4D4D4D,
                ),
                Row(
                  children: [
                    SvgPicture.asset(
                      'clock'.asIconSvg(),
                      height: 14.h,
                      width: 14.w,
                    ),
                    Gap(5.w),
                    Text(
                      violation.violationDateTime ?? '',
                      style: TextStyles.ts12w400c4D4D4D,
                      textDirection: TextDirection.ltr,
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
