import 'package:albalad_operator_app/features/violation/models/payment_method.dart';
import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_searchable_dropdown.dart';
import 'package:albalad_operator_app/shared/widgets/skeleton_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class PaymentMethodDropdown extends ConsumerWidget {
  final void Function(PaymentMethod?)? onChanged;
  final PaymentMethod? value;
  const PaymentMethodDropdown({required this.onChanged, this.value, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final provider = ref.watch(paymentMethodProvider);
    return provider.when(
      data: (paymentMethods) {
        List<PaymentMethod> items = paymentMethods;
        return CustomSearchableDropdown(
          items: items,
          displayBuilder: (p0) => p0.name ?? '',
          onChanged: onChanged,
          labelText: tr(context, 'payment_method_star'),
          hintText: tr(context, 'select'),
          title: tr(context, 'payment_method'),
          searchHintText: tr(context, 'search_payment_method'),
          value: value,
          validator: (p0) {
            if (p0 == null || p0.isEmpty) {
              return tr(context, 'please_select_payment_method');
            }
            return null;
          },
        );
        // return CustomDropdownField<PaymentMethod?>(
        //   items: items
        //       .map(
        //         (e) => DropdownMenuItem(value: e, child: Text(e.name ?? '')),
        //       )
        //       .toList(),
        //   onChanged: onChanged,
        //   label: tr(context, 'payment_method_star'),
        //   hint: tr(context, 'select'),
        //   value: value,
        //   validator: (p0) {
        //     if (p0 == null) {
        //       return tr(context, 'please_select_payment_method');
        //     }
        //     return null;
        //   },
        // );
      },
      error: (error, stackTrace) => SkeletonDropdown(enabled: true),
      loading: () => const SkeletonDropdown(),
    );
  }
}
