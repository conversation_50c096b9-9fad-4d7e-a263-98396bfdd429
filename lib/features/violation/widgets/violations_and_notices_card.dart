import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/vehicle_info_chip.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/models/vehicle_details.dart';
import 'package:albalad_operator_app/shared/widgets/inner_assign_button.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class ViolationsAndNoticesCard extends StatelessWidget {
  final VehicleDetails vehicle;
  const ViolationsAndNoticesCard({required this.vehicle, super.key});

  @override
  Widget build(BuildContext context) {
    String vehicleImage = vehicle.vehicleImage?.firstOrNull?.image ?? '';
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    return InkWell(
      onTap: () {
        final arguments = VehicleDetailsScreen(
          uid: vehicle.uid ?? '',
        );
        Navigator.pushNamed(
          context,
          VehicleDetailsScreen.route,
          arguments: arguments,
        );
      },
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  offset: const Offset(0, 0),
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  spreadRadius: 0,
                ),
              ],
            ),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      height: 35.h,
                      width: 35.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                        color: ColorConstants.colorF1EFE9,
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8.r),
                        child: CachedNetworkImage(
                          imageUrl: vehicleImage,
                          memCacheWidth: 200,
                          fit: BoxFit.fill,
                          errorWidget: (context, url, error) =>
                              Image.asset('car'.asImagePng()),
                        ),
                      ),
                    ),
                    Gap(10.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          vehicle.number ?? '',
                          style: TextStyles.ts14w600c181818,
                        ),
                        Text(
                          vehicle.vehicleName ?? '',
                          style: TextStyles.ts10w400cA1A09B,
                        ),
                      ],
                    ),
                  ],
                ),
                Gap(16.h),
                Row(
                  spacing: 8.w,
                  children: [
                    if (vehicle.makeYear != null)
                      VehicleInfoChip(
                        title: vehicle.makeYear.toString(),
                      ),
                    if (vehicle.vehicleType != null)
                      VehicleInfoChip(
                        title: vehicle.vehicleType ?? '',
                      ),
                    if (vehicle.isSharedVehicle == true)
                      VehicleInfoChip(
                        title: tr(context, 'shared'),
                        shared: true,
                      ),
                    if (vehicle.numberPlateType != null)
                      VehicleInfoChip(
                        title: vehicle.numberPlateType ?? '',
                      ),
                  ],
                ),
              ],
            ),
          ),
          Positioned(
            top: 0.h,
            right: isRTL ? null : 10.w,
            left: isRTL ? 10.w : null,
            child: InnerAssignButton(
              onPressed: () {
                final arguments = VehicleDetailsScreen(
                  uid: vehicle.uid ?? '',
                );
                Navigator.pushNamed(
                  context,
                  VehicleDetailsScreen.route,
                  arguments: arguments,
                );
              },
              title: tr(context, 'assign_violation'),
            ),
          ),
        ],
      ),
    );
  }
}

class SkeletonViolationsAndNoticesCard extends StatelessWidget {
  const SkeletonViolationsAndNoticesCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                offset: const Offset(0, 0),
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                spreadRadius: 0,
              ),
            ],
          ),
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    height: 35.h,
                    width: 35.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      color: ColorConstants.colorF1EFE9,
                    ),
                    child: CachedNetworkImage(
                      imageUrl: '',
                      memCacheWidth: 100,
                      errorWidget: (context, url, error) =>
                          Image.asset('car'.asImagePng()),
                    ),
                  ),
                  Gap(10.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '7403 RUA',
                        style: TextStyles.ts14w600c181818,
                      ),
                      Text(
                        'Toyota Camry',
                        style: TextStyles.ts10w400cA1A09B,
                      ),
                    ],
                  ),
                ],
              ),
              Gap(16.h),
              Row(
                spacing: 8.w,
                children: [
                  VehicleInfoChip(
                    title: '2002',
                  ),
                  VehicleInfoChip(
                    title: 'Sedan',
                  ),
                  // if (parkedVehicle.isSharedVehicle == true)
                  //   VehicleInfoChip(
                  //     title: tr(context, 'shared'),
                  //     shared: true,
                  //   ),
                  VehicleInfoChip(
                    title: 'Private',
                  ),
                ],
              ),
            ],
          ),
        ),
        Positioned(
          top: 0.h,
          right: 10.w,
          child: InnerAssignButton(
            onPressed: () {},
            title: tr(context, 'assign_violation'),
          ),
        ),
      ],
    );
  }
}
