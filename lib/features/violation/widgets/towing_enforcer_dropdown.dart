import 'package:albalad_operator_app/features/violation/models/enforcer.dart';
import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_searchable_dropdown.dart';
import 'package:albalad_operator_app/shared/widgets/skeleton_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class TowingEnforcerDropdown extends ConsumerWidget {
  final String enforcerType;
  final Enforcer? value;
  final void Function(Enforcer?)? onChanged;
  final AutovalidateMode? autovalidateMode;
  const TowingEnforcerDropdown(
      {required this.enforcerType,
      required this.value,
      required this.onChanged,
      this.autovalidateMode,
      super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final enforcers = ref.watch(enforcersProvider(enforcerType));
    return enforcers.when(
      data: (values) {
        List<Enforcer> items = values;
        return CustomSearchableDropdown<Enforcer>(
          items: items,
          displayBuilder: (p0) => p0.name ?? '',
          onChanged: onChanged,
          labelText: tr(context, 'towing_enforcer_star'),
          title: tr(context, 'towing_enforcer'),
          hintText: tr(context, 'select_towing_enforcer'),
          searchHintText: tr(context, 'search_towing_enforcer'),
          autovalidateMode: autovalidateMode,
          value: value,
          validator: (p0) {
            if (p0 == null || p0.isEmpty) {
              return tr(context, 'please_select_towing_enforcer');
            }
            return null;
          },
        );
      },
      error: (error, stackTrace) => SkeletonDropdown(enabled: true),
      loading: () => const SkeletonDropdown(),
    );
  }
}
