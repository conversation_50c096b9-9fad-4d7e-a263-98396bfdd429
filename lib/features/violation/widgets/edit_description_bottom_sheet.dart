import 'package:albalad_operator_app/features/violation/provider/parking_violation_provider.dart';
import 'package:albalad_operator_app/features/violation/provider/update_violation_provider.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/custom_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class EditDescriptionBottomSheet extends HookConsumerWidget {
  final String description;
  final String violationId;
  const EditDescriptionBottomSheet(
      {required this.description, required this.violationId, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    double bottom = MediaQuery.of(context).viewInsets.bottom;
    double paddingBottom = 30.h;
    if (bottom > 0) {
      paddingBottom = bottom;
    }
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final descriptionFilled = useState(false);
    final descriptionController = useTextEditingController();
    if (!descriptionFilled.value) {
      descriptionController.text = description;
      descriptionFilled.value = true;
    }

    ref.listen(
      updateViolationProvider,
      (previous, next) {
        if (next is UpdateViolationSuccess) {
          ref
              .read(parkingViolationNotifierProvider.notifier)
              .getViolationDetails(violationId);
          Navigator.pop(context);
        }
      },
    );

    final state = ref.watch(updateViolationProvider);
    final notifier = ref.read(updateViolationProvider.notifier);

    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 43.h, 16.w, paddingBottom),
      child: SingleChildScrollView(
        child: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                tr(context, 'update_description'),
                style: TextStyles.ts18w600c353535,
              ),
              Gap(10.h),
              CustomTextFormField(
                controller: descriptionController,
                labelText: tr(context, 'violation_description'),
                hintText: tr(context, 'description_here'),
                maxLines: 5,
                validator: (p0) {
                  if (p0 == null || p0.isEmpty) {
                    return tr(context, 'please_enter_violation_description');
                  }
                  return null;
                },
              ),
              Gap(20.h),
              if (state is UpdateViolationLoading)
                const Center(
                  child: CustomGradientSpinner(),
                )
              else
                ElevatedButton(
                  onPressed: () {
                    if (formKey.currentState?.validate() == true) {
                      notifier.updateDescription(
                        violationUid: violationId,
                        description: descriptionController.text,
                      );
                    }
                  },
                  child: Text(tr(context, 'submit')),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
