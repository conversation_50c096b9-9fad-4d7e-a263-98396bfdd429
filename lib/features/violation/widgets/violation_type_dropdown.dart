import 'package:albalad_operator_app/features/violation/models/violation_type.dart';
import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:albalad_operator_app/features/violation/violation_type.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_searchable_dropdown.dart';
import 'package:albalad_operator_app/shared/widgets/skeleton_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ViolationTypeDropdown extends ConsumerWidget {
  final VIOLATIONTYPE violationtype;
  final ViolationType? value;
  final AutovalidateMode? autovalidateMode;
  final void Function(ViolationType?)? onChanged;
  const ViolationTypeDropdown({
    required this.violationtype,
    required this.value,
    required this.onChanged,
    this.autovalidateMode,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final violationTypesAsyncValue =
        ref.watch(violationTypesProvider(violationtype));
    return violationTypesAsyncValue.when(
      data: (violationTypes) {
        List<ViolationType> items = violationTypes;
        return CustomSearchableDropdown<ViolationType>(
          items: items,
          displayBuilder: (p0) => p0.name ?? '',
          onChanged: onChanged,
          labelText: tr(context, 'violation_type_star'),
          title: tr(context, 'violation_type'),
          hintText: tr(context, 'select_violation_type'),
          searchHintText: tr(context, 'search_violation_type'),
          autovalidateMode: autovalidateMode,
          value: value,
          validator: (p0) {
            if (p0 == null || p0.isEmpty) {
              return tr(context, 'please_select_violation_type');
            }
            return null;
          },
        );
      },
      error: (error, stackTrace) => SkeletonDropdown(enabled: true),
      loading: () => const SkeletonDropdown(),
    );
  }
}
