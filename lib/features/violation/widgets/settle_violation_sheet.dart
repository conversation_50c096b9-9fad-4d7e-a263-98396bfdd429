import 'package:albalad_operator_app/features/home/<USER>/home_provider.dart';
import 'package:albalad_operator_app/features/success_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/provider/vehicle_details_provider.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/features/violation/models/payment_method.dart';
import 'package:albalad_operator_app/features/violation/models/settlement_type.dart';
import 'package:albalad_operator_app/features/violation/provider/current_violation_listing_provider.dart';
import 'package:albalad_operator_app/features/violation/provider/settle_violation_provider.dart';
import 'package:albalad_operator_app/features/violation/provider/settle_violation_state.dart';
import 'package:albalad_operator_app/features/violation/view/current_violations_screen.dart';
import 'package:albalad_operator_app/features/violation/widgets/payment_method_dropdown.dart';
import 'package:albalad_operator_app/features/violation/widgets/settlement_type_dropdown.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/constants/violation_status.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/helper/dialog_helper.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/custom_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SettleViolationSheet extends HookConsumerWidget {
  final String violationUid;
  final bool isFined;
  final String? vehicleUid;
  final String previousRoute;
  const SettleViolationSheet({
    required this.violationUid,
    required this.isFined,
    required this.previousRoute,
    this.vehicleUid,
    super.key,
  });

  updateDetails(WidgetRef ref) {
    ref.invalidate(homeSummaryProvider);
    updateVehicleDetails(ref);
    updateCurrentViolationList(ref);
  }

  updateVehicleDetails(WidgetRef ref) {
    if (previousRoute == VehicleDetailsScreen.route && vehicleUid != null) {
      final notifier = ref.read(vehicleDetailsNotifierProvider.notifier);
      notifier.getVehicleDetails(uid: vehicleUid);
    }
  }

  updateCurrentViolationList(WidgetRef ref) {
    if (previousRoute == CurrentViolationsScreen.route) {
      final notifier =
          ref.read(currentVioaltionListingNotifierProvider.notifier);
      notifier.reset();
      notifier.fetchCurrentViolations();
    }
  }

  showSuccessScreen(BuildContext context) {
    final arguments = SuccessScreen(
      title: tr(context, 'violation_settled_successfully'),
      message: tr(context, 'violation_settled_success_message'),
      onPressed: (context) => Navigator.popUntil(
        context,
        ModalRoute.withName(previousRoute),
      ),
    );
    Navigator.pushReplacementNamed(
      context,
      SuccessScreen.route,
      arguments: arguments,
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    double bottom = MediaQuery.of(context).viewInsets.bottom;
    double paddingBottom = 30.h;
    if (bottom > 0) {
      paddingBottom = bottom;
    }

    final formKey = useMemoized(() => GlobalKey<FormState>());
    final selectedStatus = useState<SettlementType?>(null);
    final selectedPaymentMethod = useState<PaymentMethod?>(null);
    final noteController = useTextEditingController();

    final notifier = ref.read(settleViolationNotifierProvider.notifier);
    final state = ref.watch(settleViolationNotifierProvider);

    ref.listen(
      settleViolationNotifierProvider,
      (previous, next) {
        if (next is SettleViolationSuccess) {
          updateDetails(ref);
          showSuccessScreen(context);
        }
        if (next is SettleViolationFailure) {
          DialogHelper.showErrorDialog(context: context, message: next.message);
        }
      },
    );

    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 43.h, 16.w, paddingBottom),
      child: SingleChildScrollView(
        child: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                tr(context, 'settle_violation'),
                style: TextStyles.ts18w600c353535,
              ),
              Gap(10.h),
              SettlementTypeDropdown(
                onChanged: (p0) {
                  selectedStatus.value = p0;
                  selectedPaymentMethod.value = null;
                },
                value: selectedStatus.value,
              ),
              Gap(24.h),
              if (isFined &&
                  int.tryParse('${selectedStatus.value?.id}') ==
                      ViolationStatus.settled) ...[
                PaymentMethodDropdown(
                  onChanged: (p0) => selectedPaymentMethod.value = p0,
                  value: selectedPaymentMethod.value,
                ),
                Gap(24.h)
              ],
              CustomTextFormField(
                controller: noteController,
                labelText: tr(context, 'note_star'),
                hintText: tr(context, 'customer_have_settled_ticket'),
                maxLines: 5,
                validator: (p0) {
                  if (p0 == null || p0.isEmpty) {
                    return tr(context, 'please_enter_note');
                  }
                  // if (p0.length < 10) {
                  //   return context.loc
                  //       .settle_violation_note_minimum_chars_validation(10);
                  // }
                  return null;
                },
              ),
              Gap(20.h),
              if (state is SettleViolationLoading)
                const Center(
                  child: CustomGradientSpinner(),
                )
              else
                ElevatedButton(
                  onPressed: () {
                    if (formKey.currentState?.validate() == true) {
                      notifier.submit(
                        violationUid: violationUid,
                        status: selectedStatus.value?.id ?? '',
                        paymentMethodUid:
                            selectedPaymentMethod.value?.uid ?? '',
                        description: noteController.text,
                      );
                    }
                  },
                  child: Text(tr(context, 'submit')),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
