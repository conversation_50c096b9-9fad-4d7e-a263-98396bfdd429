import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ViolationTypeSelection extends StatelessWidget {
  final String value;
  final Function(String) onChanged;
  const ViolationTypeSelection(
      {required this.value, required this.onChanged, super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 10.w,
      children: [
        {'name': tr(context, 'all'), 'value': 'all'},
        {'name': tr(context, 'parking'), 'value': 'parking'},
        {'name': tr(context, 'clamping'), 'value': 'clamping'},
        {'name': tr(context, 'towing'), 'value': 'towing'},
      ].map(
        (e) {
          bool isSelected = value == e['value'];
          return InkWell(
            onTap: () => onChanged(e['value'].toString()),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(
                  width: 0.5,
                  color: ColorConstants.colorE1DDD2,
                ),
                color: isSelected ? ColorConstants.primaryColor : null,
              ),
              padding: EdgeInsets.symmetric(
                horizontal: 18.w,
                vertical: 7.h,
              ),
              child: Text(
                e['name'].toString(),
                style: isSelected
                    ? TextStyles.ts12w600wE1DDD2
                    : TextStyles.ts12w600w94684E,
              ),
            ),
          );
        },
      ).toList(),
    );
  }
}
