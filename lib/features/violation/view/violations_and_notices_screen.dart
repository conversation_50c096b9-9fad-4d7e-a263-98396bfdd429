import 'package:albalad_operator_app/features/add_vehicle/view/add_vehicle_screen.dart';
import 'package:albalad_operator_app/features/home/<USER>/home_search_field.dart';
import 'package:albalad_operator_app/features/number_plate_scanner/view/number_plate_scanner.dart';
import 'package:albalad_operator_app/features/search/provider/search_provider.dart';
import 'package:albalad_operator_app/features/search/view/search_screen.dart';
import 'package:albalad_operator_app/features/violation/provider/vioaltions_and_notices_provider.dart';
import 'package:albalad_operator_app/features/violation/widgets/violations_and_notices_card.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/bottom_navigation_bar_elevated_button.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:albalad_operator_app/shared/widgets/no_vehicle_widget.dart';
import 'package:albalad_operator_app/shared/widgets/smart_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ViolationsAndNoticesScreen extends ConsumerWidget {
  static const route = '/violations_and_notices';
  const ViolationsAndNoticesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifier = ref.read(violationsAndNoticesNotifierProvider.notifier);
    final state = ref.watch(violationsAndNoticesNotifierProvider);

    // Trigger the API call when the screen is first built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (state.items.isEmpty) {
        notifier.fetchtViolationsAndNoticesVehicleList();
      }
    });
    return SmartScaffold(
      isInternetAvailable: state.isConnectionError == false,
      retryConnection: () {
        notifier.reset();
        notifier.fetchtViolationsAndNoticesVehicleList();
      },
      appBar: InnerAppBar(
        title: Text(tr(context, 'violations')),
      ),
      body: NotificationListener<ScrollNotification>(
        onNotification: (scrollNotification) {
          if (scrollNotification is ScrollEndNotification &&
              scrollNotification.metrics.extentAfter == 0 &&
              state.isLoading == false) {
            notifier.fetchtViolationsAndNoticesVehicleList();
          }
          return false;
        },
        child: RefreshIndicator(
          onRefresh: () {
            notifier.reset();
            return notifier.fetchtViolationsAndNoticesVehicleList();
          },
          child: ListView(
            padding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 30.h,
            ),
            children: [
              HomeSearchField(
                hintText: tr(context, 'searchVehicleNumber'),
                readOnly: true,
                onPress: () {
                  final notifier = ref.read(searchNotifierProvider.notifier);
                  notifier.reset();
                  final arguments = SearchScreen(previousRoute: route);
                  Navigator.pushNamed(context, SearchScreen.route,
                      arguments: arguments);
                },
                onTapSuffix: () {
                  final arguments = NumberPlateScanner(previousRoute: route);
                  Navigator.of(context).pushNamed(
                    NumberPlateScanner.route,
                    arguments: arguments,
                  );
                },
              ),
              Gap(38.h),
              Text(
                tr(context, 'vehicles_list'),
                style: TextStyles.ts18w600c353535,
              ),
              Gap(7.h),
              if (state.items.isEmpty && !state.hasMore) ...[
                Gap(0.1.sh),
                const NoVehiclesWidget(),
              ] else if (state.items.isEmpty && state.isLoading) ...[
                const SkeletonViolationsList(),
              ] else
                ListView.separated(
                  shrinkWrap: true,
                  physics: const PageScrollPhysics(),
                  itemBuilder: (context, index) {
                    if (index == state.items.length) {
                      return const Center(child: CustomGradientSpinner());
                    }
                    final item = state.items[index];
                    return ViolationsAndNoticesCard(vehicle: item);
                  },
                  separatorBuilder: (context, index) => SizedBox(height: 24.h),
                  itemCount: state.items.length + (state.hasMore ? 1 : 0),
                ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: BottomNavigationBarElevatedButton(
        title: tr(context, 'addVehicle'),
        onPressed: () => Navigator.pushNamed(context, AddVehicleScreen.route),
      ),
    );
  }
}

class SkeletonViolationsList extends StatelessWidget {
  const SkeletonViolationsList({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: ListView.separated(
        shrinkWrap: true,
        physics: const PageScrollPhysics(),
        itemBuilder: (context, index) =>
            const SkeletonViolationsAndNoticesCard(),
        separatorBuilder: (context, index) => Gap(24.h),
        itemCount: 5,
      ),
    );
  }
}
