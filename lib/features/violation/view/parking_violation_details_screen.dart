import 'dart:io';

import 'package:albalad_operator_app/features/clamp_violation/view/clamp_vehicle_screen.dart';
import 'package:albalad_operator_app/features/image_preview_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/tow_vehicle_screen.dart';
import 'package:albalad_operator_app/features/violation/provider/next_action_timer_provider.dart';
import 'package:albalad_operator_app/features/violation/provider/parking_violation_provider.dart';
import 'package:albalad_operator_app/features/violation/provider/parking_violation_state.dart';
import 'package:albalad_operator_app/features/violation/provider/update_violation_provider.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/assign_clamping_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/assign_towing_screen.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/clamp_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/tow_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/violation/view/violation_details_screen.dart';
import 'package:albalad_operator_app/features/violation/widgets/edit_description_bottom_sheet.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/bottom_navigation_bar_elevated_button.dart';
import 'package:albalad_operator_app/shared/widgets/currency_icon.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/image_source_sheet.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:albalad_operator_app/shared/widgets/smart_scaffold.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ParkingViolationDetailsScreen extends ConsumerWidget {
  static const String route = '/parking-violation-details-screen';
  final String violationUid;
  final String previousRoute;
  const ParkingViolationDetailsScreen({
    required this.violationUid,
    required this.previousRoute,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(parkingViolationNotifierProvider);
    final notifier = ref.read(parkingViolationNotifierProvider.notifier);

    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        if (state is ParkingViolationInitial) {
          notifier.getViolationDetails(violationUid);
        }
      },
    );
    return SmartScaffold(
      isInternetAvailable:
          !(state is ParkingViolationError && state.isConnectionError),
      retryConnection: () => notifier.getViolationDetails(violationUid),
      appBar: InnerAppBar(
        title: Text(tr(context, 'violation_details')),
      ),
      body: RefreshIndicator(
        onRefresh: () => notifier.getViolationDetails(violationUid),
        child: Builder(
          builder: (context) {
            if (state is ParkingViolationLoading) {
              return const SkeletonParkingViolationDetails();
            }
            if (state is ParkingViolationError) {
              return Center(
                child: Text(
                  state.message,
                  style: TextStyles.ts14w400c4D4D4D,
                  textAlign: TextAlign.center,
                ),
              );
            }
            if (state is ParkingViolationSuccess) {
              final violation = state.violation;
              final parkingViolationImages = violation.violationImage ?? [];
              final clampingImages = violation.clampedImages ?? [];
              final towImages = violation.towedImages ?? [];
              final violationImages = [
                ...parkingViolationImages,
                ...clampingImages,
                ...towImages
              ];
              String description = violation.violationDescription ?? '';
              if (violation.clampedDescription != null &&
                  violation.clampedDescription!.isNotEmpty) {
                description = violation.clampedDescription!;
              }
              if (violation.towedDescription != null &&
                  violation.towedDescription!.isNotEmpty) {
                description = violation.towedDescription!;
              }
              return ListView(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16.r),
                      border: Border.all(
                        width: 1.w,
                        color: ColorConstants.colorEAEAEA,
                      ),
                    ),
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    child: Column(
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: Row(
                            children: [
                              Text.rich(
                                TextSpan(
                                  text: tr(context, 'id'),
                                  style: TextStyles.ts12w400c4D4D4D,
                                  children: [
                                    TextSpan(
                                      text: ' ${violation.violationId}',
                                      style: TextStyles.ts12w700c353535,
                                    ),
                                  ],
                                ),
                              ),
                              Gap(9.w),
                              SizedBox(
                                height: 15.h,
                                child: VerticalDivider(
                                  color: ColorConstants.colorF1F1F1,
                                  thickness: 1.w,
                                  width: 0,
                                ),
                              ),
                              Gap(9.w),
                              Expanded(
                                child: Text(
                                  violation.violationType ?? '',
                                  style: TextStyles.ts12w400c4D4D4D,
                                ),
                              ),
                              Gap(9.w),
                              Container(
                                decoration: BoxDecoration(
                                  color: ColorConstants.colorFFF7E2,
                                  borderRadius: BorderRadius.circular(20.r),
                                ),
                                padding: EdgeInsets.symmetric(
                                  horizontal: 10.w,
                                  vertical: 2.h,
                                ),
                                child: Text(
                                  violation.paymentStatus ?? '',
                                  style: TextStyles.ts12w600cE8B020,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Gap(16.h),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: Divider(
                            color: ColorConstants.colorF1F1F1,
                            thickness: 1.w,
                            height: 0,
                          ),
                        ),
                        Gap(16.h),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                tr(context, 'violation_date_and_time'),
                                style: TextStyles.ts12w400c4D4D4D,
                              ),
                              Row(
                                children: [
                                  SvgPicture.asset(
                                    'clock'.asIconSvg(),
                                    height: 14.h,
                                    width: 14.w,
                                  ),
                                  Gap(5.w),
                                  Text(
                                    violation.violationDateTime ?? '',
                                    style: TextStyles.ts12w400c4D4D4D,
                                    textDirection: TextDirection.ltr,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Gap(10.h),
                        if (violation.fineAmount != null &&
                            violation.fineAmount != 0) ...[
                          ...[
                            violationTile(
                              currencyEnabled: true,
                              title: tr(context, 'fined_amount'),
                              value: '${violation.fineAmount}  ',
                            ),
                            Gap(10.h),
                            violationTile(
                              currencyEnabled: true,
                              title: tr(context, 'vat'),
                              value: '${violation.vatAmount}  ',
                            ),
                            Gap(10.h),
                            Container(
                              decoration: BoxDecoration(
                                color: ColorConstants.colorF8F8F8,
                                gradient: RadialGradient(
                                  colors: [
                                    Colors.white,
                                    Colors.white,
                                    ColorConstants.colorF9F9FF,
                                  ],
                                ),
                              ),
                              padding: EdgeInsets.symmetric(
                                  vertical: 5.h, horizontal: 16.w),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    tr(context, 'total_amount'),
                                    style: TextStyles.ts12w400c4D4D4D,
                                  ),
                                  const Spacer(),
                                  Text.rich(
                                    TextSpan(
                                      text: '${violation.fineAmountAfterVat}',
                                      style: TextStyles.ts12w700c44322D,
                                      // children: [
                                      //   TextSpan(
                                      //     text: ' ${tr(context, 'sar')}',
                                      //     style: TextStyles.ts12w400c4C4C4C,
                                      //   ),
                                      // ],
                                    ),
                                  ),
                                  const Gap(2),
                                  const CurrencyIcon(),
                                ],
                              ),
                            ),
                            Gap(10.h),
                          ],
                        ],
                        if (violation.violationReportedBy != null) ...[
                          violationTile(
                            title: tr(context, 'violation_reported_by'),
                            value: violation.violationReportedBy ?? '',
                          ),
                          Gap(10.h),
                        ],
                        if (violation.gracePeriod != null) ...[
                          violationTile(
                            title: tr(context, 'grace_period'),
                            value: '${violation.gracePeriod}',
                          ),
                          Gap(10.h),
                        ],
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: NextActionTimer(
                            endTime: violation.gracePeriodEnd,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Gap(24.h),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: ColorConstants.colorFFFFFF,
                      boxShadow: [
                        BoxShadow(
                          color:
                              ColorConstants.color000000.withValues(alpha: 0.1),
                          blurRadius: 20.0,
                          spreadRadius: 0,
                          offset: Offset(0, 0),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: 16.w, vertical: 16.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            tr(context, 'violation_description'),
                            style: TextStyles.ts18w600c353535,
                          ),
                          Gap(6.h),
                          InkWell(
                            onTap: () {
                              showModalBottomSheet(
                                context: context,
                                isScrollControlled: true,
                                builder: (context) =>
                                    EditDescriptionBottomSheet(
                                  description: description,
                                  violationId: violationUid,
                                ),
                              );
                            },
                            child: Text(
                              description,
                              style: TextStyles.ts14w600c959595,
                            ),
                          ),
                          Gap(8.h),
                        ],
                      ),
                    ),
                  ),
                  Gap(24.h),
                  Text(
                    tr(context, 'violation_images'),
                    style: TextStyles.ts18w600c353535,
                  ),
                  Gap(9.h),
                  Wrap(
                    spacing: 12.w,
                    runSpacing: 12.h,
                    children: violationImages.map(
                      (e) {
                        return Hero(
                          tag: e.uid ?? '',
                          child: InkWell(
                            onTap: () {
                              if (e.image != null && e.image!.isNotEmpty) {
                                int initialIndex = violationImages.indexOf(e);
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => ImagePreviewScreen(
                                      violationImages: violationImages,
                                      pageController: PageController(
                                        initialPage: initialIndex,
                                      ),
                                      initialIndex: initialIndex,
                                    ),
                                  ),
                                );
                              }
                            },
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(10.r),
                              child: CachedNetworkImage(
                                imageUrl: e.image ?? '',
                                height: 154.h,
                                width: (1.sw - 44.w) / 2,
                                fit: BoxFit.cover,
                                placeholder: (context, url) =>
                                    CustomGradientSpinner(),
                                errorWidget: (context, url, error) {
                                  return Image.asset(
                                    "violation-sample".asImagePng(),
                                    height: 154.h,
                                    width: (1.sw - 44.w) / 2,
                                    fit: BoxFit.cover,
                                  );
                                },
                              ),
                            ),
                          ),
                        );
                      },
                    ).toList(),
                  ),
                  Gap(10.h),
                  AddMoreViolationImages(violationUid: violationUid),
                ],
              );
            }
            return const SizedBox();
          },
        ),
      ),
      bottomNavigationBar: actionButton(
        context: context,
        state: state,
      ),
    );
  }

  Widget? actionButton({
    required BuildContext context,
    required ParkingViolationState state,
  }) {
    if (previousRoute.isNotEmpty && state is ParkingViolationSuccess) {
      String vehicleUid = state.violation.vehicleUid ?? '';
      String gracePeriodEnd = state.violation.gracePeriodEnd ?? '';
      switch (previousRoute) {
        case ClampVehicleScreen.route:
          return BottomNavigationBarElevatedButton(
            title: tr(context, 'clamp'),
            onPressed: () => clampVehicle(
              context: context,
              vehicleUid: vehicleUid,
              violationUid: violationUid,
            ),
          );
        case TowVehicleScreen.route:
          return BottomNavigationBarElevatedButton(
            title: tr(context, 'tow'),
            onPressed: () => towVehicle(
              context: context,
              vehicleUid: vehicleUid,
              violationUid: violationUid,
            ),
          );
        default:
          return AssignClampinButton(
            vehicleUid: vehicleUid,
            endTime: gracePeriodEnd,
            previousRoute: previousRoute,
          );
      }
    }
    return null;
  }

  Widget violationTile(
      {required String title,
      required String value,
      bool currencyEnabled = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.h),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: TextStyles.ts12w400c4D4D4D,
            ),
          ),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  value,
                  style: TextStyles.ts12w400c4D4D4D,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.end,
                ),
                if (currencyEnabled) CurrencyIcon(height: 9),
              ],
            ),
          )
        ],
      ),
    );
  }

  clampVehicle({
    required BuildContext context,
    required String vehicleUid,
    required String violationUid,
  }) {
    final arguments = ClampVehicleScreen(
      vehicleUid: vehicleUid,
      violationUid: violationUid,
    );
    Navigator.pushNamed(
      context,
      ClampVehicleScreen.route,
      arguments: arguments,
    );
  }

  towVehicle({
    required BuildContext context,
    required String vehicleUid,
    required String violationUid,
  }) {
    final arguments = TowVehicleScreen(
      vehicleUid: vehicleUid,
      violationUid: violationUid,
    );
    Navigator.pushNamed(context, TowVehicleScreen.route, arguments: arguments);
  }
}

class AddMoreViolationImages extends ConsumerWidget {
  final String violationUid;
  const AddMoreViolationImages({required this.violationUid, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen(
      updateViolationProvider,
      (previous, next) {
        if (next is UpdateViolationSuccess) {
          ref
              .read(parkingViolationNotifierProvider.notifier)
              .getViolationDetails(violationUid);
        }
      },
    );

    final state = ref.watch(updateViolationProvider);
    final notifier = ref.read(updateViolationProvider.notifier);

    if (state is UpdateViolationLoading) {
      return const Center(
        child: CustomGradientSpinner(),
      );
    }

    return InkWell(
      onTap: () async {
        final source = await showModalBottomSheet(
          context: context,
          builder: (context) => const ImageSourceSheet(),
        );
        if (source != null) {
          await notifier.addMoreImages(
              source: source, violationUid: violationUid);
        }
      },
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 10.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              'add'.asIconSvg(),
              height: 15.h,
              width: 15.w,
            ),
            Gap(5.w),
            Text(
              tr(context, 'add_more'),
              style: TextStyles.ts10w500c94684E,
            )
          ],
        ),
      ),
    );
  }
}

class AssignClampinButton extends ConsumerWidget {
  final String vehicleUid;
  final String? endTime;
  final String previousRoute;
  const AssignClampinButton(
      {required this.vehicleUid,
      required this.previousRoute,
      this.endTime,
      super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final timerState = ref.watch(nextActionTimerProvider(endTime));
    if (timerState.isTimerFinished) {
      String buttonName = tr(context, 'assign_clamping');
      if (previousRoute == TowVehicleListingScreen.route) {
        buttonName = tr(context, 'assign_towing');
      }
      return SafeArea(
        bottom: true,
        child: Padding(
          padding: EdgeInsets.fromLTRB(
            16.w,
            0,
            16.w,
            Platform.isAndroid ? 30.h : 0,
          ),
          child: ElevatedButton(
            onPressed: () {
              if (previousRoute == ClampVehicleListingScreen.route) {
                final arguments = AssignClampingScreen(
                  previousRoute: previousRoute,
                  vehicleUid: vehicleUid,
                );
                Navigator.pushNamed(context, AssignClampingScreen.route,
                    arguments: arguments);
              }
              if (previousRoute == TowVehicleListingScreen.route) {
                final arguments = AssignTowingScreen(
                  previousRoute: previousRoute,
                  vehicleUid: vehicleUid,
                );
                Navigator.pushNamed(context, AssignTowingScreen.route,
                    arguments: arguments);
              }
            },
            child: Text(buttonName),
          ),
        ),
      );
    }
    return const SizedBox();
  }
}

class SkeletonParkingViolationDetails extends StatelessWidget {
  const SkeletonParkingViolationDetails({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: ListView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(
                width: 1.w,
                color: ColorConstants.colorEAEAEA,
              ),
            ),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              children: [
                Row(
                  children: [
                    Text.rich(
                      TextSpan(
                        text: tr(context, 'id'),
                        style: TextStyles.ts12w400c4D4D4D,
                        children: [
                          TextSpan(
                            text: ' 2586547',
                            style: TextStyles.ts12w700c353535,
                          ),
                        ],
                      ),
                    ),
                    Gap(9.w),
                    SizedBox(
                      height: 15.h,
                      child: VerticalDivider(
                        color: ColorConstants.colorF1F1F1,
                        thickness: 1.w,
                        width: 0,
                      ),
                    ),
                    Gap(9.w),
                    Text(
                      'Parking Violation',
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                    const Spacer(),
                    Container(
                      decoration: BoxDecoration(
                        color: ColorConstants.colorFFF7E2,
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      padding: EdgeInsets.symmetric(
                        horizontal: 10.w,
                        vertical: 2.h,
                      ),
                      child: Text(
                        "Not Settled",
                        style: TextStyles.ts12w500cE8B020,
                      ),
                    ),
                  ],
                ),
                Gap(16.h),
                Divider(
                  color: ColorConstants.colorF1F1F1,
                  thickness: 1.w,
                  height: 0,
                ),
                Gap(16.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      tr(context, 'violation_date_and_time'),
                      style: TextStyles.ts12w400c4D4D4D,
                    ),
                    Row(
                      children: [
                        SvgPicture.asset(
                          'clock'.asIconSvg(),
                          height: 14.h,
                          width: 14.w,
                        ),
                        Gap(5.w),
                        Text(
                          '20-11-2024, 12:00:00',
                          style: TextStyles.ts12w400c4D4D4D,
                        ),
                      ],
                    ),
                  ],
                ),
                Gap(10.h),
                violationTile(
                  title: tr(context, 'fined_amount'),
                  value: '600 SAR',
                ),
                Gap(10.h),
                violationTile(
                  title: tr(context, 'grace_period'),
                  value: '7 min',
                ),
                Gap(10.h),
                violationTile(
                  title: tr(context, 'violation_reported_by'),
                  value: 'Suhaib Shawish',
                ),
                Gap(10.h),
                NextActionTimer(endTime: '2025-01-20T18:39:19.214443+05:30'),
              ],
            ),
          ),
          Gap(24.h),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
              color: ColorConstants.colorFFFFFF,
              boxShadow: [
                BoxShadow(
                  color: ColorConstants.color000000.withValues(alpha: 0.1),
                  blurRadius: 20.0,
                  spreadRadius: 0,
                  offset: Offset(0, 0),
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    tr(context, 'violation_description'),
                    style: TextStyles.ts18w600c353535,
                  ),
                  Gap(6.h),
                  Text(
                    "The car is parked incorrectly, with its wheels\ncrossing the parking lines, taking up space\nand affecting nearby spots.",
                    style: TextStyles.ts14w500c959595,
                  ),
                  Gap(8.h),
                ],
              ),
            ),
          ),
          Gap(24.h),
          Text(
            tr(context, 'violation_images'),
            style: TextStyles.ts18w600c353535,
          ),
          Gap(9.h),
          Row(
            children: [
              SizedBox(
                height: 153,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10.r),
                  child: Image.asset(
                    "violation-sample".asImagePng(),
                    height: 154.h,
                    width: (1.sw - 44.w) / 2,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Gap(11.w),
              SizedBox(
                height: 153,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10.r),
                  child: Image.asset(
                    "violation-sample".asImagePng(),
                    height: 154.h,
                    width: (1.sw - 44.w) / 2,
                    fit: BoxFit.cover,
                  ),
                ),
              )
            ],
          ),
          Gap(10.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                'add'.asIconSvg(),
                height: 15.h,
                width: 15.w,
              ),
              Gap(5.w),
              Text(
                tr(context, 'add_more'),
                style: TextStyles.ts10w500c94684E,
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget violationTile({required String title, required String value}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: TextStyles.ts12w400c4D4D4D,
        ),
        Text(
          value,
          style: TextStyles.ts12w400c4D4D4D,
        )
      ],
    );
  }
}
