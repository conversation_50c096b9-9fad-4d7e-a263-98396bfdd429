import 'dart:io';

import 'package:albalad_operator_app/features/violation/models/violation_type.dart';
import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:albalad_operator_app/features/violation/widgets/initial_image_upload_card.dart';
import 'package:albalad_operator_app/features/violation/widgets/upload_image_card.dart';
import 'package:albalad_operator_app/features/violation/widgets/violation_type_dropdown.dart';
import 'package:albalad_operator_app/features/violation/violation_type.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/custom_text_form_field.dart';
import 'package:albalad_operator_app/shared/widgets/image_source_sheet.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';

class AssignViolationScreen extends HookConsumerWidget {
  static const route = '/assign_violation';
  final VIOLATIONTYPE violationtype;
  final String vehicleUid;
  final String? previousRoute;
  const AssignViolationScreen({
    required this.vehicleUid,
    this.violationtype = VIOLATIONTYPE.parking,
    this.previousRoute,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final selectedType = useState<ViolationType?>(null);
    final descriptionController = useTextEditingController();
    final showImageValidation = useState(false);
    final formState = ref.watch(assignViolationNotifierProvider);
    final formNotifier = ref.read(assignViolationNotifierProvider.notifier);

    return Scaffold(
      appBar: InnerAppBar(
        title: Text(tr(context, 'assignNewViolation')),
      ),
      body: Form(
        key: formKey,
        child: ListView(
          padding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 30.h,
          ),
          children: [
            ViolationTypeDropdown(
              violationtype: violationtype,
              value: selectedType.value,
              onChanged: (p0) => selectedType.value = p0,
            ),
            Gap(24.h),
            CustomTextFormField(
              controller: descriptionController,
              labelText: tr(context, 'violation_description_star'),
              maxLines: 5,
              hintText: tr(context, 'description_here'),
              autovalidateMode: AutovalidateMode.onUserInteraction,
              textCapitalization: TextCapitalization.sentences,
              validator: (p0) {
                if (p0 == null || p0.isEmpty) {
                  return tr(context, 'please_enter_violation_description');
                }
                return null;
              },
            ),
            Gap(24.h),
            Text(
              tr(context, 'upload_image_star'),
              style: TextStyles.ts12w400c505050,
            ),
            Gap(8.h),
            if (formState.uploadedImages.isEmpty)
              InitialImageUploadCard(
                onTap: () async {
                  ImageSource? source = await showModalBottomSheet(
                    context: context,
                    builder: (context) => const ImageSourceSheet(),
                  );
                  if (source != null) {
                    await formNotifier.uploadImages(source: source);
                  }
                },
                error: showImageValidation.value,
              )
            else
              Wrap(
                spacing: 12.w,
                runSpacing: 12.h,
                children: [
                  ...formState.uploadedImages.map(
                    (e) {
                      return Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(10.r),
                            child: Image.file(
                              File(e.path),
                              height: 154.h,
                              width: (1.sw - 44.w) / 2,
                              fit: BoxFit.cover,
                            ),
                          ),
                          Positioned(
                            right: 8,
                            top: 8,
                            child: InkWell(
                              onTap: () => formNotifier.removeImage(e),
                              child: Container(
                                width: 24.w,
                                height: 24.h,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: ColorConstants.colorF26464,
                                ),
                                alignment: Alignment.center,
                                child: Icon(
                                  Icons.delete_rounded,
                                  size: 18,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          )
                        ],
                      );
                    },
                  ),
                  UploadImageCard(
                    onTap: () async {
                      ImageSource? source = await showModalBottomSheet(
                        context: context,
                        builder: (context) => const ImageSourceSheet(),
                      );
                      if (source != null) {
                        await formNotifier.uploadImages(source: source);
                      }
                    },
                  ),
                ],
              ),
            Gap(5.h),
            if (showImageValidation.value && formState.uploadedImages.isEmpty)
              Text(
                tr(context, 'please_upload_image'),
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
          ],
        ),
      ),
      bottomNavigationBar: SafeArea(
        bottom: true,
        child: Padding(
          padding: EdgeInsets.fromLTRB(
            16.w,
            0,
            16.w,
            Platform.isAndroid ? 30.h : 0,
          ),
          child: formState.isSubmitting
              ? SizedBox(
                  height: kBottomNavigationBarHeight,
                  child: const Center(
                    child: CustomGradientSpinner(),
                  ),
                )
              : ElevatedButton(
                  onPressed: () {
                    if (formState.uploadedImages.isEmpty) {
                      showImageValidation.value = true;
                    } else {
                      showImageValidation.value = false;
                    }
                    if (formKey.currentState?.validate() == true &&
                        formState.uploadedImages.isNotEmpty) {
                      formNotifier.submit(
                        vehicleUid: vehicleUid,
                        violationTypeUid: selectedType.value?.uid ?? '',
                        description: descriptionController.text,
                        context: context,
                        previousRoute: previousRoute,
                      );
                    }
                  },
                  child: Text(tr(context, 'submit')),
                ),
        ),
      ),
    );
  }
}
