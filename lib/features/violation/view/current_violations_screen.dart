import 'package:albalad_operator_app/features/home/<USER>/home_search_field.dart';
import 'package:albalad_operator_app/features/number_plate_scanner/view/number_plate_scanner.dart';
import 'package:albalad_operator_app/features/search/provider/search_provider.dart';
import 'package:albalad_operator_app/features/search/view/search_screen.dart';
import 'package:albalad_operator_app/features/violation/provider/current_violation_listing_provider.dart';
import 'package:albalad_operator_app/features/violation/widgets/skeleton_current_violation_listing.dart';
import 'package:albalad_operator_app/features/violation/widgets/violation_card.dart';
import 'package:albalad_operator_app/features/violation/widgets/violation_type_selection.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:albalad_operator_app/shared/widgets/no_violation_widget.dart';
import 'package:albalad_operator_app/shared/widgets/smart_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CurrentViolationsScreen extends HookConsumerWidget {
  static const route = '/current-violations-listing';
  const CurrentViolationsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedType = useState('all');
    final notifier = ref.read(currentVioaltionListingNotifierProvider.notifier);
    final state = ref.watch(currentVioaltionListingNotifierProvider);

    // Trigger the API call when the screen is first built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (state.items.isEmpty) {
        notifier.fetchCurrentViolations();
      }
    });
    return SmartScaffold(
      isInternetAvailable: state.isConnectionError == false,
      retryConnection: () {
        notifier.reset();
        if (selectedType.value == 'all') {
          notifier.fetchCurrentViolations();
        } else {
          notifier.fetchCurrentViolations(
            violationType: selectedType.value,
          );
        }
      },
      appBar: InnerAppBar(
        title: Text(tr(context, 'currentViolationsTitle')),
      ),
      body: NotificationListener<ScrollNotification>(
        onNotification: (scrollNotification) {
          if (scrollNotification is ScrollEndNotification &&
              scrollNotification.metrics.extentAfter == 0 &&
              state.isLoading == false) {
            notifier.fetchCurrentViolations();
          }
          return false;
        },
        child: RefreshIndicator(
          onRefresh: () async {
            notifier.reset();
            if (selectedType.value == 'all') {
              return notifier.fetchCurrentViolations();
            }
            return notifier.fetchCurrentViolations(
              violationType: selectedType.value,
            );
          },
          child: ListView(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            children: [
              Gap(30.h),
              HomeSearchField(
                hintText: tr(context, 'searchVehicleNumber'),
                readOnly: true,
                onPress: () {
                  final notifier = ref.read(searchNotifierProvider.notifier);
                  notifier.reset();
                  final arguments = SearchScreen(previousRoute: route);
                  Navigator.pushNamed(context, SearchScreen.route,
                      arguments: arguments);
                },
                onTapSuffix: () {
                  final arguments = NumberPlateScanner(previousRoute: route);
                  Navigator.of(context).pushNamed(
                    NumberPlateScanner.route,
                    arguments: arguments,
                  );
                },
              ),
              Gap(12.h),
              ViolationTypeSelection(
                value: selectedType.value,
                onChanged: (p0) {
                  if (selectedType.value == p0) {
                    return;
                  }
                  selectedType.value = p0;
                  notifier.reset();
                  if (p0 == 'all') {
                    notifier.fetchCurrentViolations();
                    return;
                  }
                  notifier.fetchCurrentViolations(violationType: p0);
                },
              ),
              Gap(19.h),
              Divider(
                height: 0,
                color: ColorConstants.colorF1F1F1,
                thickness: 1,
              ),
              Gap(24.h),
              Text(
                tr(context, 'current_violations_list'),
                style: TextStyles.ts18w600c353535,
              ),
              Gap(8.h),
              if (state.items.isEmpty && !state.hasMore) ...[
                Gap(0.1.sh),
                const NoViolationWidget(),
              ] else if (state.items.isEmpty && state.isLoading) ...[
                const SkeletonCurrentViolationListing(),
              ] else
                ListView.separated(
                  shrinkWrap: true,
                  physics: const PageScrollPhysics(),
                  itemBuilder: (context, index) {
                    if (index == state.items.length) {
                      return const Center(child: CustomGradientSpinner());
                    }
                    final item = state.items[index];
                    return ViolationCard(
                      violation: item,
                      previousRoute: CurrentViolationsScreen.route,
                    );
                  },
                  separatorBuilder: (context, index) => SizedBox(height: 24.h),
                  itemCount: state.items.length + (state.hasMore ? 1 : 0),
                ),
              Gap(25.h),
            ],
          ),
        ),
      ),
    );
  }
}
