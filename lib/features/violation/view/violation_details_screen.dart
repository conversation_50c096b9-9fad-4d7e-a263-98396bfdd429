import 'dart:io';

import 'package:albalad_operator_app/features/current_parked_vehicles/widgets/vehicle_info_chip.dart';
import 'package:albalad_operator_app/features/image_preview_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/features/violation/provider/next_action_timer_provider.dart';
import 'package:albalad_operator_app/features/violation/provider/violation_provider.dart';
import 'package:albalad_operator_app/features/violation/provider/violation_state.dart';
import 'package:albalad_operator_app/features/violation/widgets/settle_violation_sheet.dart';
import 'package:albalad_operator_app/features/violation/widgets/skeleton_violation_details.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/models/violation_details.dart';
import 'package:albalad_operator_app/shared/widgets/currency_icon.dart';
import 'package:albalad_operator_app/shared/widgets/inner_app_bar.dart';
import 'package:albalad_operator_app/shared/widgets/smart_scaffold.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ViolationDetailsScreen extends HookConsumerWidget {
  static const route = '/violation_details';
  final String violationUid;
  final String? vehicleUid;
  final String previousRoute;
  const ViolationDetailsScreen({
    required this.violationUid,
    required this.previousRoute,
    this.vehicleUid,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(violationNotifierProvider);
    final notifier = ref.read(violationNotifierProvider.notifier);

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifier.getViolationDetails(violationUid);
      });
      return null;
    }, []);

    return SmartScaffold(
      isInternetAvailable: !(state is ViolationError && state.isInternetError),
      retryConnection: () => notifier.getViolationDetails(violationUid),
      appBar: InnerAppBar(
        title: Text(tr(context, 'violation_details')),
      ),
      body: RefreshIndicator(
        onRefresh: () => notifier.getViolationDetails(violationUid),
        child: Builder(builder: (context) {
          if (state is ViolationLoading) {
            return SkeletonViolationDetails();
          }
          if (state is ViolationError) {
            return Center(
              child: Text(
                state.message,
                style: TextStyles.ts14w400c4D4D4D,
                textAlign: TextAlign.center,
              ),
            );
          }
          if (state is ViolationSuccess) {
            final violation = state.violation;
            final parkingViolationImages = violation.violationImage ?? [];
            final clampedImages = violation.clampedImages ?? [];
            final towedImages = violation.towedImages ?? [];
            final violationImages = [
              ...parkingViolationImages,
              ...clampedImages,
              ...towedImages
            ];
            return ListView(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16.r),
                    border: Border.all(
                      width: 1.w,
                      color: ColorConstants.colorEAEAEA,
                    ),
                  ),
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.h),
                        child: Row(
                          children: [
                            Text.rich(
                              TextSpan(
                                text: tr(context, 'id'),
                                style: TextStyles.ts12w400c4D4D4D,
                                children: [
                                  TextSpan(
                                    text: ' ${violation.violationId ?? ''}',
                                    style: TextStyles.ts12w700c353535,
                                  ),
                                ],
                              ),
                            ),
                            Gap(9.w),
                            SizedBox(
                              height: 15.h,
                              child: VerticalDivider(
                                color: ColorConstants.colorF1F1F1,
                                thickness: 1.w,
                                width: 0,
                              ),
                            ),
                            Gap(9.w),
                            Expanded(
                              child: Text(
                                violation.violationType ?? '',
                                style: TextStyles.ts12w400c4D4D4D,
                              ),
                            ),
                            Gap(9.w),
                            Container(
                              decoration: BoxDecoration(
                                color: ColorConstants.colorFFF7E2,
                                borderRadius: BorderRadius.circular(20.r),
                              ),
                              padding: EdgeInsets.symmetric(
                                horizontal: 10.w,
                                vertical: 2.h,
                              ),
                              child: Text(
                                violation.status ?? '',
                                style: TextStyles.ts12w600cE8B020,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Gap(16.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.h),
                        child: Divider(
                          color: ColorConstants.colorF1F1F1,
                          thickness: 1.w,
                          height: 0,
                        ),
                      ),
                      Gap(16.h),
                      if (previousRoute != VehicleDetailsScreen.route) ...[
                        ViolationVehicleInfo(violation: violation),
                        Gap(16.h),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.h),
                          child: Divider(
                            color: ColorConstants.colorF1F1F1,
                            thickness: 1.w,
                            height: 0,
                          ),
                        ),
                        Gap(16.h),
                      ],
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.h),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              tr(context, 'violation_date_and_time'),
                              style: TextStyles.ts12w400c4D4D4D,
                            ),
                            Row(
                              children: [
                                SvgPicture.asset(
                                  'clock'.asIconSvg(),
                                  height: 14.h,
                                  width: 14.w,
                                ),
                                Gap(5.w),
                                Text(
                                  violation.violationDateTime ?? '',
                                  style: TextStyles.ts12w400c4D4D4D,
                                  textDirection: TextDirection.ltr,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Gap(10.h),
                      if (violation.fineAmount != null &&
                          violation.fineAmount != 0) ...[
                        ...[
                          violationTile(
                            currencyEnabled: true,
                            title: tr(context, 'fined_amount'),
                            value: '${violation.fineAmount} ',
                          ),
                          Gap(10.h),
                          violationTile(
                            currencyEnabled: true,
                            title: tr(context, 'vat'),
                            value: '${violation.vatAmount} ',
                          ),
                          Gap(10.h),
                          Container(
                            decoration: BoxDecoration(
                              color: ColorConstants.colorF8F8F8,
                              gradient: RadialGradient(
                                colors: [
                                  Colors.white,
                                  Colors.white,
                                  ColorConstants.colorF9F9FF,
                                ],
                              ),
                            ),
                            padding: EdgeInsets.symmetric(
                                vertical: 5.h, horizontal: 16.w),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  tr(context, 'total_amount'),
                                  style: TextStyles.ts12w400c4D4D4D,
                                ),
                                const Spacer(),
                                Text.rich(
                                  TextSpan(
                                    text: '${violation.fineAmountAfterVat}',
                                    style: TextStyles.ts12w700c44322D,
                                    // children: [
                                    //   TextSpan(
                                    //     text: ' ${tr(context, 'sar')}',
                                    //     style: TextStyles.ts12w400c4C4C4C,
                                    //   ),
                                    // ],
                                  ),
                                ),
                                const Gap(2),
                                CurrencyIcon(),
                              ],
                            ),
                          ),
                          Gap(10.h),
                        ],
                      ],
                      if (violation.violationReportedBy != null &&
                          violation.violationReportedBy != '') ...[
                        violationTile(
                          title: tr(context, 'violation_reported_by'),
                          value: violation.violationReportedBy ?? '',
                        ),
                        Gap(10.h),
                      ],
                      if (violation.gracePeriod != null &&
                          violation.gracePeriod != '') ...[
                        violationTile(
                          title: tr(context, 'grace_period'),
                          value: violation.gracePeriod!.toLowerCase(),
                        ),
                        Gap(10.h),
                      ],
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.h),
                        child: NextActionTimer(
                          endTime: violation.gracePeriodEnd,
                          vehicleUid: vehicleUid,
                        ),
                      )
                    ],
                  ),
                ),
                Gap(24.h),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: ColorConstants.colorFFFFFF,
                    boxShadow: [
                      BoxShadow(
                        color:
                            ColorConstants.color000000.withValues(alpha: 0.1),
                        blurRadius: 20.0,
                        spreadRadius: 0,
                        offset: Offset(0, 0),
                      ),
                    ],
                  ),
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        tr(context, 'violation_description'),
                        style: TextStyles.ts18w600c353535,
                      ),
                      Gap(6.h),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            violation.violationDescription ?? '',
                            style: TextStyles.ts14w600c959595,
                          ),
                          if (violation.clampedDescription != null &&
                              violation.clampedDescription != '')
                            Text(
                              '${tr(context, 'clamp')}: ${violation.clampedDescription}',
                              style: TextStyles.ts14w600c959595,
                            ),
                          if (violation.towedDescription != null &&
                              violation.towedDescription != '')
                            Text(
                              '${tr(context, 'tow')}: ${violation.towedDescription}',
                              style: TextStyles.ts14w600c959595,
                            ),
                        ],
                      ),
                      Gap(8.h),
                    ],
                  ),
                ),
                Gap(30.h),
                Text(
                  tr(context, 'violation_images'),
                  style: TextStyles.ts18w600c353535,
                ),
                Gap(16.h),
                Wrap(
                  spacing: 12.w,
                  runSpacing: 12.h,
                  children: violationImages.map(
                    (e) {
                      return InkWell(
                        onTap: () {
                          int initialIndex = violationImages.indexOf(e);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ImagePreviewScreen(
                                violationImages: violationImages,
                                pageController: PageController(
                                  initialPage: initialIndex,
                                ),
                                initialIndex: initialIndex,
                              ),
                            ),
                          );
                        },
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: CachedNetworkImage(
                            imageUrl: e.image ?? '',
                            height: 154.h,
                            width: (1.sw - 44.w) / 2,
                            fit: BoxFit.cover,
                            errorWidget: (context, url, error) {
                              return Image.asset(
                                "violation-sample".asImagePng(),
                                height: 154.h,
                                width: (1.sw - 44.w) / 2,
                                fit: BoxFit.cover,
                              );
                            },
                          ),
                        ),
                      );
                    },
                  ).toList(),
                )
              ],
            );
          }
          return const SizedBox();
        }),
      ),
      bottomNavigationBar: state is ViolationSuccess
          ? ViolationBottomNavigationBar(
              endTime: state.violation.gracePeriodEnd,
              violationUid: violationUid,
              isFined: state.violation.fineAmount != null &&
                  state.violation.fineAmount != 0,
              vehicleUid: vehicleUid,
              previousRoute: previousRoute,
            )
          : null,
    );
  }

  Widget violationTile({
    required String title,
    required String value,
    bool currencyEnabled = false,
    TextDirection? textDirection,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.h),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: TextStyles.ts12w400c4D4D4D,
            ),
          ),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  value,
                  style: TextStyles.ts12w400c4D4D4D,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.end,
                  textDirection: textDirection,
                ),
                if (currencyEnabled) CurrencyIcon(height: 9),
              ],
            ),
          )
        ],
      ),
    );
  }
}

class NextActionTimer extends ConsumerWidget {
  final String? endTime;
  final String? vehicleUid;
  const NextActionTimer({required this.endTime, this.vehicleUid, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final timerState = ref.watch(nextActionTimerProvider(endTime));
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          tr(context, 'next_action_in'),
          style: TextStyles.ts12w400c4D4D4D,
        ),
        Text(
          timerState.formattedTime,
          style: TextStyles.ts12w700c4D4D4D,
        )
      ],
    );
  }
}

class ViolationBottomNavigationBar extends ConsumerWidget {
  final String? endTime;
  final String violationUid;
  final bool isFined;
  final String? vehicleUid;
  final String previousRoute;
  const ViolationBottomNavigationBar({
    required this.endTime,
    required this.violationUid,
    required this.isFined,
    required this.previousRoute,
    this.vehicleUid,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // final timerState = ref.watch(nextActionTimerProvider(endTime));
    // if (timerState.isTimerFinished) {
    return SafeArea(
      bottom: true,
      child: Padding(
        padding: EdgeInsets.fromLTRB(
          16.w,
          0,
          16.w,
          Platform.isAndroid ? 30.h : 0,
        ),
        child: ElevatedButton(
          onPressed: () {
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              builder: (context) => SettleViolationSheet(
                isFined: isFined,
                violationUid: violationUid,
                vehicleUid: vehicleUid,
                previousRoute: previousRoute,
              ),
            );
          },
          child: Text(tr(context, 'settle_violation')),
        ),
      ),
    );
    // }
    // return const SizedBox();
  }
}

class ViolationVehicleInfo extends StatelessWidget {
  final ViolationDetails violation;
  const ViolationVehicleInfo({required this.violation, super.key});

  @override
  Widget build(BuildContext context) {
    String? vehicleImage = violation.vehicleImage?.firstOrNull?.image;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                height: 35.h,
                width: 35.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.r),
                  color: ColorConstants.colorF1EFE9,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8.r),
                  child: CachedNetworkImage(
                    imageUrl: vehicleImage ?? '',
                    memCacheWidth: 100,
                    fit: BoxFit.fill,
                    errorWidget: (context, url, error) =>
                        Image.asset('car'.asImagePng()),
                  ),
                ),
              ),
              Gap(10.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    violation.vehicleNumber ?? '',
                    style: TextStyles.ts14w600c181818,
                  ),
                  Text(
                    violation.vehicleName ?? '',
                    style: TextStyles.ts10w400cA1A09B,
                  ),
                ],
              ),
            ],
          ),
          Gap(16.h),
          Row(
            spacing: 8.w,
            children: [
              if (violation.makeYear != null)
                VehicleInfoChip(
                  title: violation.makeYear.toString(),
                ),
              if (violation.vehicleType != null)
                VehicleInfoChip(
                  title: violation.vehicleType ?? '',
                ),
              if (violation.numberPlateType != null)
                VehicleInfoChip(
                  title: violation.numberPlateType ?? '',
                ),
            ],
          ),
        ],
      ),
    );
  }
}
