import 'package:albalad_operator_app/features/violation/provider/assign_towing_state.dart';
import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'towing_provider.g.dart';

@riverpod
class AssignTowingNotifier extends _$AssignTowingNotifier {
  @override
  AssignTowingState build() => AssignTowingInitial();

  final Dio _dio = DioClient().dio;

  Future<void> submit({
    required String vehicleUid,
    required String violationTypeUid,
    required String clampingOperatorUid,
  }) async {
    state = AssignTowingLoading();
    try {
      final response = await _dio.post(
        ApiConstants.assignViolation,
        options: Options(
          headers: await ApiConstants.authHeaders(),
        ),
        data: {
          'vehicle_uid': vehicleUid,
          'violation_type_uid': violationTypeUid,
          'assigned_operator': clampingOperatorUid,
        },
      );

      if (response.statusCode == 400) {
        Map<String, dynamic>? errors = response.data['errors'];
        String? message = response.data['message'];
        if (errors != null && errors.isNotEmpty) {
          state = AssignTowingFailure(errors.values.firstOrNull.toString());
          return;
        }
        if (message != null) {
          state = AssignTowingFailure(message);
          return;
        }
      }
      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          state = AssignTowingSuccess();
          return;
        }
      }
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = AssignTowingFailure(
          appLocalization.translate('networkError'),
        );
        return;
      }
      state = AssignTowingFailure(e.toString());
    }
  }
}
