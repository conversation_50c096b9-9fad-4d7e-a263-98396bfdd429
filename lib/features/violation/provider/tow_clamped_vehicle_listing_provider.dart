import 'dart:io';

import 'package:albalad_operator_app/features/violation/models/tow_clamped_violation_model.dart';
import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'tow_clamped_vehicle_listing_provider.g.dart';

class TowClampedVehicleListingState {
  final List<TowClampedViolationModel> items;
  final bool isLoading;
  final bool hasMore;
  final bool isConnectionError;
  final String? error;

  TowClampedVehicleListingState({
    required this.items,
    required this.isLoading,
    required this.hasMore,
    this.isConnectionError = false,
    this.error,
  });

  TowClampedVehicleListingState copyWith({
    List<TowClampedViolationModel>? items,
    bool? isLoading,
    bool? hasMore,
    bool? isConnectionError,
    String? error,
  }) {
    return TowClampedVehicleListingState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      isConnectionError: isConnectionError ?? this.isConnectionError,
      error: error ?? this.error,
    );
  }
}

@riverpod
class TowClampedVehicleListingNotifier
    extends _$TowClampedVehicleListingNotifier {
  @override
  TowClampedVehicleListingState build() {
    return TowClampedVehicleListingState(
      items: [],
      isLoading: false,
      hasMore: true,
    );
  }

  int _currentPage = 1;

  Future<void> fetchClampedVehicleList() async {
    if (state.isLoading || !state.hasMore) return;
    state =
        state.copyWith(isLoading: true, error: null, isConnectionError: false);

    try {
      final violationServices = ref.read(violationServicesProvider);
      final response = await violationServices.fetchTowClampedVehicleList(
        page: _currentPage,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          final List<dynamic> records = data['records'] ?? [];
          final List<TowClampedViolationModel> clampedVehicles =
              records.map((e) => TowClampedViolationModel.fromJson(e)).toList();
          state = state.copyWith(
            items: [...state.items, ...clampedVehicles],
            isLoading: false,
            hasMore: data['pagination']?['has_next'] == true,
          );
          _currentPage++;
        } else {
          state = state.copyWith(
            isLoading: false,
            hasMore: false,
          );
        }
      } else {
        state = state.copyWith(
          isLoading: false,
          hasMore: false,
        );
      }
    } on SocketException {
      _handleError(null, isConnectionError: true);
    } catch (e, stackTrace) {
      debugPrint('Error in fetchParkingViolationList: $e');
      debugPrint('StackTrace: $stackTrace');
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        _handleError(null, isConnectionError: true);
        return;
      }
      _handleError(e.toString());
    }
  }

  _handleError(String? error, {bool isConnectionError = false}) {
    state = state.copyWith(
      isLoading: false,
      hasMore: false,
      error: error,
      isConnectionError: isConnectionError,
    );
  }

  void reset() {
    _currentPage = 1;
    state = TowClampedVehicleListingState(
      items: [],
      isLoading: false,
      hasMore: true,
    );
  }
}
