// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vioaltions_and_notices_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$violationsAndNoticesNotifierHash() =>
    r'e677b4f92cce074401d8dd4ac8ef0306bb07c33a';

/// See also [ViolationsAndNoticesNotifier].
@ProviderFor(ViolationsAndNoticesNotifier)
final violationsAndNoticesNotifierProvider = AutoDisposeNotifierProvider<
    ViolationsAndNoticesNotifier, ViolationsAndNoticesState>.internal(
  ViolationsAndNoticesNotifier.new,
  name: r'violationsAndNoticesNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$violationsAndNoticesNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ViolationsAndNoticesNotifier
    = AutoDisposeNotifier<ViolationsAndNoticesState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
