import 'package:albalad_operator_app/features/home/<USER>/home_provider.dart';
import 'package:albalad_operator_app/features/success_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/provider/vehicle_details_provider.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/features/violation/models/enforcer.dart';
import 'package:albalad_operator_app/features/violation/models/payment_method.dart';
import 'package:albalad_operator_app/features/violation/models/settlement_type.dart';
import 'package:albalad_operator_app/features/violation/models/violation_type.dart';
import 'package:albalad_operator_app/features/violation/provider/vioaltions_and_notices_provider.dart';
import 'package:albalad_operator_app/features/violation/provider/violation_form_state.dart';
import 'package:albalad_operator_app/features/violation/view/violations_and_notices_screen.dart';
import 'package:albalad_operator_app/features/violation/violation_type.dart';
import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/helper/dialog_helper.dart';
import 'package:albalad_operator_app/shared/services/violation_services.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'providers.g.dart';

// Define a Riverpod provider for the ViolationServices class
final violationServicesProvider = Provider<ViolationServices>((ref) {
  return ViolationServices();
});

// Define a Riverpod FutureProvider to fetch violation types
final violationTypesProvider = FutureProvider.family
    .autoDispose<List<ViolationType>, VIOLATIONTYPE>((ref, type) async {
  final violationServices = ref.read(violationServicesProvider);
  return await violationServices.fetchViolationTypes(type);
});

// Define a Riverpod FutureProvider to fetch enforcers
final enforcersProvider = FutureProvider.family
    .autoDispose<List<Enforcer>, String>((ref, type) async {
  final violationServices = ref.read(violationServicesProvider);
  return await violationServices.fetchOperators(type);
});

final settlementTypeProvider =
    FutureProvider.autoDispose<List<SettlementType>>((ref) async {
  final violationServices = ref.read(violationServicesProvider);
  return await violationServices.fetchSettlementTypes();
});

final paymentMethodProvider =
    FutureProvider.autoDispose<List<PaymentMethod>>((ref) async {
  final violationServices = ref.read(violationServicesProvider);
  return await violationServices.fetchPaymentMethods();
});

@riverpod
class AssignViolationNotifier extends _$AssignViolationNotifier {
  @override
  ViolationFormState build() => ViolationFormState();

  Future<void> uploadImages({required ImageSource source}) async {
    final picker = ImagePicker();
    XFile? pickedImage = await picker.pickImage(
      source: source,
      maxWidth: 1000,
    );
    if (pickedImage != null) {
      final croppedImage = await cropImage(image: pickedImage);
      if (croppedImage != null) {
        pickedImage = XFile(croppedImage.path);
      }
      state = state
          .copyWith(uploadedImages: [...state.uploadedImages, pickedImage]);
    }
  }

  Future<CroppedFile?> cropImage({required XFile image}) async {
    return ImageCropper().cropImage(
      sourcePath: image.path,
      uiSettings: [
        AndroidUiSettings(
          statusBarColor: Colors.black,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: false,
        ),
        IOSUiSettings(),
      ],
    );
  }

  removeImage(XFile xFile) {
    List<XFile> images = state.uploadedImages;
    images.remove(xFile);
    state = state.copyWith(uploadedImages: [...images]);
  }

  final Dio _dio = DioClient().dio;

  Future<void> submit({
    required String vehicleUid,
    required String violationTypeUid,
    required String description,
    String? previousRoute,
    BuildContext? context,
  }) async {
    state = state.copyWith(isSubmitting: true, success: false);
    try {
      // Create a list of MultipartFile for each image
      List<MultipartFile> multipartFiles = [];
      for (var file in state.uploadedImages) {
        String fileName = file.path.split('/').last;

        multipartFiles.add(
          await MultipartFile.fromFile(
            file.path,
            filename: fileName,
          ),
        );
      }

      final response = await _dio.post(
        ApiConstants.assignViolation,
        options: Options(
          headers: await ApiConstants.authFormDataHeaders(),
        ),
        data: FormData.fromMap(
          {
            'vehicle_uid': vehicleUid,
            'violation_type_uid': violationTypeUid,
            'description': description,
            'violation_images': multipartFiles,
          },
        ),
      );
      if (response.statusCode == 400) {
        Map<String, dynamic> errors = response.data['errors'];
        if (context != null && errors.isNotEmpty && context.mounted) {
          DialogHelper.showErrorDialog(
            context: context,
            message: errors.values.firstOrNull.toString(),
          );
        }
        state = state.copyWith(isSubmitting: false, success: false);
        return;
      }
      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          await _updateData(
            previousRoute: previousRoute,
            vehicleUid: vehicleUid,
            ref: ref,
          );

          state = state.copyWith(isSubmitting: false, success: true);

          if (context != null && context.mounted) {
            _showSuccessScreen(context);
          }
          return;
        }
      }
      state = state.copyWith(isSubmitting: false, success: false);
    } catch (e) {
      state = state.copyWith(isSubmitting: false, success: false);
    }
  }

  _updateData({String? previousRoute, String? vehicleUid, ref}) async {
    if (previousRoute == ViolationsAndNoticesScreen.route) {
      final notifier = ref.read(violationsAndNoticesNotifierProvider.notifier);
      notifier.reset();
      notifier.fetchtViolationsAndNoticesVehicleList();
    }
    await ref
        .read(vehicleDetailsNotifierProvider.notifier)
        .getVehicleDetails(uid: vehicleUid);
    ref.invalidate(homeSummaryProvider);
  }

  _showSuccessScreen(BuildContext context) {
    final args = SuccessScreen(
      title: tr(context, 'violation_assigned'),
      message: tr(context, 'violation_assigned_description'),
      onPressed: (ctxt) {
        Navigator.popUntil(
          ctxt,
          ModalRoute.withName(VehicleDetailsScreen.route),
        );
      },
    );
    Navigator.pushReplacementNamed(
      context,
      SuccessScreen.route,
      arguments: args,
    );
  }
}
