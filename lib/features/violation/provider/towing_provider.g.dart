// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'towing_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$assignTowingNotifierHash() =>
    r'3ca9267625e956902f5d9c7bd9669ab16062dfe3';

/// See also [AssignTowingNotifier].
@ProviderFor(AssignTowingNotifier)
final assignTowingNotifierProvider = AutoDisposeNotifierProvider<
    AssignTowingNotifier, AssignTowingState>.internal(
  AssignTowingNotifier.new,
  name: r'assignTowingNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$assignTowingNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AssignTowingNotifier = AutoDisposeNotifier<AssignTowingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
