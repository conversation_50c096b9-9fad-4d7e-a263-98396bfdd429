// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'parking_violation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$parkingViolationNotifierHash() =>
    r'0bd4ccddb5e4b2d2420f7c7119a114df8dea0cff';

/// See also [ParkingViolationNotifier].
@ProviderFor(ParkingViolationNotifier)
final parkingViolationNotifierProvider = AutoDisposeNotifierProvider<
    ParkingViolationNotifier, ParkingViolationState>.internal(
  ParkingViolationNotifier.new,
  name: r'parkingViolationNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$parkingViolationNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ParkingViolationNotifier = AutoDisposeNotifier<ParkingViolationState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
