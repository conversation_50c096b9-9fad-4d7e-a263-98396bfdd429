import 'package:albalad_operator_app/features/violation/models/parking_violation_detail.dart';

abstract class ParkingViolationState {}

class ParkingViolationInitial extends ParkingViolationState {}

class ParkingViolationLoading extends ParkingViolationState {}

class ParkingViolationSuccess extends ParkingViolationState {
  final ParkingViolationDetail violation;
  ParkingViolationSuccess(this.violation);
}

class ParkingViolationError extends ParkingViolationState {
  final String message;
  final bool isConnectionError;
  ParkingViolationError(this.message, {this.isConnectionError = false});
}
