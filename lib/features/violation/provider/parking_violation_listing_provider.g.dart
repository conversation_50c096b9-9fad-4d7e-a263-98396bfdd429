// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'parking_violation_listing_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$parkingViolationListingNotifierHash() =>
    r'42205dee61b87ffd7ef82c305d83ad224ac73e7c';

/// See also [ParkingViolationListingNotifier].
@ProviderFor(ParkingViolationListingNotifier)
final parkingViolationListingNotifierProvider = AutoDisposeNotifierProvider<
    ParkingViolationListingNotifier, ParkingViolationListingState>.internal(
  ParkingViolationListingNotifier.new,
  name: r'parkingViolationListingNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$parkingViolationListingNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ParkingViolationListingNotifier
    = AutoDisposeNotifier<ParkingViolationListingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
