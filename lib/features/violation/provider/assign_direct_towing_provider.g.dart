// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'assign_direct_towing_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$assignDirectTowingNotifierHash() =>
    r'1a636bbff6c4ab80c6381f8e74a05fcbffbc6ae3';

/// See also [AssignDirectTowingNotifier].
@ProviderFor(AssignDirectTowingNotifier)
final assignDirectTowingNotifierProvider = AutoDisposeNotifierProvider<
    AssignDirectTowingNotifier, AssignDirectTowingState>.internal(
  AssignDirectTowingNotifier.new,
  name: r'assignDirectTowingNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$assignDirectTowingNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AssignDirectTowingNotifier
    = AutoDisposeNotifier<AssignDirectTowingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
