import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'assign_direct_towing_provider.g.dart';

abstract class AssignDirectTowingState {}

class AssignDirectTowingInitial extends AssignDirectTowingState {}

class AssignDirectTowingLoading extends AssignDirectTowingState {}

class AssignDirectTowingSuccess extends AssignDirectTowingState {}

class AssignDirectTowingFailure extends AssignDirectTowingState {
  final String message;
  AssignDirectTowingFailure(this.message);
}

@riverpod
class AssignDirectTowingNotifier extends _$AssignDirectTowingNotifier {
  @override
  AssignDirectTowingState build() => AssignDirectTowingInitial();

  final Dio _dio = DioClient().dio;

  Future<void> submit({
    required String vehicleUid,
    required String violationTypeUid,
    required String towingOperatorUid,
    required String description,
    required List<String> violationImages,
  }) async {
    state = AssignDirectTowingLoading();
    try {
      FormData formData = FormData.fromMap({
        'vehicle_uid': vehicleUid,
        'violation_type_uid': violationTypeUid,
        'description': description,
        'assigned_operator': towingOperatorUid,
        'violation_images':
            violationImages.map((e) => MultipartFile.fromFileSync(e)).toList(),
      });

      final response = await _dio.post(
        ApiConstants.assignViolation,
        options: Options(
          headers: await ApiConstants.authHeaders(),
        ),
        data: formData,
      );

      if (response.statusCode == 400) {
        Map<String, dynamic>? errors = response.data['errors'];
        String? message = response.data['message'];
        if (errors != null && errors.isNotEmpty) {
          state =
              AssignDirectTowingFailure(errors.values.firstOrNull.toString());
          return;
        }
        if (message != null) {
          state = AssignDirectTowingFailure(message);
          return;
        }
      }
      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          state = AssignDirectTowingSuccess();
          return;
        }
      }
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = AssignDirectTowingFailure(
          appLocalization.translate('networkError'),
        );
        return;
      }
      state = AssignDirectTowingFailure(e.toString());
    }
  }
}
