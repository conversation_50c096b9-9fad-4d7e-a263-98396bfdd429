import 'dart:async';
import 'dart:io';

import 'package:albalad_operator_app/features/violation/provider/current_violaion_listing_state.dart';
import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/models/current_violation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'current_violation_listing_provider.g.dart';

@riverpod
class CurrentVioaltionListingNotifier
    extends _$CurrentVioaltionListingNotifier {
  @override
  CurrentViolationListingState build() {
    return CurrentViolationListingState(
      items: [],
      isLoading: false,
      hasMore: true,
      isConnectionError: false,
      error: null,
    );
  }

  int _currentPage = 1;

  Future<void> fetchCurrentViolations({String? violationType}) async {
    if (state.isLoading || !state.hasMore) return;
    state =
        state.copyWith(isLoading: true, error: null, isConnectionError: false);

    try {
      final violationServices = ref.read(violationServicesProvider);
      final response = await violationServices.fetchCurrentViolations(
        page: _currentPage,
        filterData: violationType,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          final List<dynamic> records = data['records'];
          final List<CurrentViolation> currentViolations =
              records.map((e) => CurrentViolation.fromJson(e)).toList();
          state = state.copyWith(
            items: [...state.items, ...currentViolations],
            isLoading: false,
            hasMore: data['pagination']['has_next'] == true,
          );
          _currentPage++;
        }
      }
    } on SocketException {
      state = state.copyWith(
        isLoading: false,
        hasMore: false,
        error: appLocalization.translate('networkError'),
        isConnectionError: true,
      );
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = state.copyWith(
          isLoading: false,
          hasMore: false,
          error: e.toString(),
          isConnectionError: true,
        );
        return;
      }
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  void reset() {
    _currentPage = 1;
    state = CurrentViolationListingState(
      items: [],
      isLoading: false,
      hasMore: true,
      isConnectionError: false,
    );
  }
}
