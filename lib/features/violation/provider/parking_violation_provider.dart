import 'dart:io';

import 'package:albalad_operator_app/features/violation/provider/parking_violation_state.dart';
import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'parking_violation_provider.g.dart';

@riverpod
class ParkingViolationNotifier extends _$ParkingViolationNotifier {
  @override
  ParkingViolationState build() => ParkingViolationInitial();

  Future<void> getViolationDetails(String violationUid) async {
    state = ParkingViolationLoading();
    try {
      final violationServices = ref.read(violationServicesProvider);
      final violation = await violationServices.fetchParkingViolationDetails(
        violationUid,
      );
      state = ParkingViolationSuccess(violation);
    } on SocketException {
      state = ParkingViolationError(
        appLocalization.translate('networkError'),
        isConnectionError: true,
      );
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = ParkingViolationError(e.toString(), isConnectionError: true);
        return;
      }
      state = ParkingViolationError(e.toString());
    }
  }
}
