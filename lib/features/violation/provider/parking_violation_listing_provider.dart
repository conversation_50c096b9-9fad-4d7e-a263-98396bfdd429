import 'dart:async';
import 'dart:io';

import 'package:albalad_operator_app/features/violation/models/parking_violation_model.dart';
import 'package:albalad_operator_app/features/violation/provider/parking_violation_listing_state.dart';
import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'parking_violation_listing_provider.g.dart';

@riverpod
class ParkingViolationListingNotifier
    extends _$ParkingViolationListingNotifier {
  @override
  ParkingViolationListingState build() {
    return ParkingViolationListingState(
      items: [],
      isLoading: false,
      hasMore: true,
      error: null,
      isConnectionError: false,
    );
  }

  int _currentPage = 1;

  Future<void> fetchParkingViolationList() async {
    if (state.isLoading || !state.hasMore) return;
    state =
        state.copyWith(isLoading: true, error: null, isConnectionError: false);

    try {
      final violationServices = ref.read(violationServicesProvider);
      final response =
          await violationServices.fetchParkingViolationList(page: _currentPage);
      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          final List<dynamic> records = data['records'] ?? [];
          final List<ParkingViolationModel> currentViolations =
              records.map((e) => ParkingViolationModel.fromJson(e)).toList();
          state = state.copyWith(
            items: [...state.items, ...currentViolations],
            isLoading: false,
            hasMore: data['pagination']?['has_next'] == true,
          );
          _currentPage++;
        } else {
          state = state.copyWith(
            isLoading: false,
            hasMore: false,
          );
        }
      } else {
        state = state.copyWith(
          isLoading: false,
          hasMore: false,
        );
      }
    } on SocketException {
      _handleError(appLocalization.translate('networkError'));
    } catch (e, stackTrace) {
      debugPrint('Error in fetchParkingViolationList: $e');
      debugPrint('StackTrace: $stackTrace');
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        _handleError(null, isConnectionError: true);
        return;
      }
      _handleError(e.toString());
    }
  }

  _handleError(String? error, {bool isConnectionError = false}) {
    state = state.copyWith(
        isLoading: false,
        hasMore: false,
        error: error,
        isConnectionError: isConnectionError);
  }

  void reset() {
    _currentPage = 1;
    state = ParkingViolationListingState(
      items: [],
      isLoading: false,
      hasMore: true,
      isConnectionError: false,
    );
  }
}
