import 'package:albalad_operator_app/features/violation/provider/settle_violation_state.dart';
import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'settle_violation_provider.g.dart';

@riverpod
class SettleViolationNotifier extends _$SettleViolationNotifier {
  @override
  SettleViolationState build() => SettleViolationInitial();

  final Dio _dio = DioClient().dio;

  Future<void> submit({
    required String violationUid,
    required String status,
    required String paymentMethodUid,
    required String description,
  }) async {
    state = SettleViolationLoading();
    try {
      Map<String, dynamic> data = {
        'violation_uid': violationUid,
        'status': status,
        'description': description,
      };
      if (paymentMethodUid.isNotEmpty) {
        data['payment_method_uid'] = paymentMethodUid;
      }
      final response = await _dio.post(
        ApiConstants.settleCloseViolation,
        options: Options(
          headers: await ApiConstants.authHeaders(),
        ),
        data: data,
      );

      if (response.statusCode == 200) {
        if (response.data['result'] == 'success') {
          state = SettleViolationSuccess();
          return;
        }
      }

      if (response.statusCode == 400) {
        Map<String, dynamic>? errors = response.data['errors'];
        String? message = response.data['message'];
        if (errors != null && errors.isNotEmpty) {
          state = SettleViolationFailure(errors.values.firstOrNull.toString());
          return;
        }
        if (message != null) {
          state = SettleViolationFailure(message);
          return;
        }
      }
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state =
            SettleViolationFailure(appLocalization.translate('networkError'));
        return;
      }
      state = SettleViolationFailure(e.toString());
    }
  }
}
