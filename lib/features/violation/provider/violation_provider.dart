import 'dart:io';

import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:albalad_operator_app/features/violation/provider/violation_state.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'violation_provider.g.dart';

@riverpod
class ViolationNotifier extends _$ViolationNotifier {
  @override
  ViolationState build() => ViolationInitial();

  /// Fetches violation details for a given violation UID
  ///
  /// This function makes an asynchronous request to fetch details of a specific violation
  /// using the violation's unique identifier. It updates the state throughout the process
  /// to reflect loading, success, or error conditions.
  ///
  /// [violationUid] The unique identifier of the violation to fetch
  Future<void> getViolationDetails(String violationUid) async {
    // Set state to loading to indicate that data is being fetched
    state = ViolationLoading();
    try {
      // Get the violation services instance from the provider
      final violationServices = ref.read(violationServicesProvider);

      // Make the API call to fetch violation details using the provided UID
      final violation = await violationServices.fetchViolationDetails(
        violationUid,
      );

      // Update state with fetched violation data on successful response
      state = ViolationSuccess(violation);
    } on SocketException {
      // Handle network-related errors like no internet connection
      state = ViolationError(appLocalization.translate('networkError'), true);
    } catch (e) {
      // Special handling for specific connection-related errors
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = ViolationError(appLocalization.translate('networkError'), true);
        return;
      }

      // Handle any other unexpected errors by displaying the error message
      state = ViolationError(e.toString(), false);
    }
  }
}
