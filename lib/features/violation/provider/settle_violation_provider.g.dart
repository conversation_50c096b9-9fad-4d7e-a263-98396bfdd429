// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'settle_violation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$settleViolationNotifierHash() =>
    r'98f6339277484c1ff9d6d3513c33c18344e770c6';

/// See also [SettleViolationNotifier].
@ProviderFor(SettleViolationNotifier)
final settleViolationNotifierProvider = AutoDisposeNotifierProvider<
    SettleViolationNotifier, SettleViolationState>.internal(
  SettleViolationNotifier.new,
  name: r'settleViolationNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$settleViolationNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SettleViolationNotifier = AutoDisposeNotifier<SettleViolationState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
