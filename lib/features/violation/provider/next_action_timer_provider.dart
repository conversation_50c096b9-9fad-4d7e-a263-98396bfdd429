import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class NextActionState {
  final String formattedTime;
  final bool isTimerFinished;

  NextActionState({required this.formattedTime, required this.isTimerFinished});
}

class NextActionNotifier extends StateNotifier<NextActionState> {
  NextActionNotifier(String? endTime)
      : super(NextActionState(
            formattedTime: "00:00:00", isTimerFinished: false)) {
    if (endTime != null) {
      _endTime = DateTime.parse(endTime);
      _startTimer();
    } else {
      state = NextActionState(formattedTime: "00:00:00", isTimerFinished: true);
    }
  }

  late DateTime _endTime;
  late Timer? _timer;

  void _startTimer() {
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      final now = DateTime.now();
      final remainingTime = _endTime.difference(now);

      if (remainingTime.isNegative) {
        _timer?.cancel();
        state =
            NextActionState(formattedTime: "00:00:00", isTimerFinished: true);
      } else {
        final hours = remainingTime.inHours.toString().padLeft(2, '0');
        final minutes =
            (remainingTime.inMinutes % 60).toString().padLeft(2, '0');
        final seconds =
            (remainingTime.inSeconds % 60).toString().padLeft(2, '0');
        state = NextActionState(
          formattedTime: "$hours:$minutes:$seconds",
          isTimerFinished: false,
        );
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}

final nextActionTimerProvider =
    StateNotifierProvider.family<NextActionNotifier, NextActionState, String?>(
  (ref, endTime) => NextActionNotifier(endTime),
);
