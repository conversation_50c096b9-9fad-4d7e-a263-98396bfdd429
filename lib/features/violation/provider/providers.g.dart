// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$assignViolationNotifierHash() =>
    r'0c9438ed622499101b8d038d33b6af90aaaec1c9';

/// See also [AssignViolationNotifier].
@ProviderFor(AssignViolationNotifier)
final assignViolationNotifierProvider = AutoDisposeNotifierProvider<
    AssignViolationNotifier, ViolationFormState>.internal(
  AssignViolationNotifier.new,
  name: r'assignViolationNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$assignViolationNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AssignViolationNotifier = AutoDisposeNotifier<ViolationFormState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
