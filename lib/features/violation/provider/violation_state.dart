import 'package:albalad_operator_app/shared/models/violation_details.dart';

abstract class ViolationState {}

class ViolationInitial extends ViolationState {}

class ViolationLoading extends ViolationState {}

class ViolationSuccess extends ViolationState {
  final ViolationDetails violation;
  ViolationSuccess(this.violation);
}

class ViolationError extends ViolationState {
  final String message;
  final bool isInternetError;
  ViolationError(this.message, this.isInternetError);
}
