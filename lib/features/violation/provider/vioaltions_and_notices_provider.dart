import 'dart:async';
import 'dart:io';

import 'package:albalad_operator_app/features/violation/provider/providers.dart';
import 'package:albalad_operator_app/features/violation/provider/violations_and_notices_state.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/models/vehicle_details.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'vioaltions_and_notices_provider.g.dart';

@riverpod
class ViolationsAndNoticesNotifier extends _$ViolationsAndNoticesNotifier {
  @override
  ViolationsAndNoticesState build() {
    return ViolationsAndNoticesState(
      items: [],
      isLoading: false,
      hasMore: true,
      isConnectionError: false,
      error: null,
    );
  }

  int _currentPage = 1;

  Future<void> fetchtViolationsAndNoticesVehicleList() async {
    if (state.isLoading || !state.hasMore) return;
    state =
        state.copyWith(isLoading: true, error: null, isConnectionError: false);

    try {
      final violationServices = ref.read(violationServicesProvider);
      final response = await violationServices
          .fetchtViolationsAndNoticesVehicleList(page: _currentPage);
      if (response.statusCode == 200) {
        final data = response.data;
        if (data['result'] == 'success') {
          final List<dynamic> records = data['records'];
          final List<VehicleDetails> currentViolations =
              records.map((e) => VehicleDetails.fromJson(e)).toList();
          state = state.copyWith(
            items: [...state.items, ...currentViolations],
            isLoading: false,
            hasMore: data['pagination']['has_next'] == true,
          );
          _currentPage++;
        } else {
          state = state.copyWith(
            items: [...state.items],
            isLoading: false,
            hasMore: false,
          );
        }
      } else {
        state = state.copyWith(
          items: [...state.items],
          isLoading: false,
          hasMore: false,
        );
      }
    } on SocketException {
      state = state.copyWith(
        isLoading: false,
        items: [...state.items],
        hasMore: false,
        isConnectionError: true,
        error: appLocalization.translate('networkError'),
      );
    } catch (e) {
      if (e.toString().contains('connection error') ||
          e.toString().contains('connection timeout')) {
        state = state.copyWith(
          isLoading: false,
          hasMore: false,
          isConnectionError: true,
          error: appLocalization.translate('networkError'),
        );
        return;
      }
      state = state.copyWith(
        items: [...state.items],
        isLoading: false,
        hasMore: false,
        error: e.toString(),
      );
    }
  }

  void reset() {
    _currentPage = 1;
    state = ViolationsAndNoticesState(
      items: [],
      isLoading: false,
      hasMore: true,
      isConnectionError: false,
    );
  }
}
