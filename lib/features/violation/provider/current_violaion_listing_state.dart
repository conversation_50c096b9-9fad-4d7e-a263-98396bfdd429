import 'package:albalad_operator_app/shared/models/current_violation.dart';

class CurrentViolationListingState {
  final List<CurrentViolation> items;
  final bool isLoading;
  final bool hasMore;
  final bool isConnectionError;
  final String? error;

  CurrentViolationListingState({
    required this.items,
    required this.isLoading,
    required this.hasMore,
    this.isConnectionError = false,
    this.error,
  });

  CurrentViolationListingState copyWith({
    List<CurrentViolation>? items,
    bool? isLoading,
    bool? hasMore,
    bool? isConnectionError,
    String? error,
  }) {
    return CurrentViolationListingState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      isConnectionError: isConnectionError ?? this.isConnectionError,
      error: error ?? this.error,
    );
  }
}
