// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'current_violation_listing_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentVioaltionListingNotifierHash() =>
    r'5c5cc6f3d45d697109892e068d7cbe87bb2b36f7';

/// See also [CurrentVioaltionListingNotifier].
@ProviderFor(CurrentVioaltionListingNotifier)
final currentVioaltionListingNotifierProvider = AutoDisposeNotifierProvider<
    CurrentVioaltionListingNotifier, CurrentViolationListingState>.internal(
  CurrentVioaltionListingNotifier.new,
  name: r'currentVioaltionListingNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentVioaltionListingNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentVioaltionListingNotifier
    = AutoDisposeNotifier<CurrentViolationListingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
