// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_violation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$updateViolationHash() => r'cefafcadc5cc3dcd81903f334d7ffc10e6ba4b14';

/// See also [UpdateViolation].
@ProviderFor(UpdateViolation)
final updateViolationProvider =
    AutoDisposeNotifierProvider<UpdateViolation, UpdateViolationState>.internal(
  UpdateViolation.new,
  name: r'updateViolationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$updateViolationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UpdateViolation = AutoDisposeNotifier<UpdateViolationState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
