import 'package:albalad_operator_app/shared/models/vehicle_details.dart';

class ViolationsAndNoticesState {
  final List<VehicleDetails> items;
  final bool isLoading;
  final bool hasMore;
  final bool isConnectionError;
  final String? error;

  ViolationsAndNoticesState({
    required this.items,
    required this.isLoading,
    required this.hasMore,
    this.isConnectionError = false,
    this.error,
  });

  ViolationsAndNoticesState copyWith({
    List<VehicleDetails>? items,
    bool? isLoading,
    bool? hasMore,
    bool? isConnectionError,
    String? error,
  }) {
    return ViolationsAndNoticesState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      isConnectionError: isConnectionError ?? this.isConnectionError,
      error: error ?? this.error,
    );
  }
}
