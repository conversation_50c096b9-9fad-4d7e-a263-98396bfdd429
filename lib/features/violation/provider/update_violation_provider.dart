import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'update_violation_provider.g.dart';

abstract class UpdateViolationState {}

class UpdateViolationInitial extends UpdateViolationState {}

class UpdateViolationLoading extends UpdateViolationState {}

class UpdateViolationSuccess extends UpdateViolationState {}

class UpdateViolationError extends UpdateViolationState {
  final String message;
  UpdateViolationError(this.message);
}

@riverpod
class UpdateViolation extends _$UpdateViolation {
  @override
  UpdateViolationState build() {
    return UpdateViolationInitial();
  }

  final Dio _dio = DioClient().dio;

  updateDescription(
      {required String description, required String violationUid}) async {
    state = UpdateViolationLoading();
    try {
      final response = await _dio.post(
        ApiConstants.updateViolation,
        options: Options(
          headers: await ApiConstants.authHeaders(),
        ),
        data: {'violation_uid': violationUid, 'description': description},
      );

      if (response.statusCode == 200) {
        final json = response.data;
        if (json['result'] == 'success') {
          state = UpdateViolationSuccess();
          return;
        }
      }
      state =
          UpdateViolationError(appLocalization.translate('somethingWentWrong'));
    } catch (e) {
      state = UpdateViolationError(e.toString());
    }
  }

  uploadImages(XFile xFile, {required String violationUid}) async {
    state = UpdateViolationLoading();
    try {
      FormData formData = FormData.fromMap({
        'violation_images': [
          await MultipartFile.fromFile(
            xFile.path,
            filename: xFile.path.split('/').last,
          )
        ],
        'violation_uid': violationUid,
      });

      final response = await _dio.post(
        ApiConstants.updateViolation,
        options: Options(
          headers: await ApiConstants.authFormDataHeaders(),
        ),
        data: formData,
      );

      if (response.statusCode == 200) {
        final json = response.data;
        if (json['result'] == 'success') {
          state = UpdateViolationSuccess();
          return;
        }
      }
      state =
          UpdateViolationError(appLocalization.translate('somethingWentWrong'));
    } catch (e) {
      state = UpdateViolationError(e.toString());
    }
  }

  addMoreImages(
      {required ImageSource source, required String violationUid}) async {
    final picker = ImagePicker();
    final pickedImage = await picker.pickImage(source: source);
    if (pickedImage != null) {
      final croppedImage = await cropImage(image: pickedImage);
      if (croppedImage != null) {
        uploadImages(XFile(croppedImage.path), violationUid: violationUid);
      }
    }
  }

  Future<CroppedFile?> cropImage({required XFile image}) async {
    return ImageCropper().cropImage(
      sourcePath: image.path,
      uiSettings: [
        AndroidUiSettings(
          statusBarColor: Colors.black,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: false,
        ),
        IOSUiSettings(),
      ],
    );
  }
}
