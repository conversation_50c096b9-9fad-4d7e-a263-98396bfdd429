// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tow_clamped_vehicle_listing_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$towClampedVehicleListingNotifierHash() =>
    r'68e16d4fe0b423592d59bafea7301f06032abbe7';

/// See also [TowClampedVehicleListingNotifier].
@ProviderFor(TowClampedVehicleListingNotifier)
final towClampedVehicleListingNotifierProvider = AutoDisposeNotifierProvider<
    TowClampedVehicleListingNotifier, TowClampedVehicleListingState>.internal(
  TowClampedVehicleListingNotifier.new,
  name: r'towClampedVehicleListingNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$towClampedVehicleListingNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TowClampedVehicleListingNotifier
    = AutoDisposeNotifier<TowClampedVehicleListingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
