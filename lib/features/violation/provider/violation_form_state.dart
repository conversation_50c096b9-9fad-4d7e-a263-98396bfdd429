import 'package:image_picker/image_picker.dart';

class ViolationFormState {
  final List<XFile> uploadedImages;
  final bool isSubmitting;
  final bool success;

  ViolationFormState({
    this.uploadedImages = const [],
    this.isSubmitting = false,
    this.success = false,
  });

  ViolationFormState copyWith({
    List<XFile>? uploadedImages,
    bool? isSubmitting,
    bool? success,
  }) {
    return ViolationFormState(
      uploadedImages: uploadedImages ?? this.uploadedImages,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      success: success ?? this.success,
    );
  }
}
