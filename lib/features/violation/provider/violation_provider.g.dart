// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'violation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$violationNotifierHash() => r'33d88e2814755fee59e354331836f915a2c09551';

/// See also [ViolationNotifier].
@ProviderFor(ViolationNotifier)
final violationNotifierProvider =
    AutoDisposeNotifierProvider<ViolationNotifier, ViolationState>.internal(
  ViolationNotifier.new,
  name: r'violationNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$violationNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ViolationNotifier = AutoDisposeNotifier<ViolationState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
