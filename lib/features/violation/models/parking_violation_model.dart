import 'package:albalad_operator_app/shared/models/vehicle_image.dart';

class ParkingViolationModel {
  String? uid;
  String? violationUid;
  String? violationId;
  String? violationName;
  String? vehicleType;
  int? makeYear;
  String? numberPlateType;
  String? paymentStatus;
  String? vehicleNumberPlate;
  String? vehicleName;
  List<VehicleImage>? vehicleImage;
  String? violationTime;
  bool? assignViolation;

  ParkingViolationModel({
    this.uid,
    this.violationUid,
    this.violationId,
    this.violationName,
    this.vehicleType,
    this.makeYear,
    this.numberPlateType,
    this.paymentStatus,
    this.vehicleNumberPlate,
    this.vehicleName,
    this.vehicleImage,
    this.violationTime,
    this.assignViolation,
  });

  ParkingViolationModel.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    violationUid = json['violation_uid'];
    violationId = json['violation_id'];
    violationName = json['violation_name'];
    vehicleType = json['vehicle_type'];
    makeYear = int.tryParse(json['make_year'].toString());
    numberPlateType = json['number_plate_type'];
    paymentStatus = json['payment_status'];
    vehicleNumberPlate = json['vehicle_number_plate'];
    vehicleName = json['vehicle_name'];
    if (json['vehicle_image'] != null) {
      vehicleImage = <VehicleImage>[];
      json['vehicle_image'].forEach((v) {
        vehicleImage!.add(VehicleImage.fromJson(v));
      });
    }
    violationTime = json['violation_time'];
    assignViolation = json['assign_violation'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['violation_uid'] = violationUid;
    data['violation_id'] = violationId;
    data['violation_name'] = violationName;
    data['vehicle_type'] = vehicleType;
    data['make_year'] = makeYear;
    data['number_plate_type'] = numberPlateType;
    data['payment_status'] = paymentStatus;
    data['vehicle_number_plate'] = vehicleNumberPlate;
    data['vehicle_name'] = vehicleName;
    if (vehicleImage != null) {
      data['vehicle_image'] = vehicleImage!.map((v) => v.toJson()).toList();
    }
    data['violation_time'] = violationTime;
    data['assign_violation'] = assignViolation;
    return data;
  }
}
