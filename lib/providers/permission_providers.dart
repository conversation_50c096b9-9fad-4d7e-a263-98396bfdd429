import 'package:albalad_operator_app/features/profile/provider/profile_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final permissionsProvider = StateProvider<List<String>>((ref) {
  final profileAsync = ref.watch(profileProvider);

  return profileAsync.whenOrNull(
        data: (profile) => profile?.permission ?? [],
      ) ??
      [];
});

final valetPermissionProvider = Provider<bool>((ref) {
  final permissions = ref.watch(permissionsProvider);
  return permissions.contains('valet');
});

final clampPermissionProvider = Provider<bool>((ref) {
  final permissions = ref.watch(permissionsProvider);
  return permissions.contains('clamp');
});

final towPermissionProvider = Provider<bool>((ref) {
  final permissions = ref.watch(permissionsProvider);
  return permissions.contains('tow');
});

final operatorPermissionProvider = Provider<bool>((ref) {
  final permissions = ref.watch(permissionsProvider);
  return permissions.contains('operator');
});
