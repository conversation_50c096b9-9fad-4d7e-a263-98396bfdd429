import 'package:albalad_operator_app/app/app.dart';
import 'package:albalad_operator_app/firebase_options.dart';
import 'package:albalad_operator_app/language/app_localizations.dart';
import 'package:albalad_operator_app/language/language_storage.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/helper/shared_preference_helper.dart';
import 'package:albalad_operator_app/shared/services/fcm_services.dart';
import 'package:albalad_operator_app/shared/services/language_services.dart';
import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  await SharedPreferenceHelper.instance.init();
  await FcmServices.init();
  await setupApp();

  easyLoadingConfig();
  runApp(const ProviderScope(child: MyApp()));
}

void easyLoadingConfig() {
  EasyLoading.instance
    ..indicatorWidget = const CustomGradientSpinner()
    ..backgroundColor = Colors.transparent
    ..maskColor = Colors.transparent
    ..maskType = EasyLoadingMaskType.none
    ..textPadding = EdgeInsets.zero
    ..contentPadding = EdgeInsets.zero
    ..loadingStyle = EasyLoadingStyle.custom
    ..indicatorColor = ColorConstants.primaryColor
    ..userInteractions = false
    ..dismissOnTap = false
    ..textColor = Colors.transparent
    ..boxShadow = <BoxShadow>[
      const BoxShadow(
        offset: Offset(2, 2),
        blurRadius: 10,
        color: Color.fromRGBO(0, 0, 0, 0),
      )
    ];
}

Future<void> setupApp() async {
  await LanguageServices.syncLanguageData();

  // Preload language data and register AppLocalizations
  final languageData = await LanguageStorage.getLanguageData();
  final initialLocale = const Locale('en'); // Default locale
  final translations = languageData != null
      ? (initialLocale.languageCode == 'en' ? languageData.en : languageData.ar)
      : null;
  final appLocalizations = AppLocalizations(translations, initialLocale);
  getIt.registerSingleton<AppLocalizations>(appLocalizations);
}
