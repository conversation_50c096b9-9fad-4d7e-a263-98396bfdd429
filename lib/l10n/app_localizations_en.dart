// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get splashScreen => 'Splash Screen!';

  @override
  String get chooseYourPreferredLanguage => 'Choose Your Preferred Language';

  @override
  String get continueString => 'Continue';

  @override
  String get signIn => 'Sign In';

  @override
  String get signInDescription =>
      'Log in to your operator account to efficiently manage parking spaces, monitor activities, and ensure smooth operations.';

  @override
  String get usernameOrEmail => 'Username or Email';

  @override
  String get password => 'Password';

  @override
  String get forgotPasswordLink => 'Forgot Password?';

  @override
  String get forgotPassword => 'Forgot Password';

  @override
  String get pleaseEnterYourEmail => 'Please enter your email';

  @override
  String get pleaseEnterAValidEmail => 'Please enter a valid email';

  @override
  String get pleaseEnterYourPassword => 'Please enter your password';

  @override
  String get pleaseEnterAValidPassword => 'Please enter a valid password';

  @override
  String get searchVehicleNumber => 'Search vehicle number...';

  @override
  String get currentParkedVehicles => 'Current Parked Vehicles';

  @override
  String get currentViolations => 'Current\nViolations';

  @override
  String get currentViolationsTitle => 'Current Violations';

  @override
  String get currentValetVehicles => 'Current Valet Vehicles';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get violationsAndNotices => 'Violations & Notices';

  @override
  String get clampVehicle => 'Clamp Vehicle';

  @override
  String get towVehicle => 'Tow Vehicle';

  @override
  String get valetOperation => 'Valet Operation';

  @override
  String get addVehicle => 'Add Vehicle';

  @override
  String get forgot_password_description =>
      'Choose a contact method to reset\nyour password.';

  @override
  String get via_email => 'Via Email';

  @override
  String get contact_admin => 'Contact Admin';

  @override
  String get get_otp => 'Get OTP';

  @override
  String get enter_the_otp => 'Enter The OTP';

  @override
  String get enter_the_four_digit_code_description =>
      'Enter the 4 digit code sent to you at your registered\nemail address';

  @override
  String get verify => 'Verify';

  @override
  String get confirm_password => 'Confirm Password';

  @override
  String get reset_password => 'Reset Password';

  @override
  String get create_new_password => 'Create New Password';

  @override
  String get create_new_password_description =>
      'You have successfully reset your password.\nPlease use your new password when logging in.';

  @override
  String get back_to_sign_in => 'Back to Sign In';

  @override
  String get connect => 'Connect';

  @override
  String get connect_with_admin => 'Connect with admin';

  @override
  String get connect_with_admin_description =>
      'Would you like to connect with admin?';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get send_otp => 'Send OTP';

  @override
  String get would_you_like_to_get_otp => 'Would you like to get the OTP to';

  @override
  String get somethingWentWrong => 'Something went wrong';

  @override
  String get pleaseEnterAValidOTP => 'Please enter a valid OTP.';

  @override
  String get invalidOtpPleaseTryAgain => 'Invalid OTP. Please try again.';

  @override
  String get anErrorOccurredPleaseTryAgain =>
      'An error occurred. Please try again.';

  @override
  String get passwordReset =>
      'Your new password must be different from\npreviously used password';

  @override
  String get contact => 'Contact';

  @override
  String get passwordMustbeAtLeast8CharactersLong =>
      'Password must be at least 8 characters long.';

  @override
  String get passwordMustHaveAtLeastOneUppercase =>
      'Password must have at least one uppercase letter.';

  @override
  String get passwordMustHaveAtLeastOneLowercase =>
      'Password must have at least one lowercase letter.';

  @override
  String get passwordMustHaveAtLeastOneNumber =>
      'Password must have at least one number.';

  @override
  String get passwordMustHaveAtLeastOneSpecialCharacter =>
      'Password must have at least one special character.';

  @override
  String get passwordsDontMatch => 'Passwords do not match.';

  @override
  String get networkError =>
      'Network error. Please check your connection and try again';

  @override
  String get notAssigned => 'Not Assigned';

  @override
  String get contactAdmin => 'Contact Admin';

  @override
  String get contactAdminDescription =>
      'Are you sure you want to contact the admin? This action will open your phone\'s dialer, and you can proceed to call the admin for assistance. Standard call charges may apply.';

  @override
  String get cancel => 'Cancel';

  @override
  String get services => 'Services';

  @override
  String get id => 'ID :';

  @override
  String get checkInTime => 'Check-in time';

  @override
  String get totalHrs => 'Total Hrs';

  @override
  String get subscriptionDetails => 'Subscription Details';

  @override
  String get subscriptionName => 'Subscription Name';

  @override
  String get location => 'Location';

  @override
  String get type => 'Type';

  @override
  String get startAndEndTime => 'Start & End Time';

  @override
  String get expiryDate => 'Expiry Date';

  @override
  String get searchedVehiclesNotFound =>
      'We couldn\'t find any details for the entered vehicle number. Please check back later or add a new vehicle to the list.';

  @override
  String get noParkedVehiclesFound =>
      'No vehicles are currently parked. Please check back later or add a new vehicle to the list.';

  @override
  String get shared => 'Shared';

  @override
  String get owner => 'Owner';

  @override
  String get vehicleNotFoundInDatabase =>
      'The scanned plate number does not match any registered vehicle in our system.';

  @override
  String get vehicleDataNotFound => 'Vehicle Data Not Found';

  @override
  String get language => 'Language';

  @override
  String get terms_and_conditions => 'Terms & Conditions';

  @override
  String get privacy_policies => 'Privacy Policies';

  @override
  String get notification_settings => 'Notification Settings';

  @override
  String get faqs => 'FAQ’s';

  @override
  String get contact_us => 'Contact Us';

  @override
  String get logout => 'Log Out';

  @override
  String get my_profile => 'My Profile';

  @override
  String get logout_confirmation => 'Are you sure you want to log out?';

  @override
  String get search => 'Search';

  @override
  String get assignNewViolation => 'Assign New Violation';

  @override
  String get violation_type_star => 'Violation Type*';

  @override
  String get select_violation_type => 'Select Violation Type';

  @override
  String get violation_description => 'Violation Description';

  @override
  String get description_here => 'Description here...';

  @override
  String get upload_image => 'Upload Image';

  @override
  String get upload_a_picture => 'Upload a picture';

  @override
  String get submit => 'Submit';

  @override
  String get select_source => 'Select Source';

  @override
  String get please_select_violation_type => 'Please select violation type';

  @override
  String get please_enter_violation_description =>
      'Please enter violation description';

  @override
  String get please_upload_image => 'Please upload image';

  @override
  String get violation_assigned => 'Violation Assigned';

  @override
  String get violation_assigned_description =>
      'A parking violation has been successfully\nassigned to this vehicle.';

  @override
  String get done => 'Done';

  @override
  String get assign_clamping => 'Assign Clamping';

  @override
  String get clamping_enforcer_star => 'Clamping Enforcer*';

  @override
  String get select_clamping_enforcer => 'Select Clamping Enforcer';

  @override
  String get please_select_clamping_enforcer =>
      'Please select clamping enforcer';

  @override
  String get clamping_assigned => 'Clamping Assigned';

  @override
  String get clamping_assigned_description =>
      'This vehicle has been flagged for clamping\ndue to a unsettled parking violation.';

  @override
  String get direct_clamping_assigned_description =>
      'This vehicle has been flagged for clamping\ndue to a parking violation.';

  @override
  String get violation_details => 'Violation Details';

  @override
  String get fined_amount => 'Fined Amount';

  @override
  String get grace_period => 'Grace Period';

  @override
  String get violation_reported_by => 'Violation Reported By';

  @override
  String get next_action_in => 'Next Action in';

  @override
  String get violation_images => 'Violation Images';

  @override
  String get violation_date_and_time => 'Violation Date & Time';

  @override
  String get settle_violation => 'Settle Violation';

  @override
  String get status => 'Status';

  @override
  String get payment_method => 'Payment Method';

  @override
  String get note => 'Note';

  @override
  String get settled => 'Settled';

  @override
  String get pos_machine => 'POS Machine';

  @override
  String get customer_have_settled_ticket => 'Customer have settled ticket';

  @override
  String get please_select_settlement_status =>
      'Please select settlement status';

  @override
  String get please_select_payment_method => 'Please select payment method';

  @override
  String get please_enter_note => 'Please enter note';

  @override
  String settle_violation_note_minimum_chars_validation(Object length) {
    return 'Please provide a note with at least $length characters to explain the settlement';
  }

  @override
  String get violation_settled_successfully =>
      'Violation Settled\nSuccessfully';

  @override
  String get violation_settled_success_message =>
      'The issue has been resolved, and the vehicle is\ncleared from the violation status.';

  @override
  String get violation_list => 'Violations List';

  @override
  String get valet_booking_list => 'Valet Booking List';

  @override
  String get vehicle_details => 'Vehicle Details';

  @override
  String get scanNumberPlate => 'Scan Number Plate';

  @override
  String get scannedNumberPlate => 'Scanned Plate Number';

  @override
  String get assign_towing => 'Assign Towing';

  @override
  String get towing_enforcer_star => 'Towing Enforcer*';

  @override
  String get select_towing_enforcer => 'Select Towing Enforcer';

  @override
  String get please_select_towing_enforcer => 'Please select towing enforcer';

  @override
  String get towing_assigned => 'Towing Assigned';

  @override
  String get towing_assigned_description =>
      'This vehicle has been flagged for towing due to a unsettled parking violation.';

  @override
  String get all => 'All';

  @override
  String get parking => 'Parking';

  @override
  String get clamping => 'Clamping';

  @override
  String get towing => 'Towing';

  @override
  String get current_violations_list => 'Current Violations List';

  @override
  String get everything_looks_good => 'Everything looks good!';

  @override
  String get violations => 'Violations';

  @override
  String get vehicles_list => 'Vehicles List';

  @override
  String get assign_violation => 'Assign Violation';

  @override
  String get no_data_found => 'No data found!';

  @override
  String get save_changes => 'Save Changes';

  @override
  String get full_name_star => 'Full Name*';

  @override
  String get password_star => 'Password*';

  @override
  String get change_password => 'Change Password';

  @override
  String get please_enter_full_name => 'Please enter full name';

  @override
  String get update_successful => 'Update Successful';

  @override
  String get profile_update_success_message =>
      'Profile Updated Successfully! Your\nchanges have been saved.';

  @override
  String get current_password_star => 'Current Password*';

  @override
  String get new_password_star => 'New Password*';

  @override
  String get confirm_new_password_star => 'Confirm New Password*';

  @override
  String get enter_your_current_password => 'Enter your current password';

  @override
  String get enter_your_new_password => 'Enter your new password';

  @override
  String get confirm_your_new_password => 'Confirm your new password';

  @override
  String get save => 'Save';

  @override
  String get please_enter_your_current_password =>
      'Please enter your current password';

  @override
  String get please_enter_your_new_password => 'Please enter your new password';

  @override
  String get please_confirm_your_new_password =>
      'Please confirm your new password';

  @override
  String get new_password_must_be_different =>
      'New password must be different from current password';

  @override
  String get password_updated => 'Password Updated';

  @override
  String get password_updated_message =>
      'Your password has been successfully changed. Please use your new password the next time you log in';

  @override
  String get no_violations_found => 'No Violations Found';

  @override
  String get no_valet_booked => 'No Valet Booked';

  @override
  String get no_violation_assigned => 'No Violation Assigned';

  @override
  String get assign_valet => 'Assign Valet';

  @override
  String get valet_person => 'Valet Person';

  @override
  String get select => 'Select';

  @override
  String get location_of_valet_parking => 'Location of Valet Parking';

  @override
  String get please_select_valet_person => 'Please select valet person';

  @override
  String get please_select_location_of_valet_parking =>
      'Please select location of valet parking';

  @override
  String get valet_assigned_successfully => 'Valet Assigned Successfully';

  @override
  String get valet_assigned_success_message =>
      'The valet has been notified and will manage\nthe vehicle promptly.';

  @override
  String get generate_ticket => 'Generate Ticket';

  @override
  String get ticket_details => 'Ticket Details';

  @override
  String get number_plate => 'Number Plate';

  @override
  String get date => 'Date';

  @override
  String get total_amount => 'Total Amount';

  @override
  String get print_ticket => 'Print Ticket';

  @override
  String get enter_vehicle => 'Enter Vehicle';

  @override
  String get successful => 'Successful';

  @override
  String get enter_vehicle_description =>
      'The vehicle has been checked in for the valet booking.';

  @override
  String get valet_assign_permission_error_message =>
      'Valet assignment requires prior approval. Please get permission before proceeding.';

  @override
  String get valet_booking_details_view_permission_error_message =>
      'You don\'t have permission to view valet booking details. Please get permission before proceeding.';

  @override
  String get violation_assign_permission_error_message =>
      'You don\'t have permission to assign violations. Please get permission before proceeding.';

  @override
  String get violation_details_view_permission_error_message =>
      'You don\'t have permission to view violation details. Please contact the admin for access.';

  @override
  String get failed_to_generate_ticket => 'Failed to generate ticket';

  @override
  String get update_description => 'Update Description';

  @override
  String get no_vehicles_found => 'No vehicles found';

  @override
  String get no_vehicles_found_description =>
      'Please check back later\nor add a new vehicle to the list.';

  @override
  String get violation_description_star => 'Violation Description*';

  @override
  String get upload_image_star => 'Upload Image*';

  @override
  String get status_star => 'Status*';

  @override
  String get payment_method_star => 'Payment Method*';

  @override
  String get note_star => 'Note*';

  @override
  String get valet_person_star => 'Valet Person*';

  @override
  String get location_of_valet_parking_star => 'Location of Valet Parking*';

  @override
  String get search_valet_person => 'Search Valet Person';

  @override
  String get search_location_of_valet_parking =>
      'Search Location Of Valet Parking';

  @override
  String get violation_type => 'Violation Type';

  @override
  String get search_violation_type => 'Search Violation Type';

  @override
  String get clamping_enforcer => 'Clamping Enforcer';

  @override
  String get search_clamping_enforcer => 'Search Clamping Enforcer';

  @override
  String get towing_enforcer => 'Towing Enforcer';

  @override
  String get search_towing_enforcer => 'Search Towing Enforcer';

  @override
  String get valet_vehicles_list => 'Valet Vehicles List';

  @override
  String get time => 'Time';

  @override
  String get settle_ticket => 'Settle Ticket';

  @override
  String get request_vehicle => 'Request Vehicle';

  @override
  String get exit_vehicle => 'Exit Vehicle';

  @override
  String get no_valet_vehicles_found => 'No Valet Vehicles Found';

  @override
  String get please_check_again_later => 'Please check again later';

  @override
  String get confirm_vehicle_exit => 'Confirm Vehicle Exit';

  @override
  String get confirm_vehicle_exit_description =>
      'This action will update the booking status and\ncannot be undone.';

  @override
  String get confirm => 'Confirm';

  @override
  String get exit_successful => 'Exit Successful';

  @override
  String get exit_successful_message =>
      'The vehicle has been handed over and the\nbooking is now complete.';

  @override
  String get vehicle_request_submitted_successfully =>
      'Vehicle request submitted successfully.';

  @override
  String get vat => 'VAT';

  @override
  String get amount => 'Amount';

  @override
  String get new_clamping_requests => 'New Clamping Requests';

  @override
  String get clamp => 'Clamp';

  @override
  String get description_star => 'Description*';

  @override
  String get please_enter_description => 'Please enter description';

  @override
  String get before => 'Before';

  @override
  String get after => 'After';

  @override
  String get could_not_find_vehicle => 'Couldn’t find Vehicle ?';

  @override
  String get vehicle_not_found_description =>
      'Vehicle not found at the specified location. Click here to close clamping procedure';

  @override
  String get close_violation => 'Close Violation';

  @override
  String get clamping_successful => 'Clamping Successful';

  @override
  String get clamping_successful_message =>
      'The vehicle has been clamped due to unsettled\nparking violations.';

  @override
  String get close_clamping => 'Close Clamping';

  @override
  String get scan_qr => 'Scan QR';

  @override
  String get note_here => 'Note here...';

  @override
  String note_minimum_chars_validation(Object length) {
    return 'Please provide a note with at least $length characters';
  }

  @override
  String get clamping_procedure_successfully_closed =>
      'Clamping procedure successfully closed.';

  @override
  String get towing_procedure_successfully_closed =>
      'Towing procedure successfully closed.';

  @override
  String get new_towing_requests => 'New Towing Requests';

  @override
  String get tow => 'Tow';

  @override
  String get close_towing => 'Close Towing';

  @override
  String get towing_successful => 'Towing Successful';

  @override
  String get towing_successful_message =>
      'The vehicle has been towed due to unsettled\nparking violations.';

  @override
  String get clamped_vehicles => 'Clamped Vehicles';

  @override
  String get towed_vehicles => 'Towed Vehicles';

  @override
  String get clamping_requests => 'Clamping Requests';

  @override
  String get towing_requests => 'Towing Requests';

  @override
  String get requests => 'Requests';

  @override
  String get clamped => 'Clamped';

  @override
  String get towed => 'Towed';

  @override
  String get assign => 'Assign';

  @override
  String get no_new_request => 'No New Request';

  @override
  String get no_new_clamping_request_description =>
      'You\'re all caught up!\nNo new clamping requests right now.';

  @override
  String get no_new_towing_request_description =>
      'You\'re all caught up!\nNo new towing requests right now.';

  @override
  String get no_results_found => 'No results found';

  @override
  String get valet_operations => 'Valet Operations';

  @override
  String get valet_request_list => 'Valet Request List';

  @override
  String get search_vehicle_not_found => 'No Vehicle Found';

  @override
  String get search_vehicle_not_found_description_one =>
      'We couldn\\\'t find any vehicle matching the license plate number:';

  @override
  String get search_vehicle_not_found_description_two =>
      'Please double-check the plate number and try again.';

  @override
  String get try_again => 'Try Again';

  @override
  String get clamped_date_and_time => 'Clamped Date & Time';

  @override
  String get clamped_vehicle_details => 'Clamped Vehicle Details';

  @override
  String get clamped_by => 'Clamped By';

  @override
  String get release_vehicle => 'Release Vehicle';

  @override
  String get release_clamp_question => 'Release Clamp?';

  @override
  String get release_clamp_description =>
      'Are you sure you want to release the clamp for this vehicle? This action cannot be undone.';

  @override
  String get release_clamp_success_message =>
      'The clamped vehicle has been released.';

  @override
  String get towed_date_and_time => 'Towed Date & Time';

  @override
  String get no_towed_vehicles => 'No Towed Vehicles';

  @override
  String get no_towed_vehicles_description =>
      'No towed vehicles found in the system.';

  @override
  String get no_clamped_vehicles => 'No Clamped Vehicles';

  @override
  String get no_clamped_vehicles_description =>
      'No clamped vehicles found in the system.';

  @override
  String get towed_vehicle_details => 'Towed Vehicle Details';

  @override
  String get towed_by => 'Towed By';

  @override
  String get release_vehicle_question => 'Release Vehicle?';

  @override
  String get release_vehicle_description =>
      'Are you sure you want to release the vehicle? This action cannot be undone.';

  @override
  String get release_tow_success_message =>
      'The towed vehicle has been released.';

  @override
  String get update_vehicle_location => 'Update Vehicle Location';

  @override
  String get update_vehicle_location_description =>
      'Modify the current location of the towed vehicle.';

  @override
  String get update => 'Update';

  @override
  String get confirm_location_update => 'Confirm Location Update';

  @override
  String get confirm_location_update_description =>
      'Are you sure you want to update the location of the towed vehicle? This action will replace the previous location with the new one.';

  @override
  String get towed_location_updated_successfully =>
      'Towed vehicle location updated successfully.';

  @override
  String get add_new_vehicle => 'Add New Vehicle';

  @override
  String get select_plate_type_star => 'Select Plate Type*';

  @override
  String get license_plate_number_star => 'License Plate Number*';

  @override
  String get please_select_plate_type => 'Please select plate type';

  @override
  String get please_enter_license_plate_number =>
      'Please enter license plate number';

  @override
  String get or => 'Or';

  @override
  String get scan_plate => 'Scan Plate';

  @override
  String get vehicle_name_star => 'Vehicle Name*';

  @override
  String get please_enter_vehicle_name => 'Please enter vehicle name';

  @override
  String get vehicle_registered_country_star => 'Vehicle Registered Country*';

  @override
  String get please_select_vehicle_registered_country =>
      'Please select vehicle registered country';

  @override
  String get make_year => 'Make Year';

  @override
  String get select_year => 'Select year';

  @override
  String get color => 'Color';

  @override
  String get select_color => 'Select color';

  @override
  String get vehicle_type_star => 'Vehicle Type*';

  @override
  String get select_type => 'Select type';

  @override
  String get please_select_vehicle_type => 'Please select vehicle type';

  @override
  String get car_image => 'Car Image';

  @override
  String get contact_number => 'Contact Number';

  @override
  String get enter_mobile_number => 'Enter Mobile Number';

  @override
  String get search_country => 'Search Country';

  @override
  String get select_country => 'Select Country';

  @override
  String get license_plate_number => 'License Plate Number';

  @override
  String get vehicle_registered_country => 'Vehicle Registered Country';

  @override
  String get search_color => 'Search Color';

  @override
  String get vehicle_type => 'Vehicle Type';

  @override
  String get search_type => 'Search Type';

  @override
  String get save_vehicle => 'Save Vehicle';

  @override
  String get enter_plate_number => 'Enter plate number';

  @override
  String get enter_plate_letters => 'Enter plate letters';

  @override
  String get english_and_arabic_plate_letters_should_match =>
      'English and Arabic plate letters should match.';

  @override
  String get please_enter_valid_mobile_number =>
      'Please enter a valid mobile number';

  @override
  String get confirm_vehicle_license_plate => 'Confirm Vehicle License Plate';

  @override
  String get confirm_and_save => 'Confirm  & Save';

  @override
  String get back_to_edit => 'Back to Edit';

  @override
  String get notifications => 'Notifications';

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get admin => 'Admin';

  @override
  String get valet => 'Valet';

  @override
  String get no_notifications => 'No Notifications';

  @override
  String get no_notifications_description => 'No notifications at the moment.';

  @override
  String get press_back_again_to_close => 'Press back again to close';

  @override
  String get not_available => 'Not Available';

  @override
  String get assigned => 'Assigned';

  @override
  String get vehicles => 'Vehicles';

  @override
  String get assigned_clamping_request => 'Assigned Clamping Request';

  @override
  String get assigned_towing_request => 'Assigned Towing Request';

  @override
  String get search_status => 'Search Status';

  @override
  String get search_payment_method => 'Search Payment Method';

  @override
  String get no_request_assigned => 'No Requests Assigned';

  @override
  String get no_request_assigned_description =>
      'You haven’t assigned any requests yet. Assign a request to track progress.';

  @override
  String get connection_lost => 'Connection Lost';

  @override
  String get connection_lost_description =>
      'Please check your internet connection and try\nagain.';

  @override
  String get headsup => 'Heads Up!';

  @override
  String get okay => 'Okay';

  @override
  String get printer_not_detected_title => 'Let’s Connect Your Printer';

  @override
  String get printer_not_detected_description =>
      'No printer detected. Please ensure your SPP-R310 is paired and turned on.';

  @override
  String get printing_issue_title => 'Printing Issue';

  @override
  String get printing_issue_description =>
      'We couldn’t print your ticket. Please ensure your printer is connected and try again.';

  @override
  String get sar => 'SAR';

  @override
  String get gallery => 'Gallery';

  @override
  String get camera => 'Camera';

  @override
  String get arabic => 'Arabic';

  @override
  String get english => 'English';

  @override
  String get no_valet_assigned =>
      'No valet assigned yet. Please assign a valet to proceed.';

  @override
  String get bluetooth_not_enabled => 'Bluetooth Not Enabled';

  @override
  String get bluetooth_not_enabled_description =>
      'To print a ticket, please enable Bluetooth and connect to the printer.';

  @override
  String get enable_bluetooth => 'Enable Bluetooth';

  @override
  String get add => 'Add';

  @override
  String get vehicle_name_hint_text => 'Eg. Toyota Prado';

  @override
  String get add_more => 'Add More';

  @override
  String get did_not_receive_the_otp => 'Didn’t receive the OTP?';

  @override
  String get resend_otp => 'Resend OTP';
}
