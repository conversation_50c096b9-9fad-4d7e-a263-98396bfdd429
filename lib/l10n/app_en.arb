{"splashScreen": "Splash Screen!", "chooseYourPreferredLanguage": "Choose Your Preferred Language", "continueString": "Continue", "signIn": "Sign In", "signInDescription": "Log in to your operator account to efficiently manage parking spaces, monitor activities, and ensure smooth operations.", "usernameOrEmail": "Username or Email", "password": "Password", "forgotPasswordLink": "Forgot Password?", "forgotPassword": "Forgot Password", "pleaseEnterYourEmail": "Please enter your email", "pleaseEnterAValidEmail": "Please enter a valid email", "pleaseEnterYourPassword": "Please enter your password", "pleaseEnterAValidPassword": "Please enter a valid password", "searchVehicleNumber": "Search vehicle number...", "currentParkedVehicles": "Current Parked Vehicles", "currentViolations": "Current\nViolations", "currentViolationsTitle": "Current Violations", "currentValetVehicles": "Current Valet Vehicles", "quickActions": "Quick Actions", "violationsAndNotices": "Violations & Notices", "clampVehicle": "Clamp Vehicle", "towVehicle": "Tow Vehicle", "valetOperation": "Valet Operation", "addVehicle": "Add Vehicle", "forgot_password_description": "Choose a contact method to reset\nyour password.", "via_email": "Via Email", "contact_admin": "Contact Admin", "get_otp": "Get OTP", "enter_the_otp": "Enter The OTP", "enter_the_four_digit_code_description": "Enter the 4 digit code sent to you at your registered\nemail address", "verify": "Verify", "confirm_password": "Confirm Password", "reset_password": "Reset Password", "create_new_password": "Create New Password", "create_new_password_description": "You have successfully reset your password.\nPlease use your new password when logging in.", "back_to_sign_in": "Back to Sign In", "connect": "Connect", "connect_with_admin": "Connect with admin", "connect_with_admin_description": "Would you like to connect with admin?", "yes": "Yes", "no": "No", "send_otp": "Send OTP", "would_you_like_to_get_otp": "Would you like to get the OTP to", "somethingWentWrong": "Something went wrong", "pleaseEnterAValidOTP": "Please enter a valid OTP.", "invalidOtpPleaseTryAgain": "Invalid OTP. Please try again.", "anErrorOccurredPleaseTryAgain": "An error occurred. Please try again.", "passwordReset": "Your new password must be different from\npreviously used password", "contact": "Contact", "passwordMustbeAtLeast8CharactersLong": "Password must be at least 8 characters long.", "passwordMustHaveAtLeastOneUppercase": "Password must have at least one uppercase letter.", "passwordMustHaveAtLeastOneLowercase": "Password must have at least one lowercase letter.", "passwordMustHaveAtLeastOneNumber": "Password must have at least one number.", "passwordMustHaveAtLeastOneSpecialCharacter": "Password must have at least one special character.", "passwordsDontMatch": "Passwords do not match.", "networkError": "Network error. Please check your connection and try again", "notAssigned": "Not Assigned", "contactAdmin": "Contact Admin", "contactAdminDescription": "Are you sure you want to contact the admin? This action will open your phone's dialer, and you can proceed to call the admin for assistance. Standard call charges may apply.", "cancel": "Cancel", "services": "Services", "id": "ID :", "checkInTime": "Check-in time", "totalHrs": "Total Hrs", "subscriptionDetails": "Subscription Details", "subscriptionName": "Subscription Name", "location": "Location", "type": "Type", "startAndEndTime": "Start & End Time", "expiryDate": "Expiry Date", "searchedVehiclesNotFound": "We couldn't find any details for the entered vehicle number. Please check back later or add a new vehicle to the list.", "noParkedVehiclesFound": "No vehicles are currently parked. Please check back later or add a new vehicle to the list.", "shared": "Shared", "owner": "Owner", "vehicleNotFoundInDatabase": "The scanned plate number does not match any registered vehicle in our system.", "vehicleDataNotFound": "Vehicle Data Not Found", "language": "Language", "terms_and_conditions": "Terms & Conditions", "privacy_policies": "Privacy Policies", "notification_settings": "Notification Settings", "faqs": "FAQ’s", "contact_us": "Contact Us", "logout": "Log Out", "my_profile": "My Profile", "logout_confirmation": "Are you sure you want to log out?", "search": "Search", "assignNewViolation": "Assign New Violation", "violation_type_star": "Violation Type*", "select_violation_type": "Select Violation Type", "violation_description": "Violation Description", "description_here": "Description here...", "upload_image": "Upload Image", "upload_a_picture": "Upload a picture", "submit": "Submit", "select_source": "Select Source", "please_select_violation_type": "Please select violation type", "please_enter_violation_description": "Please enter violation description", "please_upload_image": "Please upload image", "violation_assigned": "Violation Assigned", "violation_assigned_description": "A parking violation has been successfully\nassigned to this vehicle.", "done": "Done", "assign_clamping": "Assign <PERSON>ing", "clamping_enforcer_star": "Clamping Enforcer*", "select_clamping_enforcer": "Select Clamping Enforcer", "please_select_clamping_enforcer": "Please select clamping enforcer", "clamping_assigned": "Clamping Assigned", "clamping_assigned_description": "This vehicle has been flagged for clamping\ndue to a unsettled parking violation.", "direct_clamping_assigned_description": "This vehicle has been flagged for clamping\ndue to a parking violation.", "violation_details": "Violation Details", "fined_amount": "Fined Amount", "grace_period": "<PERSON> Period", "violation_reported_by": "Violation Reported By", "next_action_in": "Next Action in", "violation_images": "Violation Images", "violation_date_and_time": "Violation Date & Time", "settle_violation": "Settle Violation", "status": "Status", "payment_method": "Payment Method", "note": "Note", "settled": "Settled", "pos_machine": "POS Machine", "customer_have_settled_ticket": "Customer have settled ticket", "please_select_settlement_status": "Please select settlement status", "please_select_payment_method": "Please select payment method", "please_enter_note": "Please enter note", "settle_violation_note_minimum_chars_validation": "Please provide a note with at least {length} characters to explain the settlement", "violation_settled_successfully": "Violation Settled\nSuccessfully", "violation_settled_success_message": "The issue has been resolved, and the vehicle is\ncleared from the violation status.", "violation_list": "Violations List", "valet_booking_list": "Valet Booking List", "vehicle_details": "Vehicle Details", "scanNumberPlate": "Scan Number Plate", "scannedNumberPlate": "Scanned Plate Number", "assign_towing": "Assign <PERSON><PERSON>", "towing_enforcer_star": "Towing Enforcer*", "select_towing_enforcer": "Select Towing Enforcer", "please_select_towing_enforcer": "Please select towing enforcer", "towing_assigned": "Towing Assigned", "towing_assigned_description": "This vehicle has been flagged for towing due to a unsettled parking violation.", "all": "All", "parking": "Parking", "clamping": "Clamping", "towing": "Towing", "current_violations_list": "Current Violations List", "everything_looks_good": "Everything looks good!", "violations": "Violations", "vehicles_list": "Vehicles List", "assign_violation": "Assign Violation", "no_data_found": "No data found!", "save_changes": "Save Changes", "full_name_star": "Full Name*", "password_star": "Password*", "change_password": "Change Password", "please_enter_full_name": "Please enter full name", "update_successful": "Update Successful", "profile_update_success_message": "Profile Updated Successfully! Your\nchanges have been saved.", "current_password_star": "Current Password*", "new_password_star": "New Password*", "confirm_new_password_star": "Confirm New Password*", "enter_your_current_password": "Enter your current password", "enter_your_new_password": "Enter your new password", "confirm_your_new_password": "Confirm your new password", "save": "Save", "please_enter_your_current_password": "Please enter your current password", "please_enter_your_new_password": "Please enter your new password", "please_confirm_your_new_password": "Please confirm your new password", "new_password_must_be_different": "New password must be different from current password", "password_updated": "Password Updated", "password_updated_message": "Your password has been successfully changed. Please use your new password the next time you log in", "no_violations_found": "No Violations Found", "no_valet_booked": "No Valet Booked", "no_violation_assigned": "No Violation Assigned", "assign_valet": "Assign <PERSON>", "valet_person": "<PERSON><PERSON>", "select": "Select", "location_of_valet_parking": "Location of Valet Parking", "please_select_valet_person": "Please select valet person", "please_select_location_of_valet_parking": "Please select location of valet parking", "valet_assigned_successfully": "Valet Assigned Successfully", "valet_assigned_success_message": "The valet has been notified and will manage\nthe vehicle promptly.", "generate_ticket": "Generate Ticket", "ticket_details": "Ticket Details", "number_plate": "Number Plate", "date": "Date", "total_amount": "Total Amount", "print_ticket": "Print Ticket", "enter_vehicle": "Enter Vehicle", "successful": "Successful", "enter_vehicle_description": "The vehicle has been checked in for the valet booking.", "valet_assign_permission_error_message": "Valet assignment requires prior approval. Please get permission before proceeding.", "valet_booking_details_view_permission_error_message": "You don't have permission to view valet booking details. Please get permission before proceeding.", "violation_assign_permission_error_message": "You don't have permission to assign violations. Please get permission before proceeding.", "violation_details_view_permission_error_message": "You don't have permission to view violation details. Please contact the admin for access.", "failed_to_generate_ticket": "Failed to generate ticket", "update_description": "Update Description", "no_vehicles_found": "No vehicles found", "no_vehicles_found_description": "Please check back later\nor add a new vehicle to the list.", "violation_description_star": "Violation Description*", "upload_image_star": "Upload Image*", "status_star": "Status*", "payment_method_star": "Payment Method*", "note_star": "Note*", "valet_person_star": "Valet Person*", "location_of_valet_parking_star": "Location of Valet Parking*", "search_valet_person": "Search Valet Person", "search_location_of_valet_parking": "Search Location Of Valet Parking", "violation_type": "Violation Type", "search_violation_type": "Search Violation Type", "clamping_enforcer": "Clamping Enforcer", "search_clamping_enforcer": "Search Clamping Enforcer", "towing_enforcer": "Towing Enforcer", "search_towing_enforcer": "Search Towing Enforcer", "valet_vehicles_list": "Valet Vehicles List", "time": "Time", "settle_ticket": "Settle Ticket", "request_vehicle": "Request Vehicle", "exit_vehicle": "Exit Vehicle", "no_valet_vehicles_found": "No Valet Vehicles Found", "please_check_again_later": "Please check again later", "confirm_vehicle_exit": "Confirm Vehicle Exit", "confirm_vehicle_exit_description": "This action will update the booking status and\ncannot be undone.", "confirm": "Confirm", "exit_successful": "Exit Successful", "exit_successful_message": "The vehicle has been handed over and the\nbooking is now complete.", "vehicle_request_submitted_successfully": "Vehicle request submitted successfully.", "vat": "VAT", "amount": "Amount", "new_clamping_requests": "New Clamping Requests", "clamp": "C<PERSON>", "description_star": "Description*", "please_enter_description": "Please enter description", "before": "Before", "after": "After", "could_not_find_vehicle": "Couldn’t find Vehicle ?", "vehicle_not_found_description": "Vehicle not found at the specified location. Click here to close clamping procedure", "close_violation": "Close Violation", "clamping_successful": "Clamping Successful", "clamping_successful_message": "The vehicle has been clamped due to unsettled\nparking violations.", "close_clamping": "Close Clamping", "scan_qr": "Scan QR", "note_here": "Note here...", "note_minimum_chars_validation": "Please provide a note with at least {length} characters", "clamping_procedure_successfully_closed": "Clamping procedure successfully closed.", "towing_procedure_successfully_closed": "Towing procedure successfully closed.", "new_towing_requests": "New Towing Requests", "tow": "Tow", "close_towing": "Close Towing", "towing_successful": "Towing Successful", "towing_successful_message": "The vehicle has been towed due to unsettled\nparking violations.", "clamped_vehicles": "Clamped Vehicles", "towed_vehicles": "Towed Vehicles", "clamping_requests": "Clamping Requests", "towing_requests": "Towing Requests", "requests": "Requests", "clamped": "Clamped", "towed": "Towed", "assign": "Assign", "no_new_request": "No New Request", "no_new_clamping_request_description": "You're all caught up!\nNo new clamping requests right now.", "no_new_towing_request_description": "You're all caught up!\nNo new towing requests right now.", "no_results_found": "No results found", "valet_operations": "Valet Operations", "valet_request_list": "Valet Request List", "search_vehicle_not_found": "No Vehicle Found", "search_vehicle_not_found_description_one": "We couldn\\'t find any vehicle matching the license plate number:", "search_vehicle_not_found_description_two": "Please double-check the plate number and try again.", "try_again": "Try Again", "clamped_date_and_time": "Clamped Date & Time", "clamped_vehicle_details": "Clamped Vehicle Details", "clamped_by": "Clamped By", "release_vehicle": "Release Vehicle", "release_clamp_question": "Release Clamp?", "release_clamp_description": "Are you sure you want to release the clamp for this vehicle? This action cannot be undone.", "release_clamp_success_message": "The clamped vehicle has been released.", "towed_date_and_time": "Towed Date & Time", "no_towed_vehicles": "No Towed Vehicles", "no_towed_vehicles_description": "No towed vehicles found in the system.", "no_clamped_vehicles": "No Clamped Vehicles", "no_clamped_vehicles_description": "No clamped vehicles found in the system.", "towed_vehicle_details": "Towed Vehicle Details", "towed_by": "Towed By", "release_vehicle_question": "Release Vehicle?", "release_vehicle_description": "Are you sure you want to release the vehicle? This action cannot be undone.", "release_tow_success_message": "The towed vehicle has been released.", "update_vehicle_location": "Update Vehicle Location", "update_vehicle_location_description": "Modify the current location of the towed vehicle.", "update": "Update", "confirm_location_update": "Confirm Location Update", "confirm_location_update_description": "Are you sure you want to update the location of the towed vehicle? This action will replace the previous location with the new one.", "towed_location_updated_successfully": "Towed vehicle location updated successfully.", "add_new_vehicle": "Add New Vehicle", "select_plate_type_star": "Select Plate Type*", "license_plate_number_star": "License Plate Number*", "please_select_plate_type": "Please select plate type", "please_enter_license_plate_number": "Please enter license plate number", "or": "Or", "scan_plate": "Scan Plate", "vehicle_name_star": "Vehicle Name*", "please_enter_vehicle_name": "Please enter vehicle name", "vehicle_registered_country_star": "Vehicle Registered Country*", "please_select_vehicle_registered_country": "Please select vehicle registered country", "make_year": "Make Year", "select_year": "Select year", "color": "Color", "select_color": "Select color", "vehicle_type_star": "Vehicle Type*", "select_type": "Select type", "please_select_vehicle_type": "Please select vehicle type", "car_image": "Car Image", "contact_number": "Contact Number", "enter_mobile_number": "Enter Mobile Number", "search_country": "Search Country", "select_country": "Select Country", "license_plate_number": "License Plate Number", "vehicle_registered_country": "Vehicle Registered Country", "search_color": "Search Color", "vehicle_type": "Vehicle Type", "search_type": "Search Type", "save_vehicle": "Save Vehicle", "enter_plate_number": "Enter plate number", "enter_plate_letters": "Enter plate letters", "english_and_arabic_plate_letters_should_match": "English and Arabic plate letters should match.", "please_enter_valid_mobile_number": "Please enter a valid mobile number", "confirm_vehicle_license_plate": "Confirm Vehicle License Plate", "confirm_and_save": "Confirm  & Save", "back_to_edit": "Back to Edit", "notifications": "Notifications", "today": "Today", "yesterday": "Yesterday", "admin": "Admin", "valet": "Valet", "no_notifications": "No Notifications", "no_notifications_description": "No notifications at the moment.", "press_back_again_to_close": "Press back again to close", "not_available": "Not Available", "assigned": "Assigned", "vehicles": "Vehicles", "assigned_clamping_request": "Assigned Clamping Request", "assigned_towing_request": "Assigned Towing Request", "search_status": "Search Status", "search_payment_method": "Search Payment Method", "no_request_assigned": "No Requests Assigned", "no_request_assigned_description": "You haven’t assigned any requests yet. Assign a request to track progress.", "connection_lost": "Connection Lost", "connection_lost_description": "Please check your internet connection and try\nagain.", "headsup": "Heads Up!", "okay": "Okay", "printer_not_detected_title": "Let’s Connect Your Printer", "printer_not_detected_description": "No printer detected. Please ensure your SPP-R310 is paired and turned on.", "printing_issue_title": "Printing Issue", "printing_issue_description": "We couldn’t print your ticket. Please ensure your printer is connected and try again.", "sar": "SAR", "gallery": "Gallery", "camera": "Camera", "arabic": "Arabic", "english": "English", "no_valet_assigned": "No valet assigned yet. Please assign a valet to proceed.", "bluetooth_not_enabled": "Bluetooth Not Enabled", "bluetooth_not_enabled_description": "To print a ticket, please enable Bluetooth and connect to the printer.", "enable_bluetooth": "Enable Bluetooth", "add": "Add", "vehicle_name_hint_text": "Eg. Toyota Prado", "add_more": "Add More", "did_not_receive_the_otp": "Didn’t receive the OTP?", "resend_otp": "Resend OTP"}