// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get splashScreen => 'شاشة البداية!';

  @override
  String get chooseYourPreferredLanguage => 'اختر لغتك المفضلة';

  @override
  String get continueString => 'متابعة';

  @override
  String get signIn => 'تسجيل الدخول';

  @override
  String get signInDescription =>
      'سجل الدخول إلى حساب المشغل الخاص بك لإدارة أماكن الوقوف بكفاءة، ومراقبة الأنشطة، وضمان سلاسة العمليات.';

  @override
  String get usernameOrEmail => 'اسم المستخدم أو البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get forgotPasswordLink => 'هل نسيت كلمة المرور؟';

  @override
  String get forgotPassword => 'نسيت كلمة المرور';

  @override
  String get pleaseEnterYourEmail => 'يرجى إدخال بريدك الإلكتروني';

  @override
  String get pleaseEnterAValidEmail => 'يرجى إدخال بريد إلكتروني صالح';

  @override
  String get pleaseEnterYourPassword => 'يرجى إدخال كلمة المرور الخاصة بك';

  @override
  String get pleaseEnterAValidPassword => 'يرجى إدخال كلمة مرور صالحة';

  @override
  String get searchVehicleNumber => 'ابحث عن رقم المركبة...';

  @override
  String get currentParkedVehicles => 'المركبات المتوقفة حاليًا';

  @override
  String get currentViolations => 'المخالفات\nالحالية';

  @override
  String get currentViolationsTitle => 'المخالفات الحالية';

  @override
  String get currentValetVehicles => 'مركبات الخدمة الذاتية الحالية';

  @override
  String get quickActions => 'إجراءات سريعة';

  @override
  String get violationsAndNotices => 'المخالفات والإشعارات';

  @override
  String get clampVehicle => 'تثبيت المركبة';

  @override
  String get towVehicle => 'سحب المركبة';

  @override
  String get valetOperation => 'عملية الخدمة الذاتية';

  @override
  String get addVehicle => 'إضافة مركبة';

  @override
  String get forgot_password_description =>
      'اختر طريقة الاتصال لإعادة تعيين\nكلمة المرور الخاصة بك.';

  @override
  String get via_email => 'عبر البريد الإلكتروني';

  @override
  String get contact_admin => 'اتصل بالمسؤول';

  @override
  String get get_otp => 'احصل على رمز OTP';

  @override
  String get enter_the_otp => 'أدخل رمز OTP';

  @override
  String get enter_the_four_digit_code_description =>
      'أدخل الرمز المكون من 4 أرقام المرسل إليك على\nعنوان بريدك الإلكتروني المسجل';

  @override
  String get verify => 'تحقق';

  @override
  String get confirm_password => 'تأكيد كلمة المرور';

  @override
  String get reset_password => 'إعادة تعيين كلمة المرور';

  @override
  String get create_new_password => 'إنشاء كلمة مرور جديدة';

  @override
  String get create_new_password_description =>
      'لقد نجحت في إعادة تعيين كلمة المرور الخاصة بك.\nيرجى استخدام كلمة المرور الجديدة عند تسجيل الدخول.';

  @override
  String get back_to_sign_in => 'العودة إلى تسجيل الدخول';

  @override
  String get connect => 'اتصل';

  @override
  String get connect_with_admin => 'اتصل بالمسؤول';

  @override
  String get connect_with_admin_description => 'هل ترغب في الاتصال بالمسؤول؟';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get send_otp => 'إرسال رمز OTP';

  @override
  String get would_you_like_to_get_otp => 'هل ترغب في الحصول على رمز OTP إلى';

  @override
  String get somethingWentWrong => 'حدث خطأ ما';

  @override
  String get pleaseEnterAValidOTP => 'يرجى إدخال رمز OTP صالح.';

  @override
  String get invalidOtpPleaseTryAgain =>
      'رمز OTP غير صالح. يرجى المحاولة مرة أخرى.';

  @override
  String get anErrorOccurredPleaseTryAgain =>
      'حدث خطأ. يرجى المحاولة مرة أخرى.';

  @override
  String get passwordReset =>
      'يجب أن تكون كلمة المرور الجديدة مختلفة عن\nكلمة المرور المستخدمة سابقًا';

  @override
  String get contact => 'اتصال';

  @override
  String get passwordMustbeAtLeast8CharactersLong =>
      'يجب أن تتكون كلمة المرور من 8 أحرف على الأقل.';

  @override
  String get passwordMustHaveAtLeastOneUppercase =>
      'يجب أن تحتوي كلمة المرور على حرف كبير واحد على الأقل.';

  @override
  String get passwordMustHaveAtLeastOneLowercase =>
      'يجب أن تحتوي كلمة المرور على حرف صغير واحد على الأقل.';

  @override
  String get passwordMustHaveAtLeastOneNumber =>
      'يجب أن تحتوي كلمة المرور على رقم واحد على الأقل.';

  @override
  String get passwordMustHaveAtLeastOneSpecialCharacter =>
      'يجب أن تحتوي كلمة المرور على رمز خاص واحد على الأقل.';

  @override
  String get passwordsDontMatch => 'كلمات المرور لا تتطابق.';

  @override
  String get networkError =>
      'خطأ في الشبكة. يرجى التحقق من اتصالك والمحاولة مرة أخرى';

  @override
  String get notAssigned => 'غير معين';

  @override
  String get contactAdmin => 'اتصل بالمسؤول';

  @override
  String get contactAdminDescription =>
      'هل أنت متأكد أنك تريد الاتصال بالمسؤول؟ سيؤدي هذا الإجراء إلى فتح تطبيق الهاتف الخاص بك، ويمكنك المتابعة للاتصال بالمسؤول للحصول على المساعدة. قد يتم تطبيق رسوم المكالمة القياسية.';

  @override
  String get cancel => 'إلغاء';

  @override
  String get services => 'الخدمات';

  @override
  String get id => 'المعرف:';

  @override
  String get checkInTime => 'وقت التسجيل';

  @override
  String get totalHrs => 'إجمالي الساعات';

  @override
  String get subscriptionDetails => 'تفاصيل الاشتراك';

  @override
  String get subscriptionName => 'اسم الاشتراك';

  @override
  String get location => 'الموقع';

  @override
  String get type => 'النوع';

  @override
  String get startAndEndTime => 'وقت البداية والنهاية';

  @override
  String get expiryDate => 'تاريخ الانتهاء';

  @override
  String get searchedVehiclesNotFound =>
      'لم نتمكن من العثور على أي تفاصيل لرقم المركبة المدخل. يرجى التحقق مرة أخرى لاحقًا أو إضافة مركبة جديدة إلى القائمة.';

  @override
  String get noParkedVehiclesFound =>
      'لا توجد مركبات متوقفة حاليًا. يرجى التحقق مرة أخرى لاحقًا أو إضافة مركبة جديدة إلى القائمة.';

  @override
  String get shared => 'مشترك';

  @override
  String get owner => 'المالك';

  @override
  String get vehicleNotFoundInDatabase =>
      'رقم اللوحة الممسوحة لا يتطابق مع أي مركبة مسجلة في نظامنا.';

  @override
  String get vehicleDataNotFound => 'بيانات المركبة غير موجودة';

  @override
  String get language => 'اللغة';

  @override
  String get terms_and_conditions => 'الشروط والأحكام';

  @override
  String get privacy_policies => 'سياسات الخصوصية';

  @override
  String get notification_settings => 'إعدادات الإشعارات';

  @override
  String get faqs => 'الأسئلة الشائعة';

  @override
  String get contact_us => 'اتصل بنا';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get my_profile => 'ملفي الشخصي';

  @override
  String get logout_confirmation => 'هل أنت متأكد أنك تريد تسجيل الخروج؟';

  @override
  String get search => 'بحث';

  @override
  String get assignNewViolation => 'تعيين مخالفة جديدة';

  @override
  String get violation_type_star => 'نوع المخالفة*';

  @override
  String get select_violation_type => 'اختر نوع المخالفة';

  @override
  String get violation_description => 'وصف المخالفة';

  @override
  String get description_here => 'الوصف هنا...';

  @override
  String get upload_image => 'رفع صورة';

  @override
  String get upload_a_picture => 'رفع صورة';

  @override
  String get submit => 'إرسال';

  @override
  String get select_source => 'اختر المصدر';

  @override
  String get please_select_violation_type => 'يرجى اختيار نوع المخالفة';

  @override
  String get please_enter_violation_description => 'يرجى إدخال وصف المخالفة';

  @override
  String get please_upload_image => 'يرجى رفع صورة';

  @override
  String get violation_assigned => 'تم تعيين المخالفة';

  @override
  String get violation_assigned_description =>
      'تم تعيين مخالفة وقوف بنجاح\nلهذه المركبة.';

  @override
  String get done => 'تم';

  @override
  String get assign_clamping => 'تعيين التثبيت';

  @override
  String get clamping_enforcer_star => 'منفذ التثبيت*';

  @override
  String get select_clamping_enforcer => 'اختر منفذ التثبيت';

  @override
  String get please_select_clamping_enforcer => 'يرجى اختيار منفذ التثبيت';

  @override
  String get clamping_assigned => 'تم تعيين التثبيت';

  @override
  String get clamping_assigned_description =>
      'تم وضع علامة على هذه المركبة للتثبيت\nبسبب مخالفة وقوف غير مسددة.';

  @override
  String get direct_clamping_assigned_description =>
      'تم وضع علامة على هذه المركبة للتثبيت\nبسبب مخالفة وقوف.';

  @override
  String get violation_details => 'تفاصيل المخالفة';

  @override
  String get fined_amount => 'المبلغ المغرم';

  @override
  String get grace_period => 'فترة السماح';

  @override
  String get violation_reported_by => 'تم الإبلاغ عن المخالفة بواسطة';

  @override
  String get next_action_in => 'الإجراء التالي في';

  @override
  String get violation_images => 'صور المخالفة';

  @override
  String get violation_date_and_time => 'تاريخ ووقت المخالفة';

  @override
  String get settle_violation => 'تسوية المخالفة';

  @override
  String get status => 'الحالة';

  @override
  String get payment_method => 'طريقة الدفع';

  @override
  String get note => 'ملاحظة';

  @override
  String get settled => 'تم التسوية';

  @override
  String get pos_machine => 'آلة نقاط البيع';

  @override
  String get customer_have_settled_ticket => 'العميل قد سدد التذكرة';

  @override
  String get please_select_settlement_status => 'يرجى اختيار حالة التسوية';

  @override
  String get please_select_payment_method => 'يرجى اختيار طريقة الدفع';

  @override
  String get please_enter_note => 'يرجى إدخال ملاحظة';

  @override
  String settle_violation_note_minimum_chars_validation(Object length) {
    return 'يرجى تقديم ملاحظة تحتوي على $length أحرف على الأقل لشرح التسوية';
  }

  @override
  String get violation_settled_successfully => 'تم تسوية المخالفة\nبنجاح';

  @override
  String get violation_settled_success_message =>
      'تم حل المشكلة، وتم مسح المركبة من حالة المخالفة.';

  @override
  String get violation_list => 'قائمة المخالفات';

  @override
  String get valet_booking_list => 'قائمة حجوزات الخدمة الذاتية';

  @override
  String get vehicle_details => 'تفاصيل المركبة';

  @override
  String get scanNumberPlate => 'مسح لوحة الأرقام';

  @override
  String get scannedNumberPlate => 'رقم اللوحة الممسوحة';

  @override
  String get assign_towing => 'تعيين السحب';

  @override
  String get towing_enforcer_star => 'منفذ السحب*';

  @override
  String get select_towing_enforcer => 'اختر منفذ السحب';

  @override
  String get please_select_towing_enforcer => 'يرجى اختيار منفذ السحب';

  @override
  String get towing_assigned => 'تم تعيين السحب';

  @override
  String get towing_assigned_description =>
      'تم وضع علامة على هذه المركبة للسحب بسبب مخالفة وقوف غير مسددة.';

  @override
  String get all => 'الكل';

  @override
  String get parking => 'الوقوف';

  @override
  String get clamping => 'التثبيت';

  @override
  String get towing => 'السحب';

  @override
  String get current_violations_list => 'قائمة المخالفات الحالية';

  @override
  String get everything_looks_good => 'كل شيء يبدو جيدًا!';

  @override
  String get violations => 'المخالفات';

  @override
  String get vehicles_list => 'قائمة المركبات';

  @override
  String get assign_violation => 'تعيين مخالفة';

  @override
  String get no_data_found => 'لم يتم العثور على بيانات!';

  @override
  String get save_changes => 'حفظ التغييرات';

  @override
  String get full_name_star => 'الاسم الكامل*';

  @override
  String get password_star => 'كلمة المرور*';

  @override
  String get change_password => 'تغيير كلمة المرور';

  @override
  String get please_enter_full_name => 'يرجى إدخال الاسم الكامل';

  @override
  String get update_successful => 'تم التحديث بنجاح';

  @override
  String get profile_update_success_message =>
      'تم تحديث الملف الشخصي بنجاح! تم حفظ\nتغييراتك.';

  @override
  String get current_password_star => 'كلمة المرور الحالية*';

  @override
  String get new_password_star => 'كلمة المرور الجديدة*';

  @override
  String get confirm_new_password_star => 'تأكيد كلمة المرور الجديدة*';

  @override
  String get enter_your_current_password => 'أدخل كلمة المرور الحالية';

  @override
  String get enter_your_new_password => 'أدخل كلمة المرور الجديدة';

  @override
  String get confirm_your_new_password => 'تأكد من كلمة المرور الجديدة';

  @override
  String get save => 'حفظ';

  @override
  String get please_enter_your_current_password =>
      'يرجى إدخال كلمة المرور الحالية';

  @override
  String get please_enter_your_new_password => 'يرجى إدخال كلمة المرور الجديدة';

  @override
  String get please_confirm_your_new_password =>
      'يرجى تأكيد كلمة المرور الجديدة';

  @override
  String get new_password_must_be_different =>
      'يجب أن تكون كلمة المرور الجديدة مختلفة عن كلمة المرور الحالية';

  @override
  String get password_updated => 'تم تحديث كلمة المرور';

  @override
  String get password_updated_message =>
      'تم تغيير كلمة المرور الخاصة بك بنجاح. يرجى استخدام كلمة المرور الجديدة في المرة القادمة التي تسجل فيها الدخول';

  @override
  String get no_violations_found => 'لم يتم العثور على مخالفات';

  @override
  String get no_valet_booked => 'لا يوجد حجز للخدمة الذاتية';

  @override
  String get no_violation_assigned => 'لم يتم تعيين مخالفة';

  @override
  String get assign_valet => 'تعيين خدمة ذاتية';

  @override
  String get valet_person => 'شخص الخدمة الذاتية';

  @override
  String get select => 'اختر';

  @override
  String get location_of_valet_parking => 'موقع وقوف الخدمة الذاتية';

  @override
  String get please_select_valet_person => 'يرجى اختيار شخص الخدمة الذاتية';

  @override
  String get please_select_location_of_valet_parking =>
      'يرجى اختيار موقع وقوف الخدمة الذاتية';

  @override
  String get valet_assigned_successfully => 'تم تعيين الخدمة الذاتية بنجاح';

  @override
  String get valet_assigned_success_message =>
      'تم إخطار شخص الخدمة الذاتية وسيدير\nالمركبة على الفور.';

  @override
  String get generate_ticket => 'إنشاء تذكرة';

  @override
  String get ticket_details => 'تفاصيل التذكرة';

  @override
  String get number_plate => 'رقم اللوحة';

  @override
  String get date => 'التاريخ';

  @override
  String get total_amount => 'المبلغ الإجمالي';

  @override
  String get print_ticket => 'طباعة التذكرة';

  @override
  String get enter_vehicle => 'إدخال المركبة';

  @override
  String get successful => 'ناجح';

  @override
  String get enter_vehicle_description =>
      'تم تسجيل دخول المركبة لحجز الخدمة الذاتية.';

  @override
  String get valet_assign_permission_error_message =>
      'تعيين الخدمة الذاتية يتطلب موافقة مسبقة. يرجى الحصول على الإذن قبل المتابعة.';

  @override
  String get valet_booking_details_view_permission_error_message =>
      'ليس لديك إذن لعرض تفاصيل حجز الخدمة الذاتية. يرجى الحصول على الإذن قبل المتابعة.';

  @override
  String get violation_assign_permission_error_message =>
      'ليس لديك إذن لتعيين المخالفات. يرجى الحصول على الإذن قبل المتابعة.';

  @override
  String get violation_details_view_permission_error_message =>
      'ليس لديك إذن لعرض تفاصيل المخالفة. يرجى الاتصال بالمسؤول للحصول على الوصول.';

  @override
  String get failed_to_generate_ticket => 'فشل في إنشاء التذكرة';

  @override
  String get update_description => 'تحديث الوصف';

  @override
  String get no_vehicles_found => 'لم يتم العثور على مركبات';

  @override
  String get no_vehicles_found_description =>
      'يرجى التحقق مرة أخرى لاحقًا\nأو إضافة مركبة جديدة إلى القائمة.';

  @override
  String get violation_description_star => 'وصف المخالفة*';

  @override
  String get upload_image_star => 'رفع صورة*';

  @override
  String get status_star => 'الحالة*';

  @override
  String get payment_method_star => 'طريقة الدفع*';

  @override
  String get note_star => 'ملاحظة*';

  @override
  String get valet_person_star => 'شخص الخدمة الذاتية*';

  @override
  String get location_of_valet_parking_star => 'موقع وقوف الخدمة الذاتية*';

  @override
  String get search_valet_person => 'البحث عن شخص الخدمة الذاتية';

  @override
  String get search_location_of_valet_parking =>
      'البحث عن موقع وقوف الخدمة الذاتية';

  @override
  String get violation_type => 'نوع المخالفة';

  @override
  String get search_violation_type => 'البحث عن نوع المخالفة';

  @override
  String get clamping_enforcer => 'منفذ التثبيت';

  @override
  String get search_clamping_enforcer => 'البحث عن منفذ التثبيت';

  @override
  String get towing_enforcer => 'منفذ السحب';

  @override
  String get search_towing_enforcer => 'البحث عن منفذ السحب';

  @override
  String get valet_vehicles_list => 'قائمة مركبات الخدمة الذاتية';

  @override
  String get time => 'الوقت';

  @override
  String get settle_ticket => 'تسوية التذكرة';

  @override
  String get request_vehicle => 'طلب المركبة';

  @override
  String get exit_vehicle => 'خروج المركبة';

  @override
  String get no_valet_vehicles_found => 'لم يتم العثور على مركبات خدمة ذاتية';

  @override
  String get please_check_again_later => 'يرجى التحقق مرة أخرى لاحقًا';

  @override
  String get confirm_vehicle_exit => 'تأكيد خروج المركبة';

  @override
  String get confirm_vehicle_exit_description =>
      'سيؤدي هذا الإجراء إلى تحديث حالة الحجز و\nلا يمكن التراجع عنه.';

  @override
  String get confirm => 'تأكيد';

  @override
  String get exit_successful => 'خروج ناجح';

  @override
  String get exit_successful_message =>
      'تم تسليم المركبة وأصبح الحجز الآن مكتملاً.';

  @override
  String get vehicle_request_submitted_successfully =>
      'تم تقديم طلب المركبة بنجاح.';

  @override
  String get vat => 'ضريبة القيمة المضافة';

  @override
  String get amount => 'المبلغ';

  @override
  String get new_clamping_requests => 'طلبات التثبيت الجديدة';

  @override
  String get clamp => 'تثبيت';

  @override
  String get description_star => 'الوصف*';

  @override
  String get please_enter_description => 'يرجى إدخال الوصف';

  @override
  String get before => 'قبل';

  @override
  String get after => 'بعد';

  @override
  String get could_not_find_vehicle => 'لم يتم العثور على المركبة؟';

  @override
  String get vehicle_not_found_description =>
      'لم يتم العثور على المركبة في الموقع المحدد. انقر هنا لإغلاق إجراء التثبيت';

  @override
  String get close_violation => 'إغلاق المخالفة';

  @override
  String get clamping_successful => 'تثبيت ناجح';

  @override
  String get clamping_successful_message =>
      'تم تثبيت المركبة بسبب مخالفات وقوف غير مسددة.';

  @override
  String get close_clamping => 'إغلاق التثبيت';

  @override
  String get scan_qr => 'مسح رمز الاستجابة السريعة';

  @override
  String get note_here => 'ملاحظة هنا...';

  @override
  String note_minimum_chars_validation(Object length) {
    return 'يرجى تقديم ملاحظة تحتوي على $length أحرف على الأقل';
  }

  @override
  String get clamping_procedure_successfully_closed =>
      'تم إغلاق إجراء التثبيت بنجاح.';

  @override
  String get towing_procedure_successfully_closed =>
      'تم إغلاق إجراء السحب بنجاح.';

  @override
  String get new_towing_requests => 'طلبات السحب الجديدة';

  @override
  String get tow => 'سحب';

  @override
  String get close_towing => 'إغلاق السحب';

  @override
  String get towing_successful => 'سحب ناجح';

  @override
  String get towing_successful_message =>
      'تم سحب المركبة بسبب مخالفات وقوف غير مسددة.';

  @override
  String get clamped_vehicles => 'المركبات المثبتة';

  @override
  String get towed_vehicles => 'المركبات المسحوبة';

  @override
  String get clamping_requests => 'طلبات التثبيت';

  @override
  String get towing_requests => 'طلبات السحب';

  @override
  String get requests => 'الطلبات';

  @override
  String get clamped => 'مثبت';

  @override
  String get towed => 'مسحوب';

  @override
  String get assign => 'تعيين';

  @override
  String get no_new_request => 'لا توجد طلبات جديدة';

  @override
  String get no_new_clamping_request_description =>
      'أنت محدث تمامًا!\nلا توجد طلبات تثبيت جديدة الآن.';

  @override
  String get no_new_towing_request_description =>
      'أنت محدث تمامًا!\nلا توجد طلبات سحب جديدة الآن.';

  @override
  String get no_results_found => 'لم يتم العثور على نتائج';

  @override
  String get valet_operations => 'عمليات الخدمة الذاتية';

  @override
  String get valet_request_list => 'قائمة طلبات الخدمة الذاتية';

  @override
  String get search_vehicle_not_found => 'لم يتم العثور على مركبة';

  @override
  String get search_vehicle_not_found_description_one =>
      'لم نتمكن من العثور على أي مركبة تتطابق مع رقم اللوحة:';

  @override
  String get search_vehicle_not_found_description_two =>
      'يرجى التحقق مرة أخرى من رقم اللوحة والمحاولة مرة أخرى.';

  @override
  String get try_again => 'حاول مرة أخرى';

  @override
  String get clamped_date_and_time => 'تاريخ ووقت التثبيت';

  @override
  String get clamped_vehicle_details => 'تفاصيل المركبة المثبتة';

  @override
  String get clamped_by => 'تم التثبيت بواسطة';

  @override
  String get release_vehicle => 'إطلاق المركبة';

  @override
  String get release_clamp_question => 'إطلاق التثبيت؟';

  @override
  String get release_clamp_description =>
      'هل أنت متأكد أنك تريد إطلاق التثبيت لهذه المركبة؟ لا يمكن التراجع عن هذا الإجراء.';

  @override
  String get release_clamp_success_message => 'تم إطلاق المركبة المثبتة.';

  @override
  String get towed_date_and_time => 'تاريخ ووقت السحب';

  @override
  String get no_towed_vehicles => 'لا توجد مركبات مسحوبة';

  @override
  String get no_towed_vehicles_description =>
      'لم يتم العثور على مركبات مسحوبة في النظام.';

  @override
  String get no_clamped_vehicles => 'لا توجد مركبات مثبتة';

  @override
  String get no_clamped_vehicles_description =>
      'لم يتم العثور على مركبات مثبتة في النظام.';

  @override
  String get towed_vehicle_details => 'تفاصيل المركبة المسحوبة';

  @override
  String get towed_by => 'تم السحب بواسطة';

  @override
  String get release_vehicle_question => 'إطلاق المركبة؟';

  @override
  String get release_vehicle_description =>
      'هل أنت متأكد أنك تريد إطلاق المركبة؟ لا يمكن التراجع عن هذا الإجراء.';

  @override
  String get release_tow_success_message => 'تم إطلاق المركبة المسحوبة.';

  @override
  String get update_vehicle_location => 'تحديث موقع المركبة';

  @override
  String get update_vehicle_location_description =>
      'تعديل الموقع الحالي للمركبة المسحوبة.';

  @override
  String get update => 'تحديث';

  @override
  String get confirm_location_update => 'تأكيد تحديث الموقع';

  @override
  String get confirm_location_update_description =>
      'هل أنت متأكد أنك تريد تحديث موقع المركبة المسحوبة؟ سيؤدي هذا الإجراء إلى استبدال الموقع السابق بالموقع الجديد.';

  @override
  String get towed_location_updated_successfully =>
      'تم تحديث موقع المركبة المسحوبة بنجاح.';

  @override
  String get add_new_vehicle => 'إضافة مركبة جديدة';

  @override
  String get select_plate_type_star => 'اختر نوع اللوحة*';

  @override
  String get license_plate_number_star => 'رقم لوحة الترخيص*';

  @override
  String get please_select_plate_type => 'يرجى اختيار نوع اللوحة';

  @override
  String get please_enter_license_plate_number => 'يرجى إدخال رقم لوحة الترخيص';

  @override
  String get or => 'أو';

  @override
  String get scan_plate => 'مسح اللوحة';

  @override
  String get vehicle_name_star => 'اسم المركبة*';

  @override
  String get please_enter_vehicle_name => 'يرجى إدخال اسم المركبة';

  @override
  String get vehicle_registered_country_star => 'البلد المسجل للمركبة*';

  @override
  String get please_select_vehicle_registered_country =>
      'يرجى اختيار البلد المسجل للمركبة';

  @override
  String get make_year => 'سنة الصنع';

  @override
  String get select_year => 'اختر السنة';

  @override
  String get color => 'اللون';

  @override
  String get select_color => 'اختر اللون';

  @override
  String get vehicle_type_star => 'نوع المركبة*';

  @override
  String get select_type => 'اختر النوع';

  @override
  String get please_select_vehicle_type => 'يرجى اختيار نوع المركبة';

  @override
  String get car_image => 'صورة السيارة';

  @override
  String get contact_number => 'رقم الاتصال';

  @override
  String get enter_mobile_number => 'أدخل رقم الهاتف المحمول';

  @override
  String get search_country => 'البحث عن البلد';

  @override
  String get select_country => 'اختر البلد';

  @override
  String get license_plate_number => 'رقم لوحة الترخيص';

  @override
  String get vehicle_registered_country => 'البلد المسجل للمركبة';

  @override
  String get search_color => 'البحث عن اللون';

  @override
  String get vehicle_type => 'نوع المركبة';

  @override
  String get search_type => 'البحث عن النوع';

  @override
  String get save_vehicle => 'حفظ المركبة';

  @override
  String get enter_plate_number => 'أدخل رقم اللوحة';

  @override
  String get enter_plate_letters => 'أدخل حروف اللوحة';

  @override
  String get english_and_arabic_plate_letters_should_match =>
      'يجب أن تتطابق حروف اللوحة باللغتين الإنجليزية والعربية.';

  @override
  String get please_enter_valid_mobile_number =>
      'يرجى إدخال رقم هاتف محمول صالح';

  @override
  String get confirm_vehicle_license_plate => 'تأكيد لوحة ترخيص المركبة';

  @override
  String get confirm_and_save => 'تأكيد وحفظ';

  @override
  String get back_to_edit => 'العودة للتعديل';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get today => 'اليوم';

  @override
  String get yesterday => 'أمس';

  @override
  String get admin => 'المسؤول';

  @override
  String get valet => 'الخدمة الذاتية';

  @override
  String get no_notifications => 'لا توجد إشعارات';

  @override
  String get no_notifications_description => 'لا توجد إشعارات في الوقت الحالي.';

  @override
  String get press_back_again_to_close => 'اضغط على زر العودة مرة أخرى للإغلاق';

  @override
  String get not_available => 'غير متوفر';

  @override
  String get assigned => 'مُكَلَّف';

  @override
  String get vehicles => 'المركبات';

  @override
  String get assigned_clamping_request => 'طلب التثبيت المخصص';

  @override
  String get assigned_towing_request => 'طلب سحب مخصص';

  @override
  String get search_status => 'حالة البحث';

  @override
  String get search_payment_method => 'بحث طريقة الدفع';

  @override
  String get no_request_assigned => 'لا توجد طلبات معينة من قبلك';

  @override
  String get no_request_assigned_description =>
      'لم تُخصِّص أي طلبات بعد. خصِّص طلبًا لتتبع التقدم.';

  @override
  String get connection_lost => 'تم فقدان الاتصال';

  @override
  String get connection_lost_description =>
      'يرجى التحقق من اتصالك بالإنترنت ثم المحاولة مرة أخرى.';

  @override
  String get headsup => 'انتبه!';

  @override
  String get okay => 'موافق';

  @override
  String get printer_not_detected_title => 'لنوصل الطابعة الخاصة بك';

  @override
  String get printer_not_detected_description =>
      'لم يتم اكتشاف طابعة. يرجى التأكد من أن SPP-R310 مقترنة ومفعلة.';

  @override
  String get printing_issue_title => 'مشكلة في الطباعة';

  @override
  String get printing_issue_description =>
      'لم نتمكن من طباعة تذكرتك. يرجى التأكد من اتصال الطابعة والمحاولة مرة أخرى.';

  @override
  String get sar => 'ريال';

  @override
  String get gallery => 'المعرض';

  @override
  String get camera => 'الكاميرا';

  @override
  String get arabic => 'العربية';

  @override
  String get english => 'الإنجليزية';

  @override
  String get no_valet_assigned =>
      'لم يتم تعيين خادم بعد. يرجى تعيين خادم للمتابعة.';

  @override
  String get bluetooth_not_enabled => 'البلوتوث غير مفعل';

  @override
  String get bluetooth_not_enabled_description =>
      'لطباعة تذكرة، يرجى تفعيل البلوتوث والاتصال بالطابعة.';

  @override
  String get enable_bluetooth => 'تفعيل البلوتوث';

  @override
  String get add => 'إضافة';

  @override
  String get vehicle_name_hint_text => 'مثال. تويوتا برادو';

  @override
  String get add_more => 'أضف المزيد';

  @override
  String get did_not_receive_the_otp => 'لم تصلك الرمز؟';

  @override
  String get resend_otp => 'إعادة إرسال الرمز';
}
