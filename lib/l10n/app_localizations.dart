import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en')
  ];

  /// No description provided for @splashScreen.
  ///
  /// In en, this message translates to:
  /// **'Splash Screen!'**
  String get splashScreen;

  /// No description provided for @chooseYourPreferredLanguage.
  ///
  /// In en, this message translates to:
  /// **'Choose Your Preferred Language'**
  String get chooseYourPreferredLanguage;

  /// No description provided for @continueString.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueString;

  /// No description provided for @signIn.
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signIn;

  /// No description provided for @signInDescription.
  ///
  /// In en, this message translates to:
  /// **'Log in to your operator account to efficiently manage parking spaces, monitor activities, and ensure smooth operations.'**
  String get signInDescription;

  /// No description provided for @usernameOrEmail.
  ///
  /// In en, this message translates to:
  /// **'Username or Email'**
  String get usernameOrEmail;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @forgotPasswordLink.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password?'**
  String get forgotPasswordLink;

  /// No description provided for @forgotPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password'**
  String get forgotPassword;

  /// No description provided for @pleaseEnterYourEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter your email'**
  String get pleaseEnterYourEmail;

  /// No description provided for @pleaseEnterAValidEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email'**
  String get pleaseEnterAValidEmail;

  /// No description provided for @pleaseEnterYourPassword.
  ///
  /// In en, this message translates to:
  /// **'Please enter your password'**
  String get pleaseEnterYourPassword;

  /// No description provided for @pleaseEnterAValidPassword.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid password'**
  String get pleaseEnterAValidPassword;

  /// No description provided for @searchVehicleNumber.
  ///
  /// In en, this message translates to:
  /// **'Search vehicle number...'**
  String get searchVehicleNumber;

  /// No description provided for @currentParkedVehicles.
  ///
  /// In en, this message translates to:
  /// **'Current Parked Vehicles'**
  String get currentParkedVehicles;

  /// No description provided for @currentViolations.
  ///
  /// In en, this message translates to:
  /// **'Current\nViolations'**
  String get currentViolations;

  /// No description provided for @currentViolationsTitle.
  ///
  /// In en, this message translates to:
  /// **'Current Violations'**
  String get currentViolationsTitle;

  /// No description provided for @currentValetVehicles.
  ///
  /// In en, this message translates to:
  /// **'Current Valet Vehicles'**
  String get currentValetVehicles;

  /// No description provided for @quickActions.
  ///
  /// In en, this message translates to:
  /// **'Quick Actions'**
  String get quickActions;

  /// No description provided for @violationsAndNotices.
  ///
  /// In en, this message translates to:
  /// **'Violations & Notices'**
  String get violationsAndNotices;

  /// No description provided for @clampVehicle.
  ///
  /// In en, this message translates to:
  /// **'Clamp Vehicle'**
  String get clampVehicle;

  /// No description provided for @towVehicle.
  ///
  /// In en, this message translates to:
  /// **'Tow Vehicle'**
  String get towVehicle;

  /// No description provided for @valetOperation.
  ///
  /// In en, this message translates to:
  /// **'Valet Operation'**
  String get valetOperation;

  /// No description provided for @addVehicle.
  ///
  /// In en, this message translates to:
  /// **'Add Vehicle'**
  String get addVehicle;

  /// No description provided for @forgot_password_description.
  ///
  /// In en, this message translates to:
  /// **'Choose a contact method to reset\nyour password.'**
  String get forgot_password_description;

  /// No description provided for @via_email.
  ///
  /// In en, this message translates to:
  /// **'Via Email'**
  String get via_email;

  /// No description provided for @contact_admin.
  ///
  /// In en, this message translates to:
  /// **'Contact Admin'**
  String get contact_admin;

  /// No description provided for @get_otp.
  ///
  /// In en, this message translates to:
  /// **'Get OTP'**
  String get get_otp;

  /// No description provided for @enter_the_otp.
  ///
  /// In en, this message translates to:
  /// **'Enter The OTP'**
  String get enter_the_otp;

  /// No description provided for @enter_the_four_digit_code_description.
  ///
  /// In en, this message translates to:
  /// **'Enter the 4 digit code sent to you at your registered\nemail address'**
  String get enter_the_four_digit_code_description;

  /// No description provided for @verify.
  ///
  /// In en, this message translates to:
  /// **'Verify'**
  String get verify;

  /// No description provided for @confirm_password.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirm_password;

  /// No description provided for @reset_password.
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get reset_password;

  /// No description provided for @create_new_password.
  ///
  /// In en, this message translates to:
  /// **'Create New Password'**
  String get create_new_password;

  /// No description provided for @create_new_password_description.
  ///
  /// In en, this message translates to:
  /// **'You have successfully reset your password.\nPlease use your new password when logging in.'**
  String get create_new_password_description;

  /// No description provided for @back_to_sign_in.
  ///
  /// In en, this message translates to:
  /// **'Back to Sign In'**
  String get back_to_sign_in;

  /// No description provided for @connect.
  ///
  /// In en, this message translates to:
  /// **'Connect'**
  String get connect;

  /// No description provided for @connect_with_admin.
  ///
  /// In en, this message translates to:
  /// **'Connect with admin'**
  String get connect_with_admin;

  /// No description provided for @connect_with_admin_description.
  ///
  /// In en, this message translates to:
  /// **'Would you like to connect with admin?'**
  String get connect_with_admin_description;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @send_otp.
  ///
  /// In en, this message translates to:
  /// **'Send OTP'**
  String get send_otp;

  /// No description provided for @would_you_like_to_get_otp.
  ///
  /// In en, this message translates to:
  /// **'Would you like to get the OTP to'**
  String get would_you_like_to_get_otp;

  /// No description provided for @somethingWentWrong.
  ///
  /// In en, this message translates to:
  /// **'Something went wrong'**
  String get somethingWentWrong;

  /// No description provided for @pleaseEnterAValidOTP.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid OTP.'**
  String get pleaseEnterAValidOTP;

  /// No description provided for @invalidOtpPleaseTryAgain.
  ///
  /// In en, this message translates to:
  /// **'Invalid OTP. Please try again.'**
  String get invalidOtpPleaseTryAgain;

  /// No description provided for @anErrorOccurredPleaseTryAgain.
  ///
  /// In en, this message translates to:
  /// **'An error occurred. Please try again.'**
  String get anErrorOccurredPleaseTryAgain;

  /// No description provided for @passwordReset.
  ///
  /// In en, this message translates to:
  /// **'Your new password must be different from\npreviously used password'**
  String get passwordReset;

  /// No description provided for @contact.
  ///
  /// In en, this message translates to:
  /// **'Contact'**
  String get contact;

  /// No description provided for @passwordMustbeAtLeast8CharactersLong.
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 8 characters long.'**
  String get passwordMustbeAtLeast8CharactersLong;

  /// No description provided for @passwordMustHaveAtLeastOneUppercase.
  ///
  /// In en, this message translates to:
  /// **'Password must have at least one uppercase letter.'**
  String get passwordMustHaveAtLeastOneUppercase;

  /// No description provided for @passwordMustHaveAtLeastOneLowercase.
  ///
  /// In en, this message translates to:
  /// **'Password must have at least one lowercase letter.'**
  String get passwordMustHaveAtLeastOneLowercase;

  /// No description provided for @passwordMustHaveAtLeastOneNumber.
  ///
  /// In en, this message translates to:
  /// **'Password must have at least one number.'**
  String get passwordMustHaveAtLeastOneNumber;

  /// No description provided for @passwordMustHaveAtLeastOneSpecialCharacter.
  ///
  /// In en, this message translates to:
  /// **'Password must have at least one special character.'**
  String get passwordMustHaveAtLeastOneSpecialCharacter;

  /// No description provided for @passwordsDontMatch.
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match.'**
  String get passwordsDontMatch;

  /// No description provided for @networkError.
  ///
  /// In en, this message translates to:
  /// **'Network error. Please check your connection and try again'**
  String get networkError;

  /// No description provided for @notAssigned.
  ///
  /// In en, this message translates to:
  /// **'Not Assigned'**
  String get notAssigned;

  /// No description provided for @contactAdmin.
  ///
  /// In en, this message translates to:
  /// **'Contact Admin'**
  String get contactAdmin;

  /// No description provided for @contactAdminDescription.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to contact the admin? This action will open your phone\'s dialer, and you can proceed to call the admin for assistance. Standard call charges may apply.'**
  String get contactAdminDescription;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @services.
  ///
  /// In en, this message translates to:
  /// **'Services'**
  String get services;

  /// No description provided for @id.
  ///
  /// In en, this message translates to:
  /// **'ID :'**
  String get id;

  /// No description provided for @checkInTime.
  ///
  /// In en, this message translates to:
  /// **'Check-in time'**
  String get checkInTime;

  /// No description provided for @totalHrs.
  ///
  /// In en, this message translates to:
  /// **'Total Hrs'**
  String get totalHrs;

  /// No description provided for @subscriptionDetails.
  ///
  /// In en, this message translates to:
  /// **'Subscription Details'**
  String get subscriptionDetails;

  /// No description provided for @subscriptionName.
  ///
  /// In en, this message translates to:
  /// **'Subscription Name'**
  String get subscriptionName;

  /// No description provided for @location.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// No description provided for @type.
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get type;

  /// No description provided for @startAndEndTime.
  ///
  /// In en, this message translates to:
  /// **'Start & End Time'**
  String get startAndEndTime;

  /// No description provided for @expiryDate.
  ///
  /// In en, this message translates to:
  /// **'Expiry Date'**
  String get expiryDate;

  /// No description provided for @searchedVehiclesNotFound.
  ///
  /// In en, this message translates to:
  /// **'We couldn\'t find any details for the entered vehicle number. Please check back later or add a new vehicle to the list.'**
  String get searchedVehiclesNotFound;

  /// No description provided for @noParkedVehiclesFound.
  ///
  /// In en, this message translates to:
  /// **'No vehicles are currently parked. Please check back later or add a new vehicle to the list.'**
  String get noParkedVehiclesFound;

  /// No description provided for @shared.
  ///
  /// In en, this message translates to:
  /// **'Shared'**
  String get shared;

  /// No description provided for @owner.
  ///
  /// In en, this message translates to:
  /// **'Owner'**
  String get owner;

  /// No description provided for @vehicleNotFoundInDatabase.
  ///
  /// In en, this message translates to:
  /// **'The scanned plate number does not match any registered vehicle in our system.'**
  String get vehicleNotFoundInDatabase;

  /// No description provided for @vehicleDataNotFound.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Data Not Found'**
  String get vehicleDataNotFound;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @terms_and_conditions.
  ///
  /// In en, this message translates to:
  /// **'Terms & Conditions'**
  String get terms_and_conditions;

  /// No description provided for @privacy_policies.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policies'**
  String get privacy_policies;

  /// No description provided for @notification_settings.
  ///
  /// In en, this message translates to:
  /// **'Notification Settings'**
  String get notification_settings;

  /// No description provided for @faqs.
  ///
  /// In en, this message translates to:
  /// **'FAQ’s'**
  String get faqs;

  /// No description provided for @contact_us.
  ///
  /// In en, this message translates to:
  /// **'Contact Us'**
  String get contact_us;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Log Out'**
  String get logout;

  /// No description provided for @my_profile.
  ///
  /// In en, this message translates to:
  /// **'My Profile'**
  String get my_profile;

  /// No description provided for @logout_confirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to log out?'**
  String get logout_confirmation;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @assignNewViolation.
  ///
  /// In en, this message translates to:
  /// **'Assign New Violation'**
  String get assignNewViolation;

  /// No description provided for @violation_type_star.
  ///
  /// In en, this message translates to:
  /// **'Violation Type*'**
  String get violation_type_star;

  /// No description provided for @select_violation_type.
  ///
  /// In en, this message translates to:
  /// **'Select Violation Type'**
  String get select_violation_type;

  /// No description provided for @violation_description.
  ///
  /// In en, this message translates to:
  /// **'Violation Description'**
  String get violation_description;

  /// No description provided for @description_here.
  ///
  /// In en, this message translates to:
  /// **'Description here...'**
  String get description_here;

  /// No description provided for @upload_image.
  ///
  /// In en, this message translates to:
  /// **'Upload Image'**
  String get upload_image;

  /// No description provided for @upload_a_picture.
  ///
  /// In en, this message translates to:
  /// **'Upload a picture'**
  String get upload_a_picture;

  /// No description provided for @submit.
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submit;

  /// No description provided for @select_source.
  ///
  /// In en, this message translates to:
  /// **'Select Source'**
  String get select_source;

  /// No description provided for @please_select_violation_type.
  ///
  /// In en, this message translates to:
  /// **'Please select violation type'**
  String get please_select_violation_type;

  /// No description provided for @please_enter_violation_description.
  ///
  /// In en, this message translates to:
  /// **'Please enter violation description'**
  String get please_enter_violation_description;

  /// No description provided for @please_upload_image.
  ///
  /// In en, this message translates to:
  /// **'Please upload image'**
  String get please_upload_image;

  /// No description provided for @violation_assigned.
  ///
  /// In en, this message translates to:
  /// **'Violation Assigned'**
  String get violation_assigned;

  /// No description provided for @violation_assigned_description.
  ///
  /// In en, this message translates to:
  /// **'A parking violation has been successfully\nassigned to this vehicle.'**
  String get violation_assigned_description;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// No description provided for @assign_clamping.
  ///
  /// In en, this message translates to:
  /// **'Assign Clamping'**
  String get assign_clamping;

  /// No description provided for @clamping_enforcer_star.
  ///
  /// In en, this message translates to:
  /// **'Clamping Enforcer*'**
  String get clamping_enforcer_star;

  /// No description provided for @select_clamping_enforcer.
  ///
  /// In en, this message translates to:
  /// **'Select Clamping Enforcer'**
  String get select_clamping_enforcer;

  /// No description provided for @please_select_clamping_enforcer.
  ///
  /// In en, this message translates to:
  /// **'Please select clamping enforcer'**
  String get please_select_clamping_enforcer;

  /// No description provided for @clamping_assigned.
  ///
  /// In en, this message translates to:
  /// **'Clamping Assigned'**
  String get clamping_assigned;

  /// No description provided for @clamping_assigned_description.
  ///
  /// In en, this message translates to:
  /// **'This vehicle has been flagged for clamping\ndue to a unsettled parking violation.'**
  String get clamping_assigned_description;

  /// No description provided for @direct_clamping_assigned_description.
  ///
  /// In en, this message translates to:
  /// **'This vehicle has been flagged for clamping\ndue to a parking violation.'**
  String get direct_clamping_assigned_description;

  /// No description provided for @violation_details.
  ///
  /// In en, this message translates to:
  /// **'Violation Details'**
  String get violation_details;

  /// No description provided for @fined_amount.
  ///
  /// In en, this message translates to:
  /// **'Fined Amount'**
  String get fined_amount;

  /// No description provided for @grace_period.
  ///
  /// In en, this message translates to:
  /// **'Grace Period'**
  String get grace_period;

  /// No description provided for @violation_reported_by.
  ///
  /// In en, this message translates to:
  /// **'Violation Reported By'**
  String get violation_reported_by;

  /// No description provided for @next_action_in.
  ///
  /// In en, this message translates to:
  /// **'Next Action in'**
  String get next_action_in;

  /// No description provided for @violation_images.
  ///
  /// In en, this message translates to:
  /// **'Violation Images'**
  String get violation_images;

  /// No description provided for @violation_date_and_time.
  ///
  /// In en, this message translates to:
  /// **'Violation Date & Time'**
  String get violation_date_and_time;

  /// No description provided for @settle_violation.
  ///
  /// In en, this message translates to:
  /// **'Settle Violation'**
  String get settle_violation;

  /// No description provided for @status.
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// No description provided for @payment_method.
  ///
  /// In en, this message translates to:
  /// **'Payment Method'**
  String get payment_method;

  /// No description provided for @note.
  ///
  /// In en, this message translates to:
  /// **'Note'**
  String get note;

  /// No description provided for @settled.
  ///
  /// In en, this message translates to:
  /// **'Settled'**
  String get settled;

  /// No description provided for @pos_machine.
  ///
  /// In en, this message translates to:
  /// **'POS Machine'**
  String get pos_machine;

  /// No description provided for @customer_have_settled_ticket.
  ///
  /// In en, this message translates to:
  /// **'Customer have settled ticket'**
  String get customer_have_settled_ticket;

  /// No description provided for @please_select_settlement_status.
  ///
  /// In en, this message translates to:
  /// **'Please select settlement status'**
  String get please_select_settlement_status;

  /// No description provided for @please_select_payment_method.
  ///
  /// In en, this message translates to:
  /// **'Please select payment method'**
  String get please_select_payment_method;

  /// No description provided for @please_enter_note.
  ///
  /// In en, this message translates to:
  /// **'Please enter note'**
  String get please_enter_note;

  /// No description provided for @settle_violation_note_minimum_chars_validation.
  ///
  /// In en, this message translates to:
  /// **'Please provide a note with at least {length} characters to explain the settlement'**
  String settle_violation_note_minimum_chars_validation(Object length);

  /// No description provided for @violation_settled_successfully.
  ///
  /// In en, this message translates to:
  /// **'Violation Settled\nSuccessfully'**
  String get violation_settled_successfully;

  /// No description provided for @violation_settled_success_message.
  ///
  /// In en, this message translates to:
  /// **'The issue has been resolved, and the vehicle is\ncleared from the violation status.'**
  String get violation_settled_success_message;

  /// No description provided for @violation_list.
  ///
  /// In en, this message translates to:
  /// **'Violations List'**
  String get violation_list;

  /// No description provided for @valet_booking_list.
  ///
  /// In en, this message translates to:
  /// **'Valet Booking List'**
  String get valet_booking_list;

  /// No description provided for @vehicle_details.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Details'**
  String get vehicle_details;

  /// No description provided for @scanNumberPlate.
  ///
  /// In en, this message translates to:
  /// **'Scan Number Plate'**
  String get scanNumberPlate;

  /// No description provided for @scannedNumberPlate.
  ///
  /// In en, this message translates to:
  /// **'Scanned Plate Number'**
  String get scannedNumberPlate;

  /// No description provided for @assign_towing.
  ///
  /// In en, this message translates to:
  /// **'Assign Towing'**
  String get assign_towing;

  /// No description provided for @towing_enforcer_star.
  ///
  /// In en, this message translates to:
  /// **'Towing Enforcer*'**
  String get towing_enforcer_star;

  /// No description provided for @select_towing_enforcer.
  ///
  /// In en, this message translates to:
  /// **'Select Towing Enforcer'**
  String get select_towing_enforcer;

  /// No description provided for @please_select_towing_enforcer.
  ///
  /// In en, this message translates to:
  /// **'Please select towing enforcer'**
  String get please_select_towing_enforcer;

  /// No description provided for @towing_assigned.
  ///
  /// In en, this message translates to:
  /// **'Towing Assigned'**
  String get towing_assigned;

  /// No description provided for @towing_assigned_description.
  ///
  /// In en, this message translates to:
  /// **'This vehicle has been flagged for towing due to a unsettled parking violation.'**
  String get towing_assigned_description;

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @parking.
  ///
  /// In en, this message translates to:
  /// **'Parking'**
  String get parking;

  /// No description provided for @clamping.
  ///
  /// In en, this message translates to:
  /// **'Clamping'**
  String get clamping;

  /// No description provided for @towing.
  ///
  /// In en, this message translates to:
  /// **'Towing'**
  String get towing;

  /// No description provided for @current_violations_list.
  ///
  /// In en, this message translates to:
  /// **'Current Violations List'**
  String get current_violations_list;

  /// No description provided for @everything_looks_good.
  ///
  /// In en, this message translates to:
  /// **'Everything looks good!'**
  String get everything_looks_good;

  /// No description provided for @violations.
  ///
  /// In en, this message translates to:
  /// **'Violations'**
  String get violations;

  /// No description provided for @vehicles_list.
  ///
  /// In en, this message translates to:
  /// **'Vehicles List'**
  String get vehicles_list;

  /// No description provided for @assign_violation.
  ///
  /// In en, this message translates to:
  /// **'Assign Violation'**
  String get assign_violation;

  /// No description provided for @no_data_found.
  ///
  /// In en, this message translates to:
  /// **'No data found!'**
  String get no_data_found;

  /// No description provided for @save_changes.
  ///
  /// In en, this message translates to:
  /// **'Save Changes'**
  String get save_changes;

  /// No description provided for @full_name_star.
  ///
  /// In en, this message translates to:
  /// **'Full Name*'**
  String get full_name_star;

  /// No description provided for @password_star.
  ///
  /// In en, this message translates to:
  /// **'Password*'**
  String get password_star;

  /// No description provided for @change_password.
  ///
  /// In en, this message translates to:
  /// **'Change Password'**
  String get change_password;

  /// No description provided for @please_enter_full_name.
  ///
  /// In en, this message translates to:
  /// **'Please enter full name'**
  String get please_enter_full_name;

  /// No description provided for @update_successful.
  ///
  /// In en, this message translates to:
  /// **'Update Successful'**
  String get update_successful;

  /// No description provided for @profile_update_success_message.
  ///
  /// In en, this message translates to:
  /// **'Profile Updated Successfully! Your\nchanges have been saved.'**
  String get profile_update_success_message;

  /// No description provided for @current_password_star.
  ///
  /// In en, this message translates to:
  /// **'Current Password*'**
  String get current_password_star;

  /// No description provided for @new_password_star.
  ///
  /// In en, this message translates to:
  /// **'New Password*'**
  String get new_password_star;

  /// No description provided for @confirm_new_password_star.
  ///
  /// In en, this message translates to:
  /// **'Confirm New Password*'**
  String get confirm_new_password_star;

  /// No description provided for @enter_your_current_password.
  ///
  /// In en, this message translates to:
  /// **'Enter your current password'**
  String get enter_your_current_password;

  /// No description provided for @enter_your_new_password.
  ///
  /// In en, this message translates to:
  /// **'Enter your new password'**
  String get enter_your_new_password;

  /// No description provided for @confirm_your_new_password.
  ///
  /// In en, this message translates to:
  /// **'Confirm your new password'**
  String get confirm_your_new_password;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @please_enter_your_current_password.
  ///
  /// In en, this message translates to:
  /// **'Please enter your current password'**
  String get please_enter_your_current_password;

  /// No description provided for @please_enter_your_new_password.
  ///
  /// In en, this message translates to:
  /// **'Please enter your new password'**
  String get please_enter_your_new_password;

  /// No description provided for @please_confirm_your_new_password.
  ///
  /// In en, this message translates to:
  /// **'Please confirm your new password'**
  String get please_confirm_your_new_password;

  /// No description provided for @new_password_must_be_different.
  ///
  /// In en, this message translates to:
  /// **'New password must be different from current password'**
  String get new_password_must_be_different;

  /// No description provided for @password_updated.
  ///
  /// In en, this message translates to:
  /// **'Password Updated'**
  String get password_updated;

  /// No description provided for @password_updated_message.
  ///
  /// In en, this message translates to:
  /// **'Your password has been successfully changed. Please use your new password the next time you log in'**
  String get password_updated_message;

  /// No description provided for @no_violations_found.
  ///
  /// In en, this message translates to:
  /// **'No Violations Found'**
  String get no_violations_found;

  /// No description provided for @no_valet_booked.
  ///
  /// In en, this message translates to:
  /// **'No Valet Booked'**
  String get no_valet_booked;

  /// No description provided for @no_violation_assigned.
  ///
  /// In en, this message translates to:
  /// **'No Violation Assigned'**
  String get no_violation_assigned;

  /// No description provided for @assign_valet.
  ///
  /// In en, this message translates to:
  /// **'Assign Valet'**
  String get assign_valet;

  /// No description provided for @valet_person.
  ///
  /// In en, this message translates to:
  /// **'Valet Person'**
  String get valet_person;

  /// No description provided for @select.
  ///
  /// In en, this message translates to:
  /// **'Select'**
  String get select;

  /// No description provided for @location_of_valet_parking.
  ///
  /// In en, this message translates to:
  /// **'Location of Valet Parking'**
  String get location_of_valet_parking;

  /// No description provided for @please_select_valet_person.
  ///
  /// In en, this message translates to:
  /// **'Please select valet person'**
  String get please_select_valet_person;

  /// No description provided for @please_select_location_of_valet_parking.
  ///
  /// In en, this message translates to:
  /// **'Please select location of valet parking'**
  String get please_select_location_of_valet_parking;

  /// No description provided for @valet_assigned_successfully.
  ///
  /// In en, this message translates to:
  /// **'Valet Assigned Successfully'**
  String get valet_assigned_successfully;

  /// No description provided for @valet_assigned_success_message.
  ///
  /// In en, this message translates to:
  /// **'The valet has been notified and will manage\nthe vehicle promptly.'**
  String get valet_assigned_success_message;

  /// No description provided for @generate_ticket.
  ///
  /// In en, this message translates to:
  /// **'Generate Ticket'**
  String get generate_ticket;

  /// No description provided for @ticket_details.
  ///
  /// In en, this message translates to:
  /// **'Ticket Details'**
  String get ticket_details;

  /// No description provided for @number_plate.
  ///
  /// In en, this message translates to:
  /// **'Number Plate'**
  String get number_plate;

  /// No description provided for @date.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// No description provided for @total_amount.
  ///
  /// In en, this message translates to:
  /// **'Total Amount'**
  String get total_amount;

  /// No description provided for @print_ticket.
  ///
  /// In en, this message translates to:
  /// **'Print Ticket'**
  String get print_ticket;

  /// No description provided for @enter_vehicle.
  ///
  /// In en, this message translates to:
  /// **'Enter Vehicle'**
  String get enter_vehicle;

  /// No description provided for @successful.
  ///
  /// In en, this message translates to:
  /// **'Successful'**
  String get successful;

  /// No description provided for @enter_vehicle_description.
  ///
  /// In en, this message translates to:
  /// **'The vehicle has been checked in for the valet booking.'**
  String get enter_vehicle_description;

  /// No description provided for @valet_assign_permission_error_message.
  ///
  /// In en, this message translates to:
  /// **'Valet assignment requires prior approval. Please get permission before proceeding.'**
  String get valet_assign_permission_error_message;

  /// No description provided for @valet_booking_details_view_permission_error_message.
  ///
  /// In en, this message translates to:
  /// **'You don\'t have permission to view valet booking details. Please get permission before proceeding.'**
  String get valet_booking_details_view_permission_error_message;

  /// No description provided for @violation_assign_permission_error_message.
  ///
  /// In en, this message translates to:
  /// **'You don\'t have permission to assign violations. Please get permission before proceeding.'**
  String get violation_assign_permission_error_message;

  /// No description provided for @violation_details_view_permission_error_message.
  ///
  /// In en, this message translates to:
  /// **'You don\'t have permission to view violation details. Please contact the admin for access.'**
  String get violation_details_view_permission_error_message;

  /// No description provided for @failed_to_generate_ticket.
  ///
  /// In en, this message translates to:
  /// **'Failed to generate ticket'**
  String get failed_to_generate_ticket;

  /// No description provided for @update_description.
  ///
  /// In en, this message translates to:
  /// **'Update Description'**
  String get update_description;

  /// No description provided for @no_vehicles_found.
  ///
  /// In en, this message translates to:
  /// **'No vehicles found'**
  String get no_vehicles_found;

  /// No description provided for @no_vehicles_found_description.
  ///
  /// In en, this message translates to:
  /// **'Please check back later\nor add a new vehicle to the list.'**
  String get no_vehicles_found_description;

  /// No description provided for @violation_description_star.
  ///
  /// In en, this message translates to:
  /// **'Violation Description*'**
  String get violation_description_star;

  /// No description provided for @upload_image_star.
  ///
  /// In en, this message translates to:
  /// **'Upload Image*'**
  String get upload_image_star;

  /// No description provided for @status_star.
  ///
  /// In en, this message translates to:
  /// **'Status*'**
  String get status_star;

  /// No description provided for @payment_method_star.
  ///
  /// In en, this message translates to:
  /// **'Payment Method*'**
  String get payment_method_star;

  /// No description provided for @note_star.
  ///
  /// In en, this message translates to:
  /// **'Note*'**
  String get note_star;

  /// No description provided for @valet_person_star.
  ///
  /// In en, this message translates to:
  /// **'Valet Person*'**
  String get valet_person_star;

  /// No description provided for @location_of_valet_parking_star.
  ///
  /// In en, this message translates to:
  /// **'Location of Valet Parking*'**
  String get location_of_valet_parking_star;

  /// No description provided for @search_valet_person.
  ///
  /// In en, this message translates to:
  /// **'Search Valet Person'**
  String get search_valet_person;

  /// No description provided for @search_location_of_valet_parking.
  ///
  /// In en, this message translates to:
  /// **'Search Location Of Valet Parking'**
  String get search_location_of_valet_parking;

  /// No description provided for @violation_type.
  ///
  /// In en, this message translates to:
  /// **'Violation Type'**
  String get violation_type;

  /// No description provided for @search_violation_type.
  ///
  /// In en, this message translates to:
  /// **'Search Violation Type'**
  String get search_violation_type;

  /// No description provided for @clamping_enforcer.
  ///
  /// In en, this message translates to:
  /// **'Clamping Enforcer'**
  String get clamping_enforcer;

  /// No description provided for @search_clamping_enforcer.
  ///
  /// In en, this message translates to:
  /// **'Search Clamping Enforcer'**
  String get search_clamping_enforcer;

  /// No description provided for @towing_enforcer.
  ///
  /// In en, this message translates to:
  /// **'Towing Enforcer'**
  String get towing_enforcer;

  /// No description provided for @search_towing_enforcer.
  ///
  /// In en, this message translates to:
  /// **'Search Towing Enforcer'**
  String get search_towing_enforcer;

  /// No description provided for @valet_vehicles_list.
  ///
  /// In en, this message translates to:
  /// **'Valet Vehicles List'**
  String get valet_vehicles_list;

  /// No description provided for @time.
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get time;

  /// No description provided for @settle_ticket.
  ///
  /// In en, this message translates to:
  /// **'Settle Ticket'**
  String get settle_ticket;

  /// No description provided for @request_vehicle.
  ///
  /// In en, this message translates to:
  /// **'Request Vehicle'**
  String get request_vehicle;

  /// No description provided for @exit_vehicle.
  ///
  /// In en, this message translates to:
  /// **'Exit Vehicle'**
  String get exit_vehicle;

  /// No description provided for @no_valet_vehicles_found.
  ///
  /// In en, this message translates to:
  /// **'No Valet Vehicles Found'**
  String get no_valet_vehicles_found;

  /// No description provided for @please_check_again_later.
  ///
  /// In en, this message translates to:
  /// **'Please check again later'**
  String get please_check_again_later;

  /// No description provided for @confirm_vehicle_exit.
  ///
  /// In en, this message translates to:
  /// **'Confirm Vehicle Exit'**
  String get confirm_vehicle_exit;

  /// No description provided for @confirm_vehicle_exit_description.
  ///
  /// In en, this message translates to:
  /// **'This action will update the booking status and\ncannot be undone.'**
  String get confirm_vehicle_exit_description;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @exit_successful.
  ///
  /// In en, this message translates to:
  /// **'Exit Successful'**
  String get exit_successful;

  /// No description provided for @exit_successful_message.
  ///
  /// In en, this message translates to:
  /// **'The vehicle has been handed over and the\nbooking is now complete.'**
  String get exit_successful_message;

  /// No description provided for @vehicle_request_submitted_successfully.
  ///
  /// In en, this message translates to:
  /// **'Vehicle request submitted successfully.'**
  String get vehicle_request_submitted_successfully;

  /// No description provided for @vat.
  ///
  /// In en, this message translates to:
  /// **'VAT'**
  String get vat;

  /// No description provided for @amount.
  ///
  /// In en, this message translates to:
  /// **'Amount'**
  String get amount;

  /// No description provided for @new_clamping_requests.
  ///
  /// In en, this message translates to:
  /// **'New Clamping Requests'**
  String get new_clamping_requests;

  /// No description provided for @clamp.
  ///
  /// In en, this message translates to:
  /// **'Clamp'**
  String get clamp;

  /// No description provided for @description_star.
  ///
  /// In en, this message translates to:
  /// **'Description*'**
  String get description_star;

  /// No description provided for @please_enter_description.
  ///
  /// In en, this message translates to:
  /// **'Please enter description'**
  String get please_enter_description;

  /// No description provided for @before.
  ///
  /// In en, this message translates to:
  /// **'Before'**
  String get before;

  /// No description provided for @after.
  ///
  /// In en, this message translates to:
  /// **'After'**
  String get after;

  /// No description provided for @could_not_find_vehicle.
  ///
  /// In en, this message translates to:
  /// **'Couldn’t find Vehicle ?'**
  String get could_not_find_vehicle;

  /// No description provided for @vehicle_not_found_description.
  ///
  /// In en, this message translates to:
  /// **'Vehicle not found at the specified location. Click here to close clamping procedure'**
  String get vehicle_not_found_description;

  /// No description provided for @close_violation.
  ///
  /// In en, this message translates to:
  /// **'Close Violation'**
  String get close_violation;

  /// No description provided for @clamping_successful.
  ///
  /// In en, this message translates to:
  /// **'Clamping Successful'**
  String get clamping_successful;

  /// No description provided for @clamping_successful_message.
  ///
  /// In en, this message translates to:
  /// **'The vehicle has been clamped due to unsettled\nparking violations.'**
  String get clamping_successful_message;

  /// No description provided for @close_clamping.
  ///
  /// In en, this message translates to:
  /// **'Close Clamping'**
  String get close_clamping;

  /// No description provided for @scan_qr.
  ///
  /// In en, this message translates to:
  /// **'Scan QR'**
  String get scan_qr;

  /// No description provided for @note_here.
  ///
  /// In en, this message translates to:
  /// **'Note here...'**
  String get note_here;

  /// No description provided for @note_minimum_chars_validation.
  ///
  /// In en, this message translates to:
  /// **'Please provide a note with at least {length} characters'**
  String note_minimum_chars_validation(Object length);

  /// No description provided for @clamping_procedure_successfully_closed.
  ///
  /// In en, this message translates to:
  /// **'Clamping procedure successfully closed.'**
  String get clamping_procedure_successfully_closed;

  /// No description provided for @towing_procedure_successfully_closed.
  ///
  /// In en, this message translates to:
  /// **'Towing procedure successfully closed.'**
  String get towing_procedure_successfully_closed;

  /// No description provided for @new_towing_requests.
  ///
  /// In en, this message translates to:
  /// **'New Towing Requests'**
  String get new_towing_requests;

  /// No description provided for @tow.
  ///
  /// In en, this message translates to:
  /// **'Tow'**
  String get tow;

  /// No description provided for @close_towing.
  ///
  /// In en, this message translates to:
  /// **'Close Towing'**
  String get close_towing;

  /// No description provided for @towing_successful.
  ///
  /// In en, this message translates to:
  /// **'Towing Successful'**
  String get towing_successful;

  /// No description provided for @towing_successful_message.
  ///
  /// In en, this message translates to:
  /// **'The vehicle has been towed due to unsettled\nparking violations.'**
  String get towing_successful_message;

  /// No description provided for @clamped_vehicles.
  ///
  /// In en, this message translates to:
  /// **'Clamped Vehicles'**
  String get clamped_vehicles;

  /// No description provided for @towed_vehicles.
  ///
  /// In en, this message translates to:
  /// **'Towed Vehicles'**
  String get towed_vehicles;

  /// No description provided for @clamping_requests.
  ///
  /// In en, this message translates to:
  /// **'Clamping Requests'**
  String get clamping_requests;

  /// No description provided for @towing_requests.
  ///
  /// In en, this message translates to:
  /// **'Towing Requests'**
  String get towing_requests;

  /// No description provided for @requests.
  ///
  /// In en, this message translates to:
  /// **'Requests'**
  String get requests;

  /// No description provided for @clamped.
  ///
  /// In en, this message translates to:
  /// **'Clamped'**
  String get clamped;

  /// No description provided for @towed.
  ///
  /// In en, this message translates to:
  /// **'Towed'**
  String get towed;

  /// No description provided for @assign.
  ///
  /// In en, this message translates to:
  /// **'Assign'**
  String get assign;

  /// No description provided for @no_new_request.
  ///
  /// In en, this message translates to:
  /// **'No New Request'**
  String get no_new_request;

  /// No description provided for @no_new_clamping_request_description.
  ///
  /// In en, this message translates to:
  /// **'You\'re all caught up!\nNo new clamping requests right now.'**
  String get no_new_clamping_request_description;

  /// No description provided for @no_new_towing_request_description.
  ///
  /// In en, this message translates to:
  /// **'You\'re all caught up!\nNo new towing requests right now.'**
  String get no_new_towing_request_description;

  /// No description provided for @no_results_found.
  ///
  /// In en, this message translates to:
  /// **'No results found'**
  String get no_results_found;

  /// No description provided for @valet_operations.
  ///
  /// In en, this message translates to:
  /// **'Valet Operations'**
  String get valet_operations;

  /// No description provided for @valet_request_list.
  ///
  /// In en, this message translates to:
  /// **'Valet Request List'**
  String get valet_request_list;

  /// No description provided for @search_vehicle_not_found.
  ///
  /// In en, this message translates to:
  /// **'No Vehicle Found'**
  String get search_vehicle_not_found;

  /// No description provided for @search_vehicle_not_found_description_one.
  ///
  /// In en, this message translates to:
  /// **'We couldn\\\'t find any vehicle matching the license plate number:'**
  String get search_vehicle_not_found_description_one;

  /// No description provided for @search_vehicle_not_found_description_two.
  ///
  /// In en, this message translates to:
  /// **'Please double-check the plate number and try again.'**
  String get search_vehicle_not_found_description_two;

  /// No description provided for @try_again.
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get try_again;

  /// No description provided for @clamped_date_and_time.
  ///
  /// In en, this message translates to:
  /// **'Clamped Date & Time'**
  String get clamped_date_and_time;

  /// No description provided for @clamped_vehicle_details.
  ///
  /// In en, this message translates to:
  /// **'Clamped Vehicle Details'**
  String get clamped_vehicle_details;

  /// No description provided for @clamped_by.
  ///
  /// In en, this message translates to:
  /// **'Clamped By'**
  String get clamped_by;

  /// No description provided for @release_vehicle.
  ///
  /// In en, this message translates to:
  /// **'Release Vehicle'**
  String get release_vehicle;

  /// No description provided for @release_clamp_question.
  ///
  /// In en, this message translates to:
  /// **'Release Clamp?'**
  String get release_clamp_question;

  /// No description provided for @release_clamp_description.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to release the clamp for this vehicle? This action cannot be undone.'**
  String get release_clamp_description;

  /// No description provided for @release_clamp_success_message.
  ///
  /// In en, this message translates to:
  /// **'The clamped vehicle has been released.'**
  String get release_clamp_success_message;

  /// No description provided for @towed_date_and_time.
  ///
  /// In en, this message translates to:
  /// **'Towed Date & Time'**
  String get towed_date_and_time;

  /// No description provided for @no_towed_vehicles.
  ///
  /// In en, this message translates to:
  /// **'No Towed Vehicles'**
  String get no_towed_vehicles;

  /// No description provided for @no_towed_vehicles_description.
  ///
  /// In en, this message translates to:
  /// **'No towed vehicles found in the system.'**
  String get no_towed_vehicles_description;

  /// No description provided for @no_clamped_vehicles.
  ///
  /// In en, this message translates to:
  /// **'No Clamped Vehicles'**
  String get no_clamped_vehicles;

  /// No description provided for @no_clamped_vehicles_description.
  ///
  /// In en, this message translates to:
  /// **'No clamped vehicles found in the system.'**
  String get no_clamped_vehicles_description;

  /// No description provided for @towed_vehicle_details.
  ///
  /// In en, this message translates to:
  /// **'Towed Vehicle Details'**
  String get towed_vehicle_details;

  /// No description provided for @towed_by.
  ///
  /// In en, this message translates to:
  /// **'Towed By'**
  String get towed_by;

  /// No description provided for @release_vehicle_question.
  ///
  /// In en, this message translates to:
  /// **'Release Vehicle?'**
  String get release_vehicle_question;

  /// No description provided for @release_vehicle_description.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to release the vehicle? This action cannot be undone.'**
  String get release_vehicle_description;

  /// No description provided for @release_tow_success_message.
  ///
  /// In en, this message translates to:
  /// **'The towed vehicle has been released.'**
  String get release_tow_success_message;

  /// No description provided for @update_vehicle_location.
  ///
  /// In en, this message translates to:
  /// **'Update Vehicle Location'**
  String get update_vehicle_location;

  /// No description provided for @update_vehicle_location_description.
  ///
  /// In en, this message translates to:
  /// **'Modify the current location of the towed vehicle.'**
  String get update_vehicle_location_description;

  /// No description provided for @update.
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// No description provided for @confirm_location_update.
  ///
  /// In en, this message translates to:
  /// **'Confirm Location Update'**
  String get confirm_location_update;

  /// No description provided for @confirm_location_update_description.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to update the location of the towed vehicle? This action will replace the previous location with the new one.'**
  String get confirm_location_update_description;

  /// No description provided for @towed_location_updated_successfully.
  ///
  /// In en, this message translates to:
  /// **'Towed vehicle location updated successfully.'**
  String get towed_location_updated_successfully;

  /// No description provided for @add_new_vehicle.
  ///
  /// In en, this message translates to:
  /// **'Add New Vehicle'**
  String get add_new_vehicle;

  /// No description provided for @select_plate_type_star.
  ///
  /// In en, this message translates to:
  /// **'Select Plate Type*'**
  String get select_plate_type_star;

  /// No description provided for @license_plate_number_star.
  ///
  /// In en, this message translates to:
  /// **'License Plate Number*'**
  String get license_plate_number_star;

  /// No description provided for @please_select_plate_type.
  ///
  /// In en, this message translates to:
  /// **'Please select plate type'**
  String get please_select_plate_type;

  /// No description provided for @please_enter_license_plate_number.
  ///
  /// In en, this message translates to:
  /// **'Please enter license plate number'**
  String get please_enter_license_plate_number;

  /// No description provided for @or.
  ///
  /// In en, this message translates to:
  /// **'Or'**
  String get or;

  /// No description provided for @scan_plate.
  ///
  /// In en, this message translates to:
  /// **'Scan Plate'**
  String get scan_plate;

  /// No description provided for @vehicle_name_star.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Name*'**
  String get vehicle_name_star;

  /// No description provided for @please_enter_vehicle_name.
  ///
  /// In en, this message translates to:
  /// **'Please enter vehicle name'**
  String get please_enter_vehicle_name;

  /// No description provided for @vehicle_registered_country_star.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Registered Country*'**
  String get vehicle_registered_country_star;

  /// No description provided for @please_select_vehicle_registered_country.
  ///
  /// In en, this message translates to:
  /// **'Please select vehicle registered country'**
  String get please_select_vehicle_registered_country;

  /// No description provided for @make_year.
  ///
  /// In en, this message translates to:
  /// **'Make Year'**
  String get make_year;

  /// No description provided for @select_year.
  ///
  /// In en, this message translates to:
  /// **'Select year'**
  String get select_year;

  /// No description provided for @color.
  ///
  /// In en, this message translates to:
  /// **'Color'**
  String get color;

  /// No description provided for @select_color.
  ///
  /// In en, this message translates to:
  /// **'Select color'**
  String get select_color;

  /// No description provided for @vehicle_type_star.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Type*'**
  String get vehicle_type_star;

  /// No description provided for @select_type.
  ///
  /// In en, this message translates to:
  /// **'Select type'**
  String get select_type;

  /// No description provided for @please_select_vehicle_type.
  ///
  /// In en, this message translates to:
  /// **'Please select vehicle type'**
  String get please_select_vehicle_type;

  /// No description provided for @car_image.
  ///
  /// In en, this message translates to:
  /// **'Car Image'**
  String get car_image;

  /// No description provided for @contact_number.
  ///
  /// In en, this message translates to:
  /// **'Contact Number'**
  String get contact_number;

  /// No description provided for @enter_mobile_number.
  ///
  /// In en, this message translates to:
  /// **'Enter Mobile Number'**
  String get enter_mobile_number;

  /// No description provided for @search_country.
  ///
  /// In en, this message translates to:
  /// **'Search Country'**
  String get search_country;

  /// No description provided for @select_country.
  ///
  /// In en, this message translates to:
  /// **'Select Country'**
  String get select_country;

  /// No description provided for @license_plate_number.
  ///
  /// In en, this message translates to:
  /// **'License Plate Number'**
  String get license_plate_number;

  /// No description provided for @vehicle_registered_country.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Registered Country'**
  String get vehicle_registered_country;

  /// No description provided for @search_color.
  ///
  /// In en, this message translates to:
  /// **'Search Color'**
  String get search_color;

  /// No description provided for @vehicle_type.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Type'**
  String get vehicle_type;

  /// No description provided for @search_type.
  ///
  /// In en, this message translates to:
  /// **'Search Type'**
  String get search_type;

  /// No description provided for @save_vehicle.
  ///
  /// In en, this message translates to:
  /// **'Save Vehicle'**
  String get save_vehicle;

  /// No description provided for @enter_plate_number.
  ///
  /// In en, this message translates to:
  /// **'Enter plate number'**
  String get enter_plate_number;

  /// No description provided for @enter_plate_letters.
  ///
  /// In en, this message translates to:
  /// **'Enter plate letters'**
  String get enter_plate_letters;

  /// No description provided for @english_and_arabic_plate_letters_should_match.
  ///
  /// In en, this message translates to:
  /// **'English and Arabic plate letters should match.'**
  String get english_and_arabic_plate_letters_should_match;

  /// No description provided for @please_enter_valid_mobile_number.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid mobile number'**
  String get please_enter_valid_mobile_number;

  /// No description provided for @confirm_vehicle_license_plate.
  ///
  /// In en, this message translates to:
  /// **'Confirm Vehicle License Plate'**
  String get confirm_vehicle_license_plate;

  /// No description provided for @confirm_and_save.
  ///
  /// In en, this message translates to:
  /// **'Confirm  & Save'**
  String get confirm_and_save;

  /// No description provided for @back_to_edit.
  ///
  /// In en, this message translates to:
  /// **'Back to Edit'**
  String get back_to_edit;

  /// No description provided for @notifications.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// No description provided for @today.
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// No description provided for @yesterday.
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get yesterday;

  /// No description provided for @admin.
  ///
  /// In en, this message translates to:
  /// **'Admin'**
  String get admin;

  /// No description provided for @valet.
  ///
  /// In en, this message translates to:
  /// **'Valet'**
  String get valet;

  /// No description provided for @no_notifications.
  ///
  /// In en, this message translates to:
  /// **'No Notifications'**
  String get no_notifications;

  /// No description provided for @no_notifications_description.
  ///
  /// In en, this message translates to:
  /// **'No notifications at the moment.'**
  String get no_notifications_description;

  /// No description provided for @press_back_again_to_close.
  ///
  /// In en, this message translates to:
  /// **'Press back again to close'**
  String get press_back_again_to_close;

  /// No description provided for @not_available.
  ///
  /// In en, this message translates to:
  /// **'Not Available'**
  String get not_available;

  /// No description provided for @assigned.
  ///
  /// In en, this message translates to:
  /// **'Assigned'**
  String get assigned;

  /// No description provided for @vehicles.
  ///
  /// In en, this message translates to:
  /// **'Vehicles'**
  String get vehicles;

  /// No description provided for @assigned_clamping_request.
  ///
  /// In en, this message translates to:
  /// **'Assigned Clamping Request'**
  String get assigned_clamping_request;

  /// No description provided for @assigned_towing_request.
  ///
  /// In en, this message translates to:
  /// **'Assigned Towing Request'**
  String get assigned_towing_request;

  /// No description provided for @search_status.
  ///
  /// In en, this message translates to:
  /// **'Search Status'**
  String get search_status;

  /// No description provided for @search_payment_method.
  ///
  /// In en, this message translates to:
  /// **'Search Payment Method'**
  String get search_payment_method;

  /// No description provided for @no_request_assigned.
  ///
  /// In en, this message translates to:
  /// **'No Requests Assigned'**
  String get no_request_assigned;

  /// No description provided for @no_request_assigned_description.
  ///
  /// In en, this message translates to:
  /// **'You haven’t assigned any requests yet. Assign a request to track progress.'**
  String get no_request_assigned_description;

  /// No description provided for @connection_lost.
  ///
  /// In en, this message translates to:
  /// **'Connection Lost'**
  String get connection_lost;

  /// No description provided for @connection_lost_description.
  ///
  /// In en, this message translates to:
  /// **'Please check your internet connection and try\nagain.'**
  String get connection_lost_description;

  /// No description provided for @headsup.
  ///
  /// In en, this message translates to:
  /// **'Heads Up!'**
  String get headsup;

  /// No description provided for @okay.
  ///
  /// In en, this message translates to:
  /// **'Okay'**
  String get okay;

  /// No description provided for @printer_not_detected_title.
  ///
  /// In en, this message translates to:
  /// **'Let’s Connect Your Printer'**
  String get printer_not_detected_title;

  /// No description provided for @printer_not_detected_description.
  ///
  /// In en, this message translates to:
  /// **'No printer detected. Please ensure your SPP-R310 is paired and turned on.'**
  String get printer_not_detected_description;

  /// No description provided for @printing_issue_title.
  ///
  /// In en, this message translates to:
  /// **'Printing Issue'**
  String get printing_issue_title;

  /// No description provided for @printing_issue_description.
  ///
  /// In en, this message translates to:
  /// **'We couldn’t print your ticket. Please ensure your printer is connected and try again.'**
  String get printing_issue_description;

  /// No description provided for @sar.
  ///
  /// In en, this message translates to:
  /// **'SAR'**
  String get sar;

  /// No description provided for @gallery.
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallery;

  /// No description provided for @camera.
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// No description provided for @arabic.
  ///
  /// In en, this message translates to:
  /// **'Arabic'**
  String get arabic;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @no_valet_assigned.
  ///
  /// In en, this message translates to:
  /// **'No valet assigned yet. Please assign a valet to proceed.'**
  String get no_valet_assigned;

  /// No description provided for @bluetooth_not_enabled.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth Not Enabled'**
  String get bluetooth_not_enabled;

  /// No description provided for @bluetooth_not_enabled_description.
  ///
  /// In en, this message translates to:
  /// **'To print a ticket, please enable Bluetooth and connect to the printer.'**
  String get bluetooth_not_enabled_description;

  /// No description provided for @enable_bluetooth.
  ///
  /// In en, this message translates to:
  /// **'Enable Bluetooth'**
  String get enable_bluetooth;

  /// No description provided for @add.
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// No description provided for @vehicle_name_hint_text.
  ///
  /// In en, this message translates to:
  /// **'Eg. Toyota Prado'**
  String get vehicle_name_hint_text;

  /// No description provided for @add_more.
  ///
  /// In en, this message translates to:
  /// **'Add More'**
  String get add_more;

  /// No description provided for @did_not_receive_the_otp.
  ///
  /// In en, this message translates to:
  /// **'Didn’t receive the OTP?'**
  String get did_not_receive_the_otp;

  /// No description provided for @resend_otp.
  ///
  /// In en, this message translates to:
  /// **'Resend OTP'**
  String get resend_otp;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
