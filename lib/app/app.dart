import 'package:albalad_operator_app/app/routes.dart';
import 'package:albalad_operator_app/app/theme_data.dart';
import 'package:albalad_operator_app/features/splash/view/splash_screen.dart';
import 'package:albalad_operator_app/language/app_localizations.dart';
import 'package:albalad_operator_app/language/language_data.dart';
import 'package:albalad_operator_app/language/language_storage.dart';
import 'package:albalad_operator_app/providers/global_providers.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get_it/get_it.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

final getIt = GetIt.instance;
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class MyApp extends HookConsumerWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use useState for languageData
    final languageData = useState<LanguageData?>(null);

    // Use useEffect to load language data on initialization
    useEffect(() {
      Future<void> loadData() async {
        final data = await LanguageStorage.getLanguageData();
        languageData.value = data; // Update languageData state
      }

      loadData();
      return null; // No cleanup needed
    }, []); // Empty dependency list means it runs once on mount

    return ScreenUtilInit(
      designSize: const Size(393, 852),
      enableScaleWH: () => false,
      enableScaleText: () => false,
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, _) {
        final locale = ref.watch(localeProvider);
        return MaterialApp(
          navigatorKey: navigatorKey,
          debugShowCheckedModeBanner: false,
          title: 'Makani Operators',
          locale: locale,
          localizationsDelegates: [
            AppLocalizationsDelegate(languageData.value, locale),
            // AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('en'), // English
            Locale('ar'), // Arabic
          ],
          localeResolutionCallback: (locale, supportedLocales) {
            for (var supportedLocale in supportedLocales) {
              if (supportedLocale.languageCode == locale?.languageCode) {
                return supportedLocale;
              }
            }
            return supportedLocales.first;
          },
          theme: ThemeData(
            fontFamily: 'SF-Pro-Display',
            colorScheme: ColorScheme.fromSeed(
              seedColor: ColorConstants.primaryColor,
              primary: ColorConstants.primaryColor,
            ),
            useMaterial3: true,
            scaffoldBackgroundColor: Colors.white,
            appBarTheme: appBarTheme,
            splashColor: Colors.transparent,
            elevatedButtonTheme: elevatedButtonTheme,
            inputDecorationTheme: inputDecorationTheme,
            bottomSheetTheme: const BottomSheetThemeData(
              backgroundColor: Colors.white,
            ),
          ),
          builder: EasyLoading.init(),
          onGenerateRoute: onAppGenerateRoute(),
          initialRoute: SplashScreen.route,
        );
      },
    );
  }
}
