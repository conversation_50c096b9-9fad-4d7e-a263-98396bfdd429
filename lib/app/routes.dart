import 'dart:io';
import 'package:albalad_operator_app/app/slide_right_route.dart';
import 'package:albalad_operator_app/features/add_vehicle/view/add_vehicle_screen.dart';
import 'package:albalad_operator_app/features/add_vehicle/view/confirm_vehicle_plate_screen.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/assign_direct_clamping_screen.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/clamp_vehicle_screen.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/clamped_details_screen.dart';
import 'package:albalad_operator_app/features/current_parked_vehicles/view/current_parked_listing_screen.dart';
import 'package:albalad_operator_app/features/home/<USER>/home_screen.dart';
import 'package:albalad_operator_app/features/authentication/view/sign_in_screen.dart';
import 'package:albalad_operator_app/features/master/view/master_screen.dart';
import 'package:albalad_operator_app/features/notifications/view/notification_screen.dart';
import 'package:albalad_operator_app/features/number_plate_scanner/view/number_plate_scanner.dart';
import 'package:albalad_operator_app/features/profile/view/change_password_screen.dart';
import 'package:albalad_operator_app/features/profile/view/edit_profile_screen.dart';
import 'package:albalad_operator_app/features/profile/view/my_profile_screen.dart';
import 'package:albalad_operator_app/features/search/view/search_screen.dart';
import 'package:albalad_operator_app/features/splash/view/splash_screen.dart';
import 'package:albalad_operator_app/features/success_screen.dart';
import 'package:albalad_operator_app/features/support_center/view/contact_us_screen.dart';
import 'package:albalad_operator_app/features/support_center/view/faq_screen.dart';
import 'package:albalad_operator_app/features/support_center/view/notification_settings_screen.dart';
import 'package:albalad_operator_app/features/support_center/view/privacy_policy_screen.dart';
import 'package:albalad_operator_app/features/support_center/view/terms_and_conditions_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/assign_direct_towing_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/tow_vehicle_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/towed_details_screen.dart';
import 'package:albalad_operator_app/features/valet/view/assign_valet_screen.dart';
import 'package:albalad_operator_app/features/valet/view/current_valet_vehicles_screen.dart';
import 'package:albalad_operator_app/features/valet/view/generate_valet_ticket_screen.dart';
import 'package:albalad_operator_app/features/valet/view/valet_operations_screen.dart';
import 'package:albalad_operator_app/features/vehicle_details/view/vehicle_details_screen.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/assign_clamping_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/assign_towing_screen.dart';
import 'package:albalad_operator_app/features/violation/view/assign_violation_screen.dart';
import 'package:albalad_operator_app/features/clamp_violation/view/clamp_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/violation/view/parking_violation_details_screen.dart';
import 'package:albalad_operator_app/features/violation/view/current_violations_screen.dart';
import 'package:albalad_operator_app/features/tow_violation/view/tow_vehicle_listing_screen.dart';
import 'package:albalad_operator_app/features/violation/view/violation_details_screen.dart';
import 'package:albalad_operator_app/features/violation/view/violations_and_notices_screen.dart';
import 'package:flutter/cupertino.dart';
import '../features/authentication/view/create_new_password_screen.dart';
import '../features/authentication/view/forgot_password_screen.dart';
import '../features/authentication/view/new_password_success_screen.dart';
import '../features/authentication/view/otp_verification_screen.dart';

RouteFactory onAppGenerateRoute() => (settings) {
      Route<dynamic> getRoute(Widget child) {
        if (Platform.isIOS) {
          return CupertinoPageRoute(
            builder: (context) => child,
            settings: settings,
          );
        } else {
          return SlideRightRoute(child, settings.name);
        }
      }

      switch (settings.name) {
        case SplashScreen.route:
          return getRoute(const SplashScreen());
        case SignInScreen.route:
          return getRoute(const SignInScreen());
        case MasterScreen.route:
          return getRoute(const MasterScreen());
        case HomeScreen.route:
          return getRoute(const HomeScreen());
        case EditProfileScreen.route:
          return getRoute(const EditProfileScreen());
        case ChangePasswordScreen.route:
          return getRoute(const ChangePasswordScreen());
        case ParkingViolationDetailsScreen.route:
          final args = settings.arguments as ParkingViolationDetailsScreen;
          return getRoute(
            ParkingViolationDetailsScreen(
              violationUid: args.violationUid,
              previousRoute: args.previousRoute,
            ),
          );
        case ForgotPasswordScreen.route:
          final args = settings.arguments as ForgotPasswordScreen;
          return getRoute(ForgotPasswordScreen(email: args.email));
        case OtpVerificationScreen.route:
          bool isTwoFactorAuth = settings.arguments as bool;
          return getRoute(
            OtpVerificationScreen(isTwoFactorAuth: isTwoFactorAuth),
          );
        case CreateNewPasswordScreen.route:
          final args = settings.arguments as CreateNewPasswordScreen;
          return getRoute(
            CreateNewPasswordScreen(passwordURL: args.passwordURL),
          );
        case NewPasswordSuccessScreen.route:
          return getRoute(const NewPasswordSuccessScreen());
        case CurrentParkedListingScreen.route:
          return getRoute(const CurrentParkedListingScreen());
        case VehicleDetailsScreen.route:
          final args = settings.arguments as VehicleDetailsScreen;
          return getRoute(
            VehicleDetailsScreen(
              vehicleNumber: args.vehicleNumber,
              uid: args.uid,
              previousRoute: args.previousRoute,
            ),
          );
        case AddVehicleScreen.route:
          return getRoute(const AddVehicleScreen());
        case NumberPlateScanner.route:
          final args = settings.arguments as NumberPlateScanner;
          return getRoute(
              NumberPlateScanner(previousRoute: args.previousRoute));
        case SearchScreen.route:
          final args = settings.arguments as SearchScreen;
          return getRoute(SearchScreen(previousRoute: args.previousRoute));
        case AssignViolationScreen.route:
          final args = settings.arguments as AssignViolationScreen;
          return getRoute(AssignViolationScreen(
            vehicleUid: args.vehicleUid,
            violationtype: args.violationtype,
            previousRoute: args.previousRoute,
            key: args.key,
          ));
        case AssignValetScreen.route:
          final args = settings.arguments as AssignValetScreen;
          return getRoute(AssignValetScreen(
            makeYear: args.makeYear,
            numberPlateType: args.numberPlateType,
            vehicleId: args.vehicleId,
            vehicleImage: args.vehicleImage,
            vehicleName: args.vehicleName,
            vehicleNumber: args.vehicleNumber,
            vehicleType: args.vehicleType,
            vehicleUid: args.vehicleUid,
            previousRoute: args.previousRoute,
            valetRequestUid: args.valetRequestUid,
            key: args.key,
          ));
        case TermsAndConditionsScreen.route:
          return getRoute(const TermsAndConditionsScreen());
        case PrivacyPolicyScreen.route:
          return getRoute(const PrivacyPolicyScreen());
        case FaqScreen.route:
          return getRoute(const FaqScreen());
        case ContactUsScreen.route:
          return getRoute(const ContactUsScreen());
        case NotificationSettingsScreen.route:
          return getRoute(const NotificationSettingsScreen());
        case AssignClampingScreen.route:
          final args = settings.arguments as AssignClampingScreen;
          return getRoute(
            AssignClampingScreen(
              vehicleUid: args.vehicleUid,
              previousRoute: args.previousRoute,
            ),
          );
        case AssignTowingScreen.route:
          final args = settings.arguments as AssignTowingScreen;
          return getRoute(AssignTowingScreen(
            vehicleUid: args.vehicleUid,
            previousRoute: args.previousRoute,
          ));
        case AssignDirectTowingScreen.route:
          final args = settings.arguments as AssignDirectTowingScreen;
          return getRoute(AssignDirectTowingScreen(
            vehicleUid: args.vehicleUid,
            previousRoute: args.previousRoute,
          ));
        case SuccessScreen.route:
          final args = settings.arguments as SuccessScreen;
          return getRoute(SuccessScreen(
            title: args.title,
            message: args.message,
            onPressed: args.onPressed,
            buttonName: args.buttonName,
          ));
        case ViolationDetailsScreen.route:
          final args = settings.arguments as ViolationDetailsScreen;
          return getRoute(
            ViolationDetailsScreen(
              violationUid: args.violationUid,
              vehicleUid: args.vehicleUid,
              previousRoute: args.previousRoute,
            ),
          );
        case CurrentViolationsScreen.route:
          return getRoute(CurrentViolationsScreen());
        case ViolationsAndNoticesScreen.route:
          return getRoute(ViolationsAndNoticesScreen());
        case ClampVehicleListingScreen.route:
          final args = settings.arguments as ClampVehicleListingScreen;
          return getRoute(ClampVehicleListingScreen(tabIndex: args.tabIndex));
        case TowVehicleListingScreen.route:
          final args = settings.arguments as TowVehicleListingScreen;
          return getRoute(TowVehicleListingScreen(tabIndex: args.tabIndex));
        case GenerateValetTicketScreen.route:
          final args = settings.arguments as GenerateValetTicketScreen;
          return getRoute(
            GenerateValetTicketScreen(
              previousRoute: args.previousRoute,
              valetUid: args.valetUid,
              vehicleUid: args.vehicleUid,
            ),
          );
        case CurrentValetVehiclesScreen.route:
          return getRoute(CurrentValetVehiclesScreen());
        case ClampVehicleScreen.route:
          final args = settings.arguments as ClampVehicleScreen;
          return getRoute(ClampVehicleScreen(
            violationUid: args.violationUid,
            previousRoute: args.previousRoute,
            vehicleUid: args.vehicleUid,
          ));
        case TowVehicleScreen.route:
          final args = settings.arguments as TowVehicleScreen;
          return getRoute(TowVehicleScreen(
            violationUid: args.violationUid,
            previousRoute: args.previousRoute,
            vehicleUid: args.vehicleUid,
          ));
        case ValetOperationsScreen.route:
          return getRoute(ValetOperationsScreen());
        case ClampedDetailsScreen.route:
          final args = settings.arguments as ClampedDetailsScreen;
          return getRoute(ClampedDetailsScreen(
            uid: args.uid,
            previousRoute: args.previousRoute,
          ));
        case TowedDetailsScreen.route:
          final args = settings.arguments as TowedDetailsScreen;
          return getRoute(TowedDetailsScreen(
            uid: args.uid,
            previousRoute: args.previousRoute,
          ));
        case AssignDirectClampingScreen.route:
          final args = settings.arguments as AssignDirectClampingScreen;
          return getRoute(
            AssignDirectClampingScreen(
              vehicleUid: args.vehicleUid,
              previousRoute: args.previousRoute,
            ),
          );
        case ConfirmVehiclePlateScreen.route:
          final args = settings.arguments as ConfirmVehiclePlateScreen;
          return getRoute(
            ConfirmVehiclePlateScreen(
              englishNumbers: args.englishNumbers,
              arabicNumbers: args.arabicNumbers,
              englishLetters: args.englishLetters,
              arabicLetters: args.arabicLetters,
            ),
          );
        case NotificationScreen.route:
          return getRoute(const NotificationScreen());
        case MyProfileScreen.route:
          return getRoute(const MyProfileScreen());
        default:
          return null;
      }
    };
