import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

AppBarTheme get appBarTheme => AppBarTheme(
      backgroundColor: Colors.white,
      centerTitle: true,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: TextStyle(
        fontSize: 18.sp,
        color: ColorConstants.color181818,
        fontWeight: FontWeight.w600,
      ),
    );

ElevatedButtonThemeData get elevatedButtonTheme => ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: ColorConstants.primaryColor,
        textStyle: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
        ),
        foregroundColor: ColorConstants.backgroundColor,
        minimumSize: Size(double.infinity, 50.h),
      ),
    );

InputDecorationTheme get inputDecorationTheme => InputDecorationTheme(
      border: OutlineInputBorder(
        borderSide: BorderSide(
          color: ColorConstants.primaryColor.withValues(alpha: 0.2),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(25.r),
      ),
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: ColorConstants.primaryColor.withValues(alpha: 0.2),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(25.r),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: const BorderSide(
          color: ColorConstants.colorCCB5A7,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(25.r),
      ),
      hintStyle: TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.colorCECECE,
      ),
    );
