import 'package:albalad_operator_app/app/app.dart';
import 'package:albalad_operator_app/features/authentication/view/sign_in_screen.dart';
import 'package:dio/dio.dart';

class AuthInterceptor extends Interceptor {
  bool _isNavigating = false;
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // print('Response: ${response.statusCode}');
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 401 && !_isNavigating) {
      _isNavigating = true;
      navigatorKey.currentState
          ?.pushNamedAndRemoveUntil(
        SignInScreen.route, // Replace with your login route
        (route) => false,
      )
          .then((_) {
        _isNavigating = false; // Reset the flag after navigation completes
      });
    }
    super.onError(err, handler);
  }
}
