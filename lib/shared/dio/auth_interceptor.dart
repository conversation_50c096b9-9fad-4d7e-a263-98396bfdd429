import 'package:albalad_operator_app/app/app.dart';
import 'package:albalad_operator_app/features/authentication/view/sign_in_screen.dart';
import 'package:albalad_operator_app/shared/services/token_refresh_service.dart';
import 'package:dio/dio.dart';

class AuthInterceptor extends Interceptor {
  bool _isNavigating = false;
  bool _isRefreshing = false;
  final TokenRefreshService _tokenRefreshService = TokenRefreshService();

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // print('Response: ${response.statusCode}');
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401 && !_isNavigating && !_isRefreshing) {
      _isRefreshing = true;

      try {
        // Attempt to refresh the token
        final newAccessToken = await _tokenRefreshService.refreshAccessToken();

        if (newAccessToken != null) {
          // Token refresh successful, retry the original request
          final originalRequest = err.requestOptions;

          // Update the authorization header with the new token
          originalRequest.headers['Authorization'] = 'Bearer $newAccessToken';

          // Retry the original request
          try {
            final response = await Dio().fetch(originalRequest);
            handler.resolve(response);
            return;
          } catch (retryError) {
            // If retry fails, continue with normal error handling
            handler.next(err);
            return;
          }
        } else {
          // Token refresh failed, navigate to sign-in
          _navigateToSignIn();
        }
      } catch (refreshError) {
        // Token refresh failed, navigate to sign-in
        _navigateToSignIn();
      } finally {
        _isRefreshing = false;
      }
    } else if (err.response?.statusCode == 401 && !_isNavigating) {
      // If already refreshing or navigation in progress, just navigate to sign-in
      _navigateToSignIn();
    }

    super.onError(err, handler);
  }

  void _navigateToSignIn() {
    if (!_isNavigating) {
      _isNavigating = true;
      navigatorKey.currentState
          ?.pushNamedAndRemoveUntil(
        SignInScreen.route,
        (route) => false,
      )
          .then((_) {
        _isNavigating = false;
      });
    }
  }
}
