import 'package:albalad_operator_app/shared/models/vehicle_image.dart';
import 'package:albalad_operator_app/shared/models/violation_image.dart';

class ViolationDetails {
  String? uid;
  String? violationId;
  String? lastViolationRequestUid;
  String? status;
  String? violationType;
  String? paymentStatus;
  String? violationDateTime;
  String? gracePeriodEnd;
  String? vehicleNumber;
  String? vehicleName;
  String? vehicleId;
  String? vehicleType;
  int? makeYear;
  String? numberPlateType;
  String? coorparateVehicle;
  String? vehicleOwnership;
  bool? isSharedVehicle;
  List<VehicleImage>? vehicleImage;
  double? fineAmount;
  double? vatAmount;
  double? fineAmountAfterVat;
  String? gracePeriod;
  String? violationReportedBy;
  String? violationDescription;
  String? clampedDescription;
  String? towedDescription;
  List<ViolationImage>? violationImage;
  List<ViolationImage>? clampedImages;
  List<ViolationImage>? towedImages;

  ViolationDetails({
    this.uid,
    this.violationId,
    this.lastViolationRequestUid,
    this.status,
    this.violationType,
    this.paymentStatus,
    this.violationDateTime,
    this.gracePeriodEnd,
    this.vehicleNumber,
    this.vehicleName,
    this.vehicleId,
    this.vehicleType,
    this.makeYear,
    this.numberPlateType,
    this.coorparateVehicle,
    this.vehicleOwnership,
    this.isSharedVehicle,
    this.vehicleImage,
    this.fineAmount,
    this.vatAmount,
    this.fineAmountAfterVat,
    this.gracePeriod,
    this.violationReportedBy,
    this.violationDescription,
    this.clampedDescription,
    this.towedDescription,
    this.violationImage,
    this.clampedImages,
    this.towedImages,
  });

  ViolationDetails.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    violationId = json['violation_id'];
    lastViolationRequestUid = json['last_violation_request_uid'];
    status = json['status'];
    violationType = json['violation_type'];
    paymentStatus = json['payment_status'];
    violationDateTime = json['violation_date_time'];
    gracePeriodEnd = json['grace_period_end'];
    vehicleNumber = json['vehicle_number'];
    vehicleName = json['vehicle_name'];
    vehicleId = json['vehicle_id'];
    vehicleType = json['vehicle_type'];
    makeYear = int.tryParse(json['make_year'].toString());
    numberPlateType = json['number_plate_type'];
    coorparateVehicle = json['coorparate_vehicle'];
    vehicleOwnership = json['vehicle_ownership'];
    isSharedVehicle = json['is_shared_vehicle'];
    if (json['vehicle_image'] != null) {
      vehicleImage = <VehicleImage>[];
      json['vehicle_image'].forEach((v) {
        vehicleImage!.add(VehicleImage.fromJson(v));
      });
    }
    fineAmount = double.tryParse(json['fine_amount'].toString());
    vatAmount = double.tryParse(json['vat_amount'].toString());
    fineAmountAfterVat =
        double.tryParse(json['fine_amount_after_vat'].toString());
    gracePeriod = json['grace_period'];
    violationReportedBy = json['violation_reported_by'];
    violationDescription = json['violation_description'];
    clampedDescription = json['clamped_description'];
    towedDescription = json['towed_description'];
    if (json['violation_image'] != null) {
      violationImage = <ViolationImage>[];
      json['violation_image'].forEach((v) {
        violationImage!.add(ViolationImage.fromJson(v));
      });
    }
    if (json['clamped_images'] != null) {
      clampedImages = <ViolationImage>[];
      json['clamped_images'].forEach((v) {
        clampedImages!.add(ViolationImage.fromJson(v));
      });
    }
    if (json['towed_images'] != null) {
      towedImages = <ViolationImage>[];
      json['towed_images'].forEach((v) {
        towedImages!.add(ViolationImage.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['violation_id'] = violationId;
    data['last_violation_request_uid'] = lastViolationRequestUid;
    data['status'] = status;
    data['violation_type'] = violationType;
    data['payment_status'] = paymentStatus;
    data['violation_date_time'] = violationDateTime;
    data['grace_period_end'] = gracePeriodEnd;
    data['vehicle_number'] = vehicleNumber;
    data['vehicle_name'] = vehicleName;
    data['vehicle_id'] = vehicleId;
    data['vehicle_type'] = vehicleType;
    data['make_year'] = makeYear;
    data['number_plate_type'] = numberPlateType;
    data['coorparate_vehicle'] = coorparateVehicle;
    data['vehicle_ownership'] = vehicleOwnership;
    data['is_shared_vehicle'] = isSharedVehicle;
    if (vehicleImage != null) {
      data['vehicle_image'] = vehicleImage!.map((v) => v.toJson()).toList();
    }
    data['fine_amount'] = fineAmount;
    data['vat_amount'] = vatAmount;
    data['fine_amount_after_vat'] = fineAmountAfterVat;
    data['grace_period'] = gracePeriod;
    data['violation_reported_by'] = violationReportedBy;
    data['violation_description'] = violationDescription;
    data['clamped_description'] = clampedDescription;
    data['towed_description'] = towedDescription;
    if (violationImage != null) {
      data['violation_image'] = violationImage!.map((v) => v.toJson()).toList();
    }
    if (clampedImages != null) {
      data['clamped_images'] = clampedImages!.map((v) => v.toJson()).toList();
    }
    if (towedImages != null) {
      data['towed_images'] = towedImages!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
