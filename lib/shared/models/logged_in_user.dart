class LoggedInUser {
  static String? result;
  static Token? token;
  static String? name;
  static String? email;
  static String? uid;

  LoggedInUser({result, token, name, email, uid});

  LoggedInUser.fromJson(Map<String, dynamic> json) {
    result = json['result'];
    token = json['token'] != null ? Token.fromJson(json['token']) : null;
    name = json['name'];
    email = json['email'];
    uid = json['uid'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['result'] = result;
    if (token != null) {
      data['token'] = token!.toJson();
    }
    data['name'] = name;
    data['email'] = email;
    data['uid'] = uid;
    return data;
  }
}

class Token {
  String? accessToken;
  String? refreshToken;
  String? expiresIn;

  Token({this.accessToken, this.refreshToken, this.expiresIn});

  Token.fromJson(Map<String, dynamic> json) {
    accessToken = json['access_token'];
    refreshToken = json['refresh_token'];
    expiresIn = json['expires_in'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['access_token'] = accessToken;
    data['refresh_token'] = refreshToken;
    data['expires_in'] = expiresIn;
    return data;
  }
}
