import 'package:albalad_operator_app/shared/models/vehicle_image.dart';

class CurrentViolation {
  String? uid;
  String? violationId;
  String? paymentStatus;
  String? violationStatus;
  String? violationType;
  String? violationDateTime;
  String? violationGrancePeriodEnd;
  bool? gracePeriodIsOver;
  int? gracePeriodSeconds;
  List<VehicleImage>? vehicleImage;
  String? vehicleNumber;
  String? vehicleName;
  String? vehicleId;
  String? vehicleType;
  int? makeYear;
  String? numberPlateType;

  CurrentViolation(
      {this.uid,
      this.violationId,
      this.paymentStatus,
      this.violationStatus,
      this.violationType,
      this.violationDateTime,
      this.violationGrancePeriodEnd,
      this.gracePeriodIsOver,
      this.gracePeriodSeconds,
      this.vehicleImage,
      this.vehicleNumber,
      this.vehicleName,
      this.vehicleId,
      this.vehicleType,
      this.makeYear,
      this.numberPlateType});

  CurrentViolation.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    violationId = json['violation_id'];
    paymentStatus = json['payment_status'];
    violationStatus = json['violation_status'];
    violationType = json['violation_type'];
    violationDateTime = json['violation_date_time'];
    violationGrancePeriodEnd = json['violation_grance_period_end'];
    gracePeriodIsOver = json['grace_period_is_over'];
    gracePeriodSeconds = json['grace_period_seconds'];
    if (json['vehicle_image'] != null) {
      vehicleImage = <VehicleImage>[];
      json['vehicle_image'].forEach((v) {
        vehicleImage!.add(VehicleImage.fromJson(v));
      });
    }
    vehicleNumber = json['vehicle_number'];
    vehicleName = json['vehicle_name'];
    vehicleId = json['vehicle_id'];
    vehicleType = json['vehicle_type'];
    makeYear = int.tryParse(json['make_year'].toString());
    numberPlateType = json['number_plate_type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['violation_id'] = violationId;
    data['payment_status'] = paymentStatus;
    data['violation_status'] = violationStatus;
    data['violation_type'] = violationType;
    data['violation_date_time'] = violationDateTime;
    data['violation_grance_period_end'] = violationGrancePeriodEnd;
    data['grace_period_is_over'] = gracePeriodIsOver;
    if (vehicleImage != null) {
      data['vehicle_image'] = vehicleImage!.map((v) => v.toJson()).toList();
    }
    data['vehicle_number'] = vehicleNumber;
    data['vehicle_name'] = vehicleName;
    data['vehicle_id'] = vehicleId;
    data['vehicle_type'] = vehicleType;
    data['make_year'] = makeYear;
    data['number_plate_type'] = numberPlateType;
    return data;
  }
}
