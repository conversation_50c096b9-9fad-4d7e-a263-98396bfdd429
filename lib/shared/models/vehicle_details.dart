import 'package:albalad_operator_app/shared/models/vehicle_image.dart';

class VehicleDetails {
  String? uid;
  List<VehicleImage>? vehicleImage;
  String? number;
  String? vehicleName;
  String? vehicleId;
  String? vehicleType;
  int? makeYear;
  String? numberPlateType;
  String? checkinTime;
  double? totalHrTime;
  String? checkOutTime;
  double? remainingSeconds;
  OwnerDetails? ownerDetails;
  String? violationStatus;
  String? coorparateVehicle;
  String? vehicleOwnership;
  bool? isSharedVehicle;
  List<ValetDetails>? valetDetails;
  List<SubscriptionModel>? subscription;
  ViolationDetails? violationDetails;
  String? nextAction;

  VehicleDetails({
    this.uid,
    this.vehicleImage,
    this.number,
    this.vehicleName,
    this.vehicleId,
    this.vehicleType,
    this.makeYear,
    this.numberPlateType,
    this.checkinTime,
    this.totalHrTime,
    this.checkOutTime,
    this.remainingSeconds,
    this.ownerDetails,
    this.violationStatus,
    this.coorparateVehicle,
    this.vehicleOwnership,
    this.isSharedVehicle,
    this.valetDetails,
    this.subscription,
    this.violationDetails,
    this.nextAction,
  });

  VehicleDetails.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    if (json['vehicle_image'] != null) {
      vehicleImage = <VehicleImage>[];
      json['vehicle_image'].forEach((v) {
        vehicleImage!.add(VehicleImage.fromJson(v));
      });
    }
    number = json['number'];
    vehicleName = json['vehicle_name'];
    vehicleId = json['vehicle_id'];
    vehicleType = json['vehicle_type'];
    makeYear = int.tryParse(json['make_year'].toString());
    numberPlateType = json['number_plate_type'];
    checkinTime = json['checkin_time'];
    totalHrTime = double.tryParse(json['total_hr_time'].toString());
    checkOutTime = json['check_out_time'];
    remainingSeconds = double.tryParse(json['remaining_seconds'].toString());
    ownerDetails = json['owner_details'] != null
        ? OwnerDetails.fromJson(json['owner_details'])
        : null;
    violationStatus = json['violation_status'];
    coorparateVehicle = json['coorparate_vehicle'];
    vehicleOwnership = json['vehicle_ownership'];
    isSharedVehicle = json['is_shared_vehicle'];
    if (json['valet_details'] != null) {
      valetDetails = <ValetDetails>[];
      json['valet_details'].forEach((v) {
        valetDetails!.add(ValetDetails.fromJson(v));
      });
    }
    if (json['subscription'] != null) {
      subscription = <SubscriptionModel>[];
      json['subscription'].forEach((v) {
        subscription!.add(SubscriptionModel.fromJson(v));
      });
    }
    if (json['violation_details'] != null) {
      violationDetails = ViolationDetails.fromJson(json['violation_details']);
    }
    nextAction = json['next_action'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    if (vehicleImage != null) {
      data['vehicle_image'] = vehicleImage!.map((v) => v.toJson()).toList();
    }
    data['number'] = number;
    data['vehicle_name'] = vehicleName;
    data['vehicle_id'] = vehicleId;
    data['vehicle_type'] = vehicleType;
    data['make_year'] = makeYear;
    data['number_plate_type'] = numberPlateType;
    data['checkin_time'] = checkinTime;
    data['total_hr_time'] = totalHrTime;
    data['check_out_time'] = checkOutTime;
    data['remaining_seconds'] = remainingSeconds;
    if (ownerDetails != null) {
      data['owner_details'] = ownerDetails!.toJson();
    }
    data['violation_status'] = violationStatus;
    data['coorparate_vehicle'] = coorparateVehicle;
    data['vehicle_ownership'] = vehicleOwnership;
    data['is_shared_vehicle'] = isSharedVehicle;
    if (valetDetails != null) {
      data['valet_details'] = valetDetails!.map((v) => v.toJson()).toList();
    }
    if (subscription != null) {
      data['subscription'] = subscription!.map((v) => v.toJson()).toList();
    }
    if (violationDetails != null) {
      data['violation_details'] = violationDetails!.toJson();
    }
    data['next_action'] = nextAction;
    return data;
  }
}

class OwnerDetails {
  String? name;
  String? phonenumber;
  String? profileImage;

  OwnerDetails({this.name, this.phonenumber, this.profileImage});

  OwnerDetails.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    phonenumber = json['phonenumber'];
    profileImage = json['profile_image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['name'] = name;
    data['phonenumber'] = phonenumber;
    data['profile_image'] = profileImage;
    return data;
  }
}

class ValetDetails {
  String? uid;
  String? valetId;
  String? valetPerson;
  String? valetRequestUid;
  String? bookingDate;
  String? bookingTime;
  String? valetDate;
  String? valetTime;
  String? status;
  ValetPersonImage? valetPersonImage;

  ValetDetails({
    this.uid,
    this.valetId,
    this.valetPerson,
    this.valetRequestUid,
    this.bookingDate,
    this.bookingTime,
    this.valetDate,
    this.valetTime,
    this.status,
    this.valetPersonImage,
  });

  ValetDetails.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    valetId = json['valet_id'];
    valetPerson = json['valet_person'];
    valetRequestUid = json['valet_request_uid'];
    bookingDate = json['booking_date'];
    bookingTime = json['booking_time'];
    valetDate = json['valet_date'];
    valetTime = json['valet_time'];
    status = json['status'];
    valetPersonImage = json['valet_person_image'] != null
        ? ValetPersonImage.fromJson(json['valet_person_image'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['valet_id'] = valetId;
    data['valet_person'] = valetPerson;
    data['valet_request_uid'] = valetRequestUid;
    data['booking_date'] = bookingDate;
    data['booking_time'] = bookingTime;
    data['valet_date'] = valetDate;
    data['valet_time'] = valetTime;
    data['status'] = status;
    if (valetPersonImage != null) {
      data['valet_person_image'] = valetPersonImage!.toJson();
    }
    return data;
  }
}

class ValetPersonImage {
  String? uid;
  String? image;

  ValetPersonImage({this.uid, this.image});

  ValetPersonImage.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    image = json['image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['image'] = image;
    return data;
  }
}

class ViolationDetails {
  String? uid;
  String? violationId;
  String? paymentStatus;
  String? violationStatus;
  String? violationType;
  String? violationDateTime;
  String? violationGrancePeriodEnd;
  int? violationRequestType;
  String? violationRequestUid;
  bool? gracePeriodIsOver;
  int? gracePeriodSeconds;

  ViolationDetails({
    this.uid,
    this.violationId,
    this.paymentStatus,
    this.violationStatus,
    this.violationType,
    this.violationDateTime,
    this.violationGrancePeriodEnd,
    this.violationRequestType,
    this.violationRequestUid,
    this.gracePeriodIsOver,
    this.gracePeriodSeconds,
  });

  ViolationDetails.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    violationId = json['violation_id'];
    paymentStatus = json['payment_status'];
    violationStatus = json['violation_status'];
    violationType = json['violation_type'];
    violationDateTime = json['violation_date_time'];
    violationGrancePeriodEnd = json['violation_grance_period_end'];
    violationRequestType =
        int.tryParse(json['violation_request_type'].toString());
    violationRequestUid = json['violation_request_uid'];
    gracePeriodIsOver = json['grace_period_is_over'];
    gracePeriodSeconds = int.tryParse(json['grace_period_seconds'].toString());
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['uid'] = uid;
    data['violation_id'] = violationId;
    data['payment_status'] = paymentStatus;
    data['violation_status'] = violationStatus;
    data['violation_type'] = violationType;
    data['violation_date_time'] = violationDateTime;
    data['violation_grance_period_end'] = violationGrancePeriodEnd;
    data['violation_request_type'] = violationRequestType;
    data['violation_request_uid'] = violationRequestUid;
    data['grace_period_is_over'] = gracePeriodIsOver;
    data['grace_period_seconds'] = gracePeriodSeconds;
    return data;
  }
}

class SubscriptionModel {
  String? subscriptionName;
  String? location;
  String? type;
  String? subscriptionTime;
  String? expiryDate;

  SubscriptionModel(
      {this.subscriptionName,
      this.location,
      this.type,
      this.subscriptionTime,
      this.expiryDate});

  SubscriptionModel.fromJson(Map<String, dynamic> json) {
    subscriptionName = json['subscription_name'];
    location = json['location'];
    type = json['type'];
    subscriptionTime = json['subscription_time'];
    expiryDate = json['expiry_date'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['subscription_name'] = subscriptionName;
    data['location'] = location;
    data['type'] = type;
    data['subscription_time'] = subscriptionTime;
    data['expiry_date'] = expiryDate;
    return data;
  }
}
