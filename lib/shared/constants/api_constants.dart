import 'package:albalad_operator_app/shared/helper/shared_preference_helper.dart';
import 'package:albalad_operator_app/shared/models/logged_in_user.dart';
import 'package:albalad_operator_app/providers/global_providers.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// Enum for different environments
enum AppEnvironment {
  development,
  staging,
  microservice,
  // production,
}

class ApiConstants {
  // Environment-specific base URLs
  // static const _baseUrlDev = 'https://al-balad.e8demo.com';
  static const _baseUrlDev = 'https://al-balad-test.e8demo.com';
  static const _baseUrlStage = 'https://al-baladstage.e8demo.com';
  static const _baseUrlMicroservice =
      'https://balad-back-office-api.e8demo.com';
  // static const _baseUrlProd = 'https://al-baladstage.e8demo.com';

  // Current environment (default to production)
  static AppEnvironment _currentEnvironment = AppEnvironment.development;

  // Getter for baseURL based on current environment
  static String get baseURL {
    switch (_currentEnvironment) {
      case AppEnvironment.development:
        return _baseUrlDev;
      case AppEnvironment.staging:
        return _baseUrlStage;
      case AppEnvironment.microservice:
        return _baseUrlMicroservice;
      // case AppEnvironment.production:
      //   return _baseUrlProd;
    }
  }

  static String get apiBaseURL => '$baseURL/api/operator/v1';

  // Method to set environment (call this from your splash screen)
  static void setEnvironment(AppEnvironment env) async {
    _currentEnvironment = env;
    await SharedPreferenceHelper.instance.saveData(
      'environment',
      env.toString().split('.').last,
    );
  }

  // static const baseURL = 'https://al-balad-stage.e8demo.com'; //STAGE
  // static const baseURL = 'https://al-balad.e8demo.com';
  // static const apiBaseURL = '$baseURL/api/operator/v1';

  // Authentication-related endpoints
  static String get encryptionSecretKey => '$apiBaseURL/encryption-secret-key/';
  static String get signin => '$apiBaseURL/signin/';
  static String get profile => '$apiBaseURL/profile/';
  static String get profileUpdate => '$apiBaseURL/profile-update/';
  static String get forgotPassword => '$apiBaseURL/forgot-password-otp/';
  static String get homePage => '$apiBaseURL/home-page/';
  static String get parkingVehicleList => '$apiBaseURL/parking-vehicle-list/';
  static String get vehicleDetails => '$apiBaseURL/vehicle-details/';
  static String get termsAndConditions => '$apiBaseURL/terms-and-condition/';
  static String get privacyPolicy => '$apiBaseURL/privacy-policy/';
  static String get faqs => '$apiBaseURL/faqs/';
  static String get contactUs => '$apiBaseURL/contact-us/';
  static String get violationTypes => '$apiBaseURL/violation-types/';
  static String get assignViolation => '$apiBaseURL/assign-violation/';
  static String get assignParkingViolation =>
      '$apiBaseURL/assign-parking-violation/';
  static String get listOperators => '$apiBaseURL/list-operators/';
  static String get assignClampingViolation =>
      '$apiBaseURL/assign-clamping-violation/';
  static String get currentViolationDetails =>
      '$apiBaseURL/current-violation-details/';
  static String get settlementTypes => '$apiBaseURL/settlement-types/';
  static String get paymentMethods => '$apiBaseURL/payment-methods/';
  static String get settleCloseViolation =>
      '$apiBaseURL/settle-close-violation/';
  static String get assignTowingViolation =>
      '$apiBaseURL/assign-towing-violation/';
  static String get currentViolations => '$apiBaseURL/current-violations/';
  static String get violationNoticesVehicleList =>
      '$apiBaseURL/violation-notices-vehicle-list/';
  static String get parkingViolationList =>
      '$apiBaseURL/parking-violation-list/';
  static String get parkingViolationDetails =>
      '$apiBaseURL/parking-violation-details/';
  static String get changePassword => '$apiBaseURL/change-password/';
  static String get clampedVehicleList => '$apiBaseURL/clamped-vehicle-list/';
  static String get towClampedVehiclesList =>
      '$apiBaseURL/tow-clamped-vehicles-list/';
  static String get valetOperators => '$apiBaseURL/valet-operators/';
  static String get valetLocations => '$apiBaseURL/valet-locations/';
  static String get assignValet => '$apiBaseURL/assign-valet/';
  static String get generateTicket => '$apiBaseURL/generate-ticket/';
  static String get enterVehicles => '$apiBaseURL/enter-vehicles/';
  static String get updateViolation => '$apiBaseURL/update-violation/';
  static String get valetVehicles => '$apiBaseURL/valet-vehicles/';
  static String get valetSettlementStatus =>
      '$apiBaseURL/valet-settlement-status/';
  static String get valetSettlement => '$apiBaseURL/valet-settlement/';
  static String get exitVehicle => '$apiBaseURL/exit-vehicle/';
  static String get vehicleRequest => '$apiBaseURL/vehicle-request/';
  static String get clampingRequestList => '$apiBaseURL/clamping-request-list/';
  static String get clampedVehicle => '$apiBaseURL/clamped-vehicle/';
  static String get clampTowVehicle => '$apiBaseURL/clamp-tow-vehicle/';
  static String get closeClampingViolation =>
      '$apiBaseURL/close-clamping-violation/';
  static String get closeTowingViolation =>
      '$apiBaseURL/close-towing-violation/';
  static String get listTowingRequest => '$apiBaseURL/list-towing-request/';
  static String get towVehicle => '$apiBaseURL/tow-vehicle/';
  static String get valetRequest => '$apiBaseURL/valet-request/';
  static String get clampedVehicleDetails =>
      '$apiBaseURL/clamped-vehicle-details/';
  static String get releaseClampedVehicle =>
      '$apiBaseURL/release-clamped-vehicle/';
  static String get towedVehicleList => '$apiBaseURL/towed-vehicle-list/';
  static String get towedVehicleDetails => '$apiBaseURL/towed-vehicle-details/';
  static String get releaseTowedVehicle => '$apiBaseURL/release-towed-vehicle/';
  static String get updateVehicleLocation =>
      '$apiBaseURL/update-vehicle-location/';
  static String get vehicleSearch => '$apiBaseURL/vehicle-search/';
  static String get assignDirectClamping =>
      '$apiBaseURL/assign-direct-clamping/';
  static String get assignDirectTowing => '$apiBaseURL/assign-direct-towing/';
  static String get vehiclePlateType => '$apiBaseURL/vehicle-plate-type/';
  static String get countries => '$apiBaseURL/countries/';
  static String get vehicleColor => '$apiBaseURL/vehicle-color/';
  static String get vehicleType => '$apiBaseURL/vehicle-type/';
  static String get vehicle => '$apiBaseURL/vehicle/';
  static String get notifications => '$apiBaseURL/notifications/';
  static String get readNotification => '$apiBaseURL/read-notification/';
  static String get notificationCount => '$apiBaseURL/notification-count/';
  static String get languageTranslation => '$apiBaseURL/language-translation/';
  static String get assignedClampingViolationList =>
      '$apiBaseURL/assigned-clamping-violation-list/';
  static String get assignedTowingViolationList =>
      '$apiBaseURL/assigned-towing-violation-list/';
  static String get signout => '$apiBaseURL/signout/';
  static String get mobileApplicationDetail =>
      '$apiBaseURL/mobile-application-detail/';
  static String get refreshFcmToken => '$apiBaseURL/refresh-fcm-token/';

  static Future<Map<String, String>> authHeaders() async {
    return {
      'time-zone': await getTimezone(),
      'X-Client-Type': 'mobile',
      'X-Environment': _currentEnvironment.toString().split('.').last,
      'Accept-Language': _getLanguageCode(),
      'Authorization': 'Bearer ${LoggedInUser.token?.accessToken}'
    };
  }

  static _getLanguageCode() {
    return SharedPreferenceHelper.instance.getData('locale') ?? 'en';
  }

  static Future<Map<String, String>> authFormDataHeaders() async {
    final container = ProviderContainer();
    final appLocale = container.read(localeProvider);
    container.dispose();
    return {
      'time-zone': await getTimezone(),
      'X-Client-Type': 'mobile',
      'X-Environment': _currentEnvironment.toString().split('.').last,
      'Accept-Language': appLocale.toLanguageTag(),
      'Authorization': 'Bearer ${LoggedInUser.token?.accessToken}',
      "Content-Type": "multipart/form-data",
    };
  }

  static Future<Map<String, String>> headers() async {
    return {
      'time-zone': await getTimezone(),
      'X-Client-Type': 'mobile',
      'X-Environment': _currentEnvironment.toString().split('.').last,
      'Accept-Language': _getLanguageCode(),
    };
  }

  // Optional: Get current environment (useful for UI indicators)
  static AppEnvironment get currentEnvironment => _currentEnvironment;

  static Future<void> initializeEnvironment() async {
    final savedEnv = SharedPreferenceHelper.instance.getData('environment');
    if (savedEnv != null) {
      _currentEnvironment = AppEnvironment.values.firstWhere(
        (e) => e.toString().split('.').last == savedEnv,
        orElse: () => AppEnvironment.development,
      );
    }
  }
}
