import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TextStyles {
  //fontSize 10
  static TextStyle get ts10w400cA1A09B => TextStyle(
        fontSize: 10.sp,
        color: ColorConstants.colorA1A09B,
        fontWeight: FontWeight.w400,
      );
  static TextStyle get ts10w400c878686 => TextStyle(
        fontSize: 10.sp,
        fontWeight: FontWeight.w400,
        color: ColorConstants.color878686,
      );
  static TextStyle get ts10w500c94684E => TextStyle(
        fontSize: 10.sp,
        fontWeight: FontWeight.w400,
        color: ColorConstants.color94684E,
      );
  static TextStyle get ts10w400c1E1E1E => TextStyle(
        fontSize: 10.sp,
        fontWeight: FontWeight.w400,
        color: ColorConstants.color1E1E1E,
      );

  static TextStyle get ts10w400c959595 => TextStyle(
        fontSize: 10.sp,
        fontWeight: FontWeight.w400,
        color: ColorConstants.color959595,
      );

  static TextStyle get ts10w700c353535 => TextStyle(
        fontSize: 10.sp,
        fontWeight: FontWeight.w700,
        color: ColorConstants.color353535,
      );

  //fontSize 12
  static TextStyle get ts12w400c505050 => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w400,
        color: ColorConstants.color505050,
      );

  static TextStyle get ts12w400c606060 => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w400,
        color: ColorConstants.color606060,
      );

  static TextStyle get ts12w400c4C4C4C => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w400,
        color: ColorConstants.color4C4C4C,
      );

  static TextStyle get ts12w500c4C4C4C => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w500,
        color: ColorConstants.color4C4C4C,
      );

  static TextStyle get ts12w400cA39A9A => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w400,
        color: ColorConstants.colorA39A9A,
      );

  static TextStyle get ts12w400c4D4D4D => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w400,
        color: ColorConstants.color4D4D4D,
      );
  static TextStyle get ts12w400c44322D => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w400,
        color: ColorConstants.primaryColor,
      );

  static TextStyle get ts12w400c959595 => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w400,
        color: ColorConstants.color959595,
      );

  static TextStyle get ts12w500c94684E => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w500,
        color: ColorConstants.color94684E,
      );

  static TextStyle get ts12w600w94684E => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.color94684E,
      );

  static TextStyle get ts12w600wE1DDD2 => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.colorE1DDD2,
      );

  static TextStyle get ts12w500w94684E => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w500,
        color: ColorConstants.color94684E,
      );
  static TextStyle get ts12w600c4D4D4D => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.color4D4D4D,
      );

  static TextStyle get ts12w600c94684E => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.color94684E,
      );
  static TextStyle get ts12w600c44322D => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.primaryColor,
      );
  static TextStyle get ts12w600cE1DDD2 => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.colorE1DDD2,
      );

  static TextStyle get ts12w600c32993E => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.color32993E,
      );

  static TextStyle get ts12w500cE8B020 => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w500,
        color: ColorConstants.colorE8B020,
      );
  static TextStyle get ts12w500c181818 => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w500,
        color: ColorConstants.color181818,
      );
  static TextStyle get ts12w600c181818 => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.color181818,
      );

  static TextStyle get ts12w600cE8B020 => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.colorE8B020,
      );

  static TextStyle get ts12w500c32993E => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w500,
        color: ColorConstants.color32993E,
      );

  static TextStyle get ts12w600c353535 => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.color353535,
      );

  static TextStyle get ts12w700c353535 => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w700,
        color: ColorConstants.color353535,
      );

  static TextStyle get ts12w700c4D4D4D => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w700,
        color: ColorConstants.color4D4D4D,
      );

  static TextStyle get ts12w800c4D4D4D => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w800,
        color: ColorConstants.color4D4D4D,
      );

  static TextStyle get ts12w700c44322D => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w700,
        color: ColorConstants.primaryColor,
      );
//fontSize 13
  static TextStyle get ts13w400c959595 => TextStyle(
        fontSize: 13.sp,
        fontWeight: FontWeight.w400,
        color: ColorConstants.color959595,
      );

  static TextStyle get ts13w600c44322D => TextStyle(
        fontSize: 13.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.primaryColor,
      );
  //fontSize 14
  static TextStyle get ts14w400c181818 => TextStyle(
        fontSize: 14.sp,
        color: ColorConstants.color181818,
        fontWeight: FontWeight.w400,
      );

  static TextStyle get ts14w400c959595 => TextStyle(
        fontSize: 14.sp,
        color: ColorConstants.color959595,
        fontWeight: FontWeight.w400,
      );

  static TextStyle get ts14w400c4D4D4D => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w400,
        color: ColorConstants.color4D4D4D,
      );

  static TextStyle get ts16w400cFFFFFF => TextStyle(
        fontWeight: FontWeight.w400,
        fontSize: 14.sp,
        color: Colors.white,
      );

  static TextStyle get ts14w500c44322D => TextStyle(
        fontWeight: FontWeight.w500,
        fontSize: 14.sp,
        color: ColorConstants.primaryColor,
      );

  static TextStyle get ts14w500CECECE => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w500,
        color: ColorConstants.colorCECECE,
      );

  static TextStyle get ts14w500c959595 => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w500,
        color: ColorConstants.color959595,
      );

  static TextStyle get ts14w500c353535 => TextStyle(
        fontWeight: FontWeight.w500,
        fontSize: 14.sp,
        color: ColorConstants.color353535,
      );

  static TextStyle get ts14w600c353535 => TextStyle(
        fontWeight: FontWeight.w600,
        fontSize: 14.sp,
        color: ColorConstants.color353535,
      );

  static TextStyle get ts14w500c94684E => TextStyle(
        fontWeight: FontWeight.w500,
        fontSize: 14.sp,
        color: ColorConstants.color94684E,
      );

  static TextStyle get ts14w500c181818 => TextStyle(
        fontWeight: FontWeight.w500,
        fontSize: 14.sp,
        color: ColorConstants.color181818,
      );

  static TextStyle get ts14w500cC6C6C6 => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w500,
        color: ColorConstants.colorC6C6C6,
      );

  static TextStyle get ts14w600cC6C6C6 => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.colorC6C6C6,
      );

  static TextStyle get ts14w500c505050 => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w500,
        color: ColorConstants.color505050,
      );
  static TextStyle get ts14w600c181818 => TextStyle(
        fontWeight: FontWeight.w600,
        fontSize: 14.sp,
        color: ColorConstants.color181818,
      );

  static TextStyle get ts14w600CECECE => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.colorCECECE,
      );

  static TextStyle get ts14w600c959595 => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.color959595,
      );

  static TextStyle get ts14w600c44322D => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.primaryColor,
      );
  static TextStyle get ts14w700c1E1E1E => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w700,
        color: ColorConstants.color1E1E1E,
      );

  static TextStyle get ts14w700c94684E => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w700,
        color: ColorConstants.color94684E,
      );

  static TextStyle get ts14w400c949494 => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w400,
        color: ColorConstants.color949494,
      );
  //fontSize 15

  static TextStyle get ts15w400c181818 => TextStyle(
        fontWeight: FontWeight.w400,
        fontSize: 15.sp,
        color: ColorConstants.color181818,
      );

  //fontSize 16
  static TextStyle get ts16w400c959595 => TextStyle(
        fontWeight: FontWeight.w400,
        fontSize: 16.sp,
        color: ColorConstants.color959595,
      );
  static TextStyle get ts16w500c44322D => TextStyle(
        fontWeight: FontWeight.w500,
        fontSize: 16.sp,
        color: ColorConstants.primaryColor,
      );

  static TextStyle get ts16w500c353535 => TextStyle(
        fontWeight: FontWeight.w500,
        fontSize: 16.sp,
        color: ColorConstants.color353535,
      );

  static TextStyle get ts16w600c44322D => TextStyle(
        fontWeight: FontWeight.w600,
        fontSize: 16.sp,
        color: ColorConstants.primaryColor,
      );

  static TextStyle get ts16w600c181818 => TextStyle(
        fontWeight: FontWeight.w600,
        fontSize: 16.sp,
        color: ColorConstants.color181818,
      );
  static TextStyle get ts16w600cE1DDD2 => TextStyle(
        fontWeight: FontWeight.w600,
        fontSize: 16.sp,
        color: ColorConstants.colorE1DDD2,
      );

  static TextStyle get ts16w700c44322D => TextStyle(
        fontWeight: FontWeight.w700,
        fontSize: 16.sp,
        color: ColorConstants.primaryColor,
      );

  static TextStyle get ts16w700c94684E => TextStyle(
        fontWeight: FontWeight.w700,
        fontSize: 16.sp,
        color: ColorConstants.color94684E,
      );

  //fontSize 18
  static TextStyle get ts18w500c181818 => TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.w500,
        color: ColorConstants.color181818,
      );

  static TextStyle get ts18w600c181818 => TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.color181818,
      );

  static TextStyle get ts18w500c353535 => TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.w500,
        color: ColorConstants.color353535,
      );

  static TextStyle get ts18w600c353535 => TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.color353535,
      );

  static TextStyle get ts18w600c44322D => TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.primaryColor,
      );

  static TextStyle get ts18w700c4D4D4D => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w700,
        color: ColorConstants.color4D4D4D,
      );

//fontSize 22

  static TextStyle get ts22w600c44322D => TextStyle(
        fontSize: 22.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.primaryColor,
      );

//fontSize 28
  static TextStyle get ts28w700c94684E => TextStyle(
      fontSize: 28.sp,
      fontWeight: FontWeight.w700,
      color: ColorConstants.color94684E);

  //fontSize 30
  static TextStyle get ts30w600c44322D => TextStyle(
        fontSize: 30.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.primaryColor,
      );

  static TextStyle get ts30w700c44322D => TextStyle(
      fontSize: 30.sp,
      fontWeight: FontWeight.w700,
      color: ColorConstants.primaryColor);

  static TextStyle error(BuildContext context) => TextStyle(
        fontSize: 12.sp,
        color: Theme.of(context).colorScheme.error,
      );
}
