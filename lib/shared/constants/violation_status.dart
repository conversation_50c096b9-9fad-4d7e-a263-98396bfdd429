class ViolationStatus {
  // Private constructor to prevent instantiation
  ViolationStatus._();

  static const int parkingViolation = 1;
  static const int clampingAssigned = 2;
  static const int clamped = 3;
  static const int towingAssigned = 4;
  static const int towed = 5;
  static const int settled = 6;
  static const int released = 7;
  static const int closed = 8;
  static const int cancelled = 9;
  static const int enabled = 10;
  static const int disabled = 11;
  static const int absconding = 12;

  // Helper method to get status name from code
  static String getStatusName(int status) {
    switch (status) {
      case parkingViolation:
        return 'Parking Violation';
      case clampingAssigned:
        return 'Clamping Assigned';
      case clamped:
        return 'Clamped';
      case towingAssigned:
        return 'Towing Assigned';
      case towed:
        return 'Towed';
      case settled:
        return 'Settled';
      case released:
        return 'Released';
      case closed:
        return 'Closed';
      case cancelled:
        return 'Cancelled';
      case enabled:
        return 'Enabled';
      case disabled:
        return 'Disabled';
      case absconding:
        return 'Absconding';
      default:
        return 'Unknown Status';
    }
  }

  // Helper method to check if status is final (no more changes expected)
  static bool isFinalStatus(int status) {
    return status == settled ||
        status == released ||
        status == closed ||
        status == cancelled;
  }

  // Helper method to check if vehicle is immobilized
  static bool isVehicleImmobilized(int status) {
    return status == clamped || status == towed;
  }
}
