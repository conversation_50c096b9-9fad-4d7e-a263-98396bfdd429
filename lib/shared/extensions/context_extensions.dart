import 'package:albalad_operator_app/language/app_localizations.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// Translates a given key using the current AppLocalizations instance from the BuildContext.
String tr(BuildContext context, String key) {
  return context.loc.translate(key);
}

extension LocalizationExtension on BuildContext {
  AppLocalizations get loc => AppLocalizations.of(this)!;
}
