/// Utility class for URL validation
class UrlValidator {
  /// Validates if a URL string is valid and has proper format
  /// 
  /// Returns true if the URL is valid, false otherwise
  /// 
  /// Invalid URLs include:
  /// - null or empty strings
  /// - 'https://' without domain
  /// - URLs that don't parse correctly
  /// - URLs without absolute path
  static bool isValidUrl(String? url) {
    if (url == null || url.isEmpty) {
      return false;
    }
    
    // Check for common invalid patterns
    if (url == 'https://' || url == 'http://') {
      return false;
    }
    
    // Try to parse the URL
    final uri = Uri.tryParse(url);
    if (uri == null) {
      return false;
    }
    
    // Check if it has a proper scheme and host
    return uri.hasAbsolutePath && 
           uri.host.isNotEmpty && 
           (uri.scheme == 'http' || uri.scheme == 'https');
  }
  
  /// Returns a safe URL for image loading
  /// 
  /// If the provided URL is invalid, returns null
  /// This can be used with image widgets that handle null URLs gracefully
  static String? getSafeImageUrl(String? url) {
    return isValidUrl(url) ? url : null;
  }
  
  /// Returns a fallback URL if the provided URL is invalid
  /// 
  /// This is useful when you need a guaranteed non-null URL
  static String getUrlWithFallback(String? url, String fallback) {
    return isValidUrl(url) ? url! : fallback;
  }
}
