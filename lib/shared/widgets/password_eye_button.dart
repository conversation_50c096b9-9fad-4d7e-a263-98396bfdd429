import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class PasswordEyeButton extends StatelessWidget {
  final bool showPassword;
  final void Function()? onTap;
  const PasswordEyeButton({this.showPassword = false, this.onTap, super.key});

  @override
  Widget build(BuildContext context) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding:
            EdgeInsets.fromLTRB(isRTL ? 16.w : 0, 13.h, isRTL ? 0 : 16.w, 13.h),
        child: SvgPicture.asset(
          showPassword
              ? 'hide-password'.asIconSvg()
              : 'show-password'.asIconSvg(),
        ),
      ),
    );
  }
}
