import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/gradient_text.dart';
import 'package:albalad_operator_app/shared/widgets/language_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';

class ImageSourceSheet extends HookConsumerWidget {
  const ImageSourceSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final List<FileModel> options = [
      FileModel(tr(context, 'camera'), ImageSource.camera, 'camera-bold'),
      FileModel(tr(context, 'gallery'), ImageSource.gallery, 'gallery'),
    ];

    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 30.h, 16.w, 30.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            tr(context, 'select_source'),
            textAlign: TextAlign.center,
            style: TextStyles.ts18w500c181818,
          ),
          Gap(30.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            spacing: 30.w,
            children: options
                .map(
                  (e) => InkWell(
                    onTap: () async {
                      Navigator.pop(context, e.source);
                    },
                    child: LanguageCard(
                      image: e.icon,
                      language: e.name,
                      selected: false,
                      hideBorder: true,
                    ),
                  ),
                )
                .toList(),
          ),
          Gap(30.h),
        ],
      ),
    );
  }
}

class CameraGalleryCard extends StatelessWidget {
  final String image;
  final String language;
  final bool selected;
  const CameraGalleryCard(
      {required this.image,
      required this.language,
      required this.selected,
      super.key});

  @override
  Widget build(BuildContext context) {
    List<BoxShadow>? boxShadow;
    BoxBorder? border;
    Size imgContainerSize = Size(46.w, 46.h);
    Size imgSize = Size(44.w, 44.h);
    Color bgColor = const Color(0xFFF8F8F8);
    Gradient gradient = const LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [Color(0xFFF0F0F0), Color(0xFFF9F9F9)],
    );
    if (selected) {
      bgColor = const Color(0xFFEFDFD5);
      border = Border.all(
        color: Colors.white,
        width: 2.w,
      );
      boxShadow = [
        BoxShadow(
          offset: const Offset(0, 0),
          blurRadius: 8,
          spreadRadius: 2,
          color: ColorConstants.primaryColor.withValues(alpha: 0.1),
        ),
      ];
      imgContainerSize = Size(54.w, 54.h);
      imgSize = Size(51.w, 51.h);
      gradient = const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xFFE9D3C6), Color(0xFFEFDFD5)],
      );
    }
    return Container(
      width: selected ? 163.w : 141.w,
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(15.r),
        border: border,
        boxShadow: boxShadow,
      ),
      child: Column(
        children: [
          Gap(selected ? 28.h : 23.h),
          Container(
            height: imgContainerSize.height,
            width: imgContainerSize.width,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white,
                width: 1.w,
              ),
            ),
            child: ClipOval(
              child: SvgPicture.asset(
                image.asIconSvg(),
                height: imgSize.height,
                width: imgSize.width,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Gap(selected ? 10.h : 8.h),
          Text(
            language,
            style: TextStyles.ts14w500c44322D,
          ),
          Gap(selected ? 5.h : 2.h),
          Align(
            heightFactor: 0.8,
            child: GradientText(
              language,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 33.sp,
              ),
              gradient: gradient,
            ),
          )
        ],
      ),
    );
  }
}

class FileModel {
  String name;
  ImageSource source;
  String icon;

  FileModel(this.name, this.source, this.icon);
}
