import 'package:albalad_operator_app/features/authentication/services/auth_services.dart';
import 'package:albalad_operator_app/features/profile/provider/profile_provider.dart';
import 'package:albalad_operator_app/providers/global_providers.dart';
import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:albalad_operator_app/shared/helper/shared_preference_helper.dart';
import 'package:albalad_operator_app/shared/services/fcm_services.dart';
import 'package:albalad_operator_app/shared/widgets/gradient_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class LanguageButton extends ConsumerWidget {
  const LanguageButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    bool isRTL = Directionality.of(context) == TextDirection.rtl;
    return OutlinedButton.icon(
      onPressed: () => showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        builder: (context) => const LangSelectionBottomSheet(),
      ),
      label: Text(ref.watch(localeProvider).languageCode.toUpperCase()),
      icon: SvgPicture.asset(
        'language'.asIconSvg(),
        height: 24.h,
        width: 24.w,
      ),
      iconAlignment: IconAlignment.end,
      style: OutlinedButton.styleFrom(
        backgroundColor: ColorConstants.colorDE542D.withValues(alpha: 0.1),
        textStyle: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w500,
        ),
        side: BorderSide(
          color: ColorConstants.colorCCB5A7,
          width: 1.w,
        ),
        padding: EdgeInsets.only(
          left: isRTL ? 4.w : 10.w,
          right: isRTL ? 10.w : 4.w,
        ),
        minimumSize: Size(72.w, 32.h),
      ),
    );
  }
}

class ProfileLanguageButton extends ConsumerWidget {
  const ProfileLanguageButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    bool isRTL = Directionality.of(context) == TextDirection.rtl;
    return OutlinedButton.icon(
      onPressed: () => showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        builder: (context) => const LangSelectionBottomSheet(),
      ),
      label: Text(ref.watch(localeProvider).languageCode.toUpperCase()),
      icon: SvgPicture.asset('language'.asIconSvg()),
      iconAlignment: IconAlignment.end,
      style: OutlinedButton.styleFrom(
        backgroundColor: ColorConstants.colorDE542D.withValues(alpha: 0.1),
        textStyle: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.w500,
        ),
        side: BorderSide(
          color: ColorConstants.colorCCB5A7,
          width: 1.w,
        ),
        padding: EdgeInsets.only(
          left: isRTL ? 4.w : 10.w,
          right: isRTL ? 10.w : 4.w,
        ),
        minimumSize: Size(59.17.w, 26.3.h),
      ),
    );
  }
}

class LangSelectionBottomSheet extends HookConsumerWidget {
  const LangSelectionBottomSheet({super.key});

  List<LangLocale> get locales => [
        LangLocale(appLocalization.translate('arabic'), 'ar', 'arabic'),
        LangLocale(appLocalization.translate('english'), 'en', 'english'),
      ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final locale = ref.watch(localeProvider);
    final selected = useState(locale);
    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 30.h, 16.w, 30.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            tr(context, 'chooseYourPreferredLanguage'),
            textAlign: TextAlign.center,
            style: TextStyles.ts18w500c181818,
          ),
          Gap(30.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            spacing: 30.w,
            children: locales
                .map(
                  (e) => InkWell(
                    onTap: () => selected.value = Locale(e.languageCode),
                    child: LanguageCard(
                      image: e.image,
                      language: e.language,
                      selected: selected.value.languageCode == e.languageCode,
                    ),
                  ),
                )
                .toList(),
          ),
          Gap(30.h),
          ElevatedButton(
            onPressed: () {
              if (selected.value.languageCode == 'en') {
                FcmServices.subscribeEnglishTopic();
              } else {
                FcmServices.subscribeArabicTopic();
              }
              AuthServices().updateLanguage(selected.value.languageCode);
              SharedPreferenceHelper.instance
                  .saveData('locale', selected.value.languageCode);
              ref.read(localeProvider.notifier).state = selected.value;
              ref.invalidate(profileProvider);
              Navigator.pop(context);
            },
            child: Text(tr(context, 'continueString')),
          )
        ],
      ),
    );
  }
}

class LanguageCard extends StatelessWidget {
  final String image;
  final String language;
  final bool selected;
  final bool hideBorder;
  const LanguageCard(
      {required this.image,
      required this.language,
      required this.selected,
      this.hideBorder = false,
      super.key});

  @override
  Widget build(BuildContext context) {
    List<BoxShadow>? boxShadow;
    BoxBorder? border;
    Size imgContainerSize = Size(46.w, 46.h);
    Size imgSize = Size(44.w, 44.h);
    Color bgColor = ColorConstants.colorF9F9F9;
    Gradient gradient = const LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [ColorConstants.colorF0F0F0, ColorConstants.colorF9F9F9],
    );
    if (selected) {
      bgColor = const Color(0xFFEFDFD5);
      border = Border.all(
        color: Colors.white,
        width: 2.w,
      );
      boxShadow = [
        BoxShadow(
          offset: const Offset(0, 0),
          blurRadius: 8,
          spreadRadius: 2,
          color: ColorConstants.primaryColor.withValues(alpha: 0.1),
        ),
      ];
      imgContainerSize = Size(54.w, 54.h);
      imgSize = Size(51.w, 51.h);
      gradient = const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Color(0xFFE9D3C6), Color(0xFFEFDFD5)],
      );
    }
    return Container(
      width: selected ? 163.w : 141.w,
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(15.r),
        border: border,
        boxShadow: boxShadow,
      ),
      child: Column(
        children: [
          Gap(selected ? 28.h : 23.h),
          Container(
            height: imgContainerSize.height,
            width: imgContainerSize.width,
            decoration: BoxDecoration(
              color: hideBorder ? null : Colors.white,
              shape: BoxShape.circle,
              border: hideBorder
                  ? null
                  : Border.all(
                      color: Colors.white,
                      width: 1.w,
                    ),
            ),
            child: ClipOval(
              child: SvgPicture.asset(
                image.asIconSvg(),
                height: imgSize.height,
                width: imgSize.width,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Gap(selected ? 10.h : 8.h),
          Text(
            language,
            style: TextStyles.ts14w500c44322D,
          ),
          Gap(selected ? 5.h : 2.h),
          Align(
            heightFactor: 0.8,
            child: GradientText(
              language,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 33.sp,
              ),
              gradient: gradient,
            ),
          )
        ],
      ),
    );
  }
}

class LangLocale {
  final String language;
  final String languageCode;
  final String image;
  const LangLocale(this.language, this.languageCode, this.image);
}
