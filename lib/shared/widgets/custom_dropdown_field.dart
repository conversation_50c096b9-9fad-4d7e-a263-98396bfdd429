import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class CustomDropdownField<T> extends StatelessWidget {
  final String? label;
  final String? hint;
  final T? value;
  final List<DropdownMenuItem<T>>? items;
  final void Function(T?)? onChanged;
  final String? Function(T?)? validator;
  final AutovalidateMode? autovalidateMode;
  const CustomDropdownField({
    this.label,
    this.hint,
    required this.items,
    required this.onChanged,
    this.value,
    this.validator,
    this.autovalidateMode,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ...[
          Text(
            label!,
            style: TextStyles.ts12w400c505050,
          ),
          Gap(5.h)
        ],
        DropdownButtonFormField<T>(
          items: items,
          onChanged: onChanged,
          hint: hint != null
              ? Text(
                  hint ?? '',
                  style: TextStyles.ts14w600CECECE,
                )
              : null,
          icon: Icon(Icons.keyboard_arrow_down_rounded),
          value: value,
          validator: validator,
          autovalidateMode: autovalidateMode,
        )
      ],
    );
  }
}
