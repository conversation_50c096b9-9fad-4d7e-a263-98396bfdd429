import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:circular_gradient_spinner/circular_gradient_spinner.dart';
import 'package:flutter/material.dart';

class CustomGradientSpinner extends StatelessWidget {
  const CustomGradientSpinner({super.key});

  @override
  Widget build(BuildContext context) {
    return const CircularGradientSpinner(
      color: ColorConstants.primaryColor,
      size: 32,
      strokeWidth: 3,
      duration: Duration(milliseconds: 1000),
    );
  }
}
