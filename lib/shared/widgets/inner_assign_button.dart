import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class InnerAssignButton extends StatelessWidget {
  final String title;
  final void Function()? onPressed;
  const InnerAssignButton(
      {required this.title, required this.onPressed, super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        minimumSize: Size(103.w, 22.h),
        textStyle: TextStyles.ts12w600c181818,
        foregroundColor: ColorConstants.colorE1DDD2,
        padding: EdgeInsets.symmetric(horizontal: 10.w),
      ),
      child: Text(title),
    );
  }
}
