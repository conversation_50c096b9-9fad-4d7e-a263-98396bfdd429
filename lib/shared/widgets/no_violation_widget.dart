import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class NoViolationWidget extends StatelessWidget {
  const NoViolationWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SvgPicture.asset(
          'not-found'.asIconSvg(),
          height: 128.h,
          width: 128.w,
        ),
        Gap(30.h),
        Text(
          tr(context, 'no_violations_found'),
          style: TextStyles.ts28w700c94684E,
        ),
        Center(
          child: Text(
            tr(context, 'everything_looks_good'),
            style: TextStyles.ts14w400c4D4D4D,
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}
