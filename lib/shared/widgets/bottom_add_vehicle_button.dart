import 'dart:io';

import 'package:albalad_operator_app/features/add_vehicle/view/add_vehicle_screen.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BottomAddVehicleButton extends StatelessWidget {
  const BottomAddVehicleButton({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: true,
      child: Padding(
        padding: EdgeInsets.fromLTRB(
          16.w,
          0,
          16.w,
          Platform.isAndroid ? 30.h : 0,
        ),
        child: ElevatedButton(
          onPressed: () => Navigator.pushNamed(
            context,
            AddVehicleScreen.route,
          ),
          child: Text(tr(context, 'addVehicle')),
        ),
      ),
    );
  }
}
