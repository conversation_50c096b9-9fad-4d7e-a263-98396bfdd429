import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class CustomTextFormField extends StatelessWidget {
  final String? hintText;
  final String? labelText;
  final String? errorText;
  final TextInputType? keyboardType;
  final String? Function(String?)? validator;
  final TextEditingController? controller;
  final bool obscureText;
  final Widget? suffixIcon;
  final int? maxLines;
  final bool readOnly;
  final List<TextInputFormatter>? inputFormatters;
  final void Function()? onTap;
  final AutovalidateMode? autovalidateMode;
  final TextCapitalization textCapitalization;
  final ValueChanged<String>? onChanged;
  final int? maxLength;
  final Color? fillColor;

  const CustomTextFormField({
    this.hintText,
    this.labelText,
    this.errorText,
    this.keyboardType,
    this.validator,
    this.controller,
    this.obscureText = false,
    this.suffixIcon,
    this.maxLines = 1,
    this.readOnly = false,
    this.onTap,
    this.inputFormatters,
    this.autovalidateMode,
    this.textCapitalization = TextCapitalization.none,
    this.onChanged,
    this.maxLength,
    this.fillColor,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null) ...[
          Text(
            labelText!,
            style: TextStyles.ts12w400c505050,
          ),
          Gap(8.h),
        ],
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          obscureText: obscureText,
          maxLines: maxLines,
          readOnly: readOnly,
          onTap: onTap,
          textCapitalization: textCapitalization,
          inputFormatters: inputFormatters,
          onTapOutside: (event) =>
              FocusManager.instance.primaryFocus?.unfocus(),
          autovalidateMode: autovalidateMode,
          decoration: InputDecoration(
            errorText: errorText,
            hintText: hintText,
            suffixIcon: suffixIcon,
            errorMaxLines: 3,
            counterText: '',
            fillColor: fillColor,
            filled: fillColor != null,
          ),
          validator: validator,
          onChanged: onChanged,
          maxLength: maxLength,
        ),
      ],
    );
  }
}
