import 'dart:io';

import 'package:albalad_operator_app/shared/widgets/custom_gradient_spinner.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BottomNavigationBarElevatedButton extends StatelessWidget {
  final String title;
  final void Function()? onPressed;
  final bool isLoading;
  const BottomNavigationBarElevatedButton({
    required this.title,
    required this.onPressed,
    this.isLoading = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: true,
      child: Padding(
        padding: EdgeInsets.fromLTRB(
          16.w,
          0,
          16.w,
          Platform.isAndroid ? 30.h : 0,
        ),
        child: isLoading
            ? SizedBox(
                height: kBottomNavigationBarHeight,
                child: const Center(child: CustomGradientSpinner()),
              )
            : ElevatedButton(
                onPressed: onPressed,
                child: Text(title),
              ),
      ),
    );
  }
}
