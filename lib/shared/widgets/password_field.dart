import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/widgets/password_eye_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class Password<PERSON>ield extends HookWidget {
  final String? labelText;
  final String? errorText;
  final String? Function(String?)? validator;
  final TextEditingController? controller;
  const PasswordField({
    this.labelText,
    this.errorText,
    this.validator,
    this.controller,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final obscureText = useState(true);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null) ...[
          Text(
            labelText!,
            style: TextStyles.ts12w400c505050,
          ),
          Gap(5.h),
        ],
        TextFormField(
          controller: controller,
          obscureText: obscureText.value,
          onTapOutside: (event) =>
              FocusManager.instance.primaryFocus?.unfocus(),
          decoration: InputDecoration(
            errorText: errorText,
            hintText: '************',
            suffixIcon: PasswordEyeButton(
              showPassword: obscureText.value,
              onTap: () => obscureText.value = !obscureText.value,
            ),
            errorMaxLines: 3,
          ),
          validator: validator,
        ),
      ],
    );
  }
}
