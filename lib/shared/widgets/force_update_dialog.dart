import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:store_redirect/store_redirect.dart';

class ForceUpdateDialog extends StatelessWidget {
  const ForceUpdateDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            'force-update'.asIconSvg(),
            width: 92.w,
            height: 92.h,
          ),
          Gap(10.h),
          Text(
            'New Update Is Available',
            style: TextStyles.ts28w700c94684E,
            textAlign: TextAlign.center,
          ),
          Gap(10.h),
          Text(
            'The current version of this application\nis no longer supported.',
            style: TextStyles.ts16w400c959595,
            textAlign: TextAlign.center,
          ),
          Gap(50.h),
          ElevatedButton(
            onPressed: () =>
                StoreRedirect.redirect(androidAppId: '', iOSAppId: ''),
            child: Text('Update Now'),
          ),
        ],
      ),
    );
  }
}
