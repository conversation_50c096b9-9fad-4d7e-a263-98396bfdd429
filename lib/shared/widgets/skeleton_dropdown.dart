import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/widgets/custom_dropdown_field.dart';
import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SkeletonDropdown extends StatelessWidget {
  final bool enabled;
  const SkeletonDropdown({this.enabled = true, super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: enabled,
      child: CustomDropdownField(
        items: [],
        onChanged: (p0) {},
        label: tr(context, 'violation_type_star'),
        hint: tr(context, 'select_violation_type'),
      ),
    );
  }
}
