import 'package:albalad_operator_app/shared/widgets/no_network_widget.dart';
import 'package:flutter/material.dart';

class SmartScaffold extends StatelessWidget {
  final PreferredSizeWidget? appBar;
  final Widget? body;
  final Widget? bottomNavigationBar;
  final bool isInternetAvailable;
  final void Function()? retryConnection;
  const SmartScaffold({
    required this.retryConnection,
    this.appBar,
    this.body,
    this.bottomNavigationBar,
    this.isInternetAvailable = true,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar,
      body: isInternetAvailable
          ? body
          : Center(
              child: NoNetworkWidget(
                onRefresh: retryConnection,
              ),
            ),
      bottomNavigationBar: isInternetAvailable ? bottomNavigationBar : null,
    );
  }
}
