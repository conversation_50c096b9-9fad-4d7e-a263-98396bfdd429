import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AddButton extends StatelessWidget {
  final void Function()? onTap;
  const AddButton({this.onTap, super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        spacing: 5.w,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 18.h,
            width: 18.w,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: ColorConstants.color94684E,
            ),
            alignment: Alignment.center,
            child: const Icon(
              Icons.add_rounded,
              size: 15,
              color: Colors.white,
            ),
          ),
          Text(
            tr(context, 'add'),
            style: TextStyles.ts12w600c94684E,
          ),
        ],
      ),
    );
  }
}
