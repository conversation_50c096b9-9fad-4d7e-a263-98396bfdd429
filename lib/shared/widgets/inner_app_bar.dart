import 'package:albalad_operator_app/features/master/view/master_screen.dart';
import 'package:albalad_operator_app/providers/global_providers.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';

class InnerAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget? title;
  const InnerAppBar({this.title, super.key});

  @override
  Widget build(BuildContext context) {
    final isRTL = Directionality.of(context) == TextDirection.rtl;
    return AppBar(
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: Transform.flip(
          flipX: isRTL,
          child: SvgPicture.asset('arrow-left'.asIconSvg()),
        ),
      ),
      title: title,
      actions: [
        Consumer(
          builder: (context, ref, _) {
            return IconButton(
              onPressed: () {
                ref.read(masterProvider.notifier).state = 0;
                Navigator.popUntil(
                  context,
                  ModalRoute.withName(MasterScreen.route),
                );
              },
              icon: SvgPicture.asset(
                'home-button-icon'.asIconSvg(),
              ),
            );
          },
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
