import 'package:albalad_operator_app/providers/global_providers.dart';
import 'package:albalad_operator_app/shared/helper/shared_preference_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class LangSelectionBottomSheet extends ConsumerWidget {
  const LangSelectionBottomSheet({super.key});

  final List<LangLocale> locales = const [
    LangLocale('Arabic', 'ar'),
    LangLocale('English', 'en'),
  ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final locale = ref.watch(localeProvider);
    return ListView.separated(
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 50),
      itemBuilder: (context, index) {
        return InkWell(
          onTap: () {
            ref.read(localeProvider.notifier).state =
                Locale(locales[index].languageCode);
            SharedPreferenceHelper.instance
                .saveData('locale', locales[index].languageCode);
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (locale.languageCode == locales[index].languageCode)
                  const Icon(
                    Icons.check_circle_rounded,
                    color: Colors.black,
                  ),
                Text(
                  locales[index].language,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
        );
      },
      separatorBuilder: (context, index) => const Divider(),
      itemCount: locales.length,
    );
  }
}

class LangLocale {
  final String language;
  final String languageCode;
  const LangLocale(this.language, this.languageCode);
}
