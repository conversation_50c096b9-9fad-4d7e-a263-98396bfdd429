import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CurrencyIcon extends StatelessWidget {
  final double? height;
  final double? width;
  final Color? color;
  final EdgeInsetsGeometry? padding;
  const CurrencyIcon(
      {super.key, this.height, this.width, this.padding, this.color});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Image.asset('sar_icon'.asIconPng(),
          color: color, height: height ?? 10.h, width: width ?? 10.h),
    );
  }
}
