import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CircularProgressWithThumbPainter extends CustomPainter {
  final double progress;

  CircularProgressWithThumbPainter({required this.progress});

  @override
  void paint(Canvas canvas, Size size) {
    double lineWidth = 5.0;

    // Base paint for the circle
    Paint basePaint = Paint()
      ..color = Colors.grey[300]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = lineWidth
      ..strokeCap = StrokeCap.round;

    // Paint for the progress arc
    Paint progressPaint = Paint()
      ..color = progress == 1
          ? const Color(0xFFF50000)
          : const Color(0xFF32993E) // Red when progress is 0
      ..style = PaintingStyle.stroke
      ..strokeWidth = lineWidth
      ..strokeCap = StrokeCap.round;

    // double radius = (size.width - lineWidth) / 2;
    double radius = 31.5.w;
    Offset center = Offset(size.width / 2, size.height / 2);

    // Draw the base circle
    canvas.drawCircle(center, radius, basePaint);

    // Draw the progress arc
    double startAngle = -pi / 2; // Start at the top
    double sweepAngle = 2 * pi * progress;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      progressPaint,
    );

    // Draw the thumb with a white border and shadow
    double thumbX = center.dx + radius * cos(startAngle + sweepAngle);
    double thumbY = center.dy + radius * sin(startAngle + sweepAngle);

    // Draw shadow
    Paint shadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.2) // Shadow color
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4); // Blur effect

    canvas.drawCircle(Offset(thumbX, thumbY), lineWidth * 1.8, shadowPaint);

    // Draw the white border
    Paint thumbBorderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    canvas.drawCircle(
        Offset(thumbX, thumbY), lineWidth * 1.8, thumbBorderPaint);

    // Draw the thumb (red if progress is 0, green otherwise)
    Paint thumbPaint = Paint()
      ..color =
          progress == 1 ? const Color(0xFFF50000) : const Color(0xFF32993E)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(Offset(thumbX, thumbY), lineWidth * 1.5, thumbPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true; // Repaint whenever progress changes
  }
}
