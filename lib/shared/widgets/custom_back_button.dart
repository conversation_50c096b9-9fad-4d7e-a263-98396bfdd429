import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class CustomBackButton extends StatelessWidget {
  final VoidCallback? onPressed;
  const CustomBackButton({super.key, this.onPressed});

  @override
  Widget build(BuildContext context) {
    return IconButton(
        onPressed: onPressed ?? () => Navigator.pop(context),
        icon: SvgPicture.asset('arrow-left'.asIconSvg()));
  }
}
