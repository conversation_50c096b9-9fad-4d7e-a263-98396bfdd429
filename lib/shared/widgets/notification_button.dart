import 'package:albalad_operator_app/features/notifications/providers/notification_provider.dart';
import 'package:albalad_operator_app/features/notifications/view/notification_screen.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NotificationButton extends ConsumerWidget {
  const NotificationButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationCount = ref.watch(notificationCountProvider);
    return notificationCount.when(
      data: (count) => _buildButton(context, count),
      error: (error, stackTrace) => _buildButton(context, 0),
      loading: () => _buildButton(context, 0),
    );
  }

  _buildButton(BuildContext context, int count) {
    return IconButton(
      icon: Badge(
        largeSize: 10,
        smallSize: 10,
        isLabelVisible: count > 0,
        child: SvgPicture.asset(
          'notification-icon'.asIconSvg(),
          height: 24.h,
          width: 24.w,
        ),
      ),
      onPressed: () => Navigator.pushNamed(context, NotificationScreen.route),
    );
  }
}
