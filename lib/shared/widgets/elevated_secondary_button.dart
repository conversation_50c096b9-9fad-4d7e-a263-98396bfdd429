import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ElevatedSecondaryButton extends StatelessWidget {
  final String title;
  final void Function()? onPressed;
  const ElevatedSecondaryButton(
      {required this.title, required this.onPressed, super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: ColorConstants.colorF1EFE9,
        textStyle: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
        ),
        foregroundColor: ColorConstants.primaryColor,
      ),
      child: Text(title),
    );
  }
}
