import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:country_picker/country_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class MobileNumberField extends ConsumerWidget {
  final TextEditingController controller;
  final String phoneCode;
  final void Function(Country) onPhoneCodeChanged;
  final String? Function(String?)? validator;
  final String? errorText;
  final String? hintText;
  final bool autofocus;
  final int maxLength;
  final AutovalidateMode? autovalidateMode;
  const MobileNumberField({
    required this.controller,
    required this.phoneCode,
    required this.onPhoneCodeChanged,
    this.errorText,
    this.hintText,
    this.validator,
    this.autofocus = false,
    required this.maxLength,
    this.autovalidateMode,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TextForm<PERSON>ield(
      controller: controller,
      autofocus: autofocus,
      autovalidateMode: autovalidateMode,
      style: TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w600,
        color: ColorConstants.primaryColor,
      ),
      maxLength: maxLength,
      keyboardType: TextInputType.phone,
      onTapOutside: (_) => FocusManager.instance.primaryFocus?.unfocus(),
      validator: validator,
      decoration: InputDecoration(
        errorText: errorText,
        counterText: '',
        hintText: hintText,
        prefixIcon: InkWell(
          onTap: () => showCountryPicker(
            context: context,
            showPhoneCode: true,
            useSafeArea: true,
            moveAlongWithKeyboard: true,
            countryListTheme: CountryListThemeData(
              bottomSheetHeight: 0.75.sh,
            ),
            onSelect: onPhoneCodeChanged,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Gap(15.w),
              Text(
                phoneCode,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: ColorConstants.primaryColor,
                ),
                textDirection: TextDirection.ltr,
              ),
              const Icon(Icons.keyboard_arrow_down_rounded),
              Gap(5.w),
              SizedBox(
                height: 16.h,
                child: const VerticalDivider(
                  width: 0,
                  thickness: 1,
                  color: ColorConstants.dividerColor,
                ),
              ),
              Gap(10.w),
            ],
          ),
        ),
      ),
    );
  }
}
