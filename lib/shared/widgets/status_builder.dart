import 'package:albalad_operator_app/shared/constants/color_constants.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class StatusBuilder extends StatelessWidget {
  final String? status;
  const StatusBuilder({this.status, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: status?.toLowerCase() == 'settled'
            ? ColorConstants.colorEAFFEC
            : ColorConstants.colorFFF7E2,
        borderRadius: BorderRadius.circular(20.r),
      ),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.h),
      child: Text(
        status ?? '',
        style: status?.toLowerCase() == 'settled'
            ? TextStyles.ts12w600c32993E
            : TextStyles.ts12w600cE8B020,
      ),
    );
  }
}
