import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class NoNetworkWidget extends StatelessWidget {
  final void Function()? onRefresh;
  const NoNetworkWidget({required this.onRefresh, super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(
          'no_connection'.asImagePng(),
          width: 142.w,
          height: 128.h,
        ),
        Gap(40.w),
        Text(
          tr(context, 'connection_lost'),
          style: TextStyles.ts28w700c94684E,
        ),
        Gap(8.h),
        Text(
          tr(context, 'connection_lost_description'),
          style: TextStyles.ts16w400c959595,
          textAlign: TextAlign.center,
        ),
        Gap(37.h),
        ElevatedButton(
          onPressed: onRefresh,
          style: ElevatedButton.styleFrom(
            minimumSize: Size(212.w, 50.h),
          ),
          child: Text(
            tr(context, 'try_again'),
            style: TextStyles.ts16w600cE1DDD2,
          ),
        )
      ],
    );
  }
}
