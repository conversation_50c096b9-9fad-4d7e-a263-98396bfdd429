import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:albalad_operator_app/shared/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';

class CustomSearchableDropdown<T> extends StatelessWidget {
  final List<T> items;
  final String Function(T) displayBuilder;
  final String? hintText;
  final ValueChanged<T>? onChanged;
  final String? Function(String?)? validator;
  final String? labelText;
  final String? title;
  final String? searchHintText;
  final T? value;
  final AutovalidateMode? autovalidateMode;
  final Color? fillColor;

  const CustomSearchableDropdown({
    super.key,
    required this.items,
    required this.displayBuilder,
    this.value,
    this.validator,
    this.hintText,
    this.labelText,
    this.title,
    this.onChanged,
    this.searchHintText,
    this.autovalidateMode,
    this.fillColor,
  });

  void _showBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      showDragHandle: true,
      useSafeArea: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(26.r)),
      ),
      builder: (BuildContext context) => CustomSearchableSheet(
        items: items,
        displayBuilder: displayBuilder,
        title: title,
        searchHintText: searchHintText,
        onChanged: onChanged,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null) ...[
          Text(
            labelText!,
            style: TextStyles.ts12w400c505050,
          ),
          Gap(8.h),
        ],
        GestureDetector(
          onTap: () => _showBottomSheet(context),
          child: AbsorbPointer(
            child: TextFormField(
              controller: TextEditingController(
                  text: value != null ? displayBuilder(value as T) : null),
              validator: validator,
              autovalidateMode: autovalidateMode,
              decoration: InputDecoration(
                hintText: hintText,
                suffixIcon: Icon(Icons.keyboard_arrow_down_rounded),
                fillColor: fillColor,
                filled: fillColor != null,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class CustomSearchableSheet<T> extends HookWidget {
  final List<T> items;
  final String Function(T) displayBuilder;
  final ValueChanged<T>? onChanged;
  final String? title;
  final String? searchHintText;
  const CustomSearchableSheet({
    required this.items,
    required this.displayBuilder,
    this.onChanged,
    this.title,
    this.searchHintText,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    double bottom = MediaQuery.of(context).viewInsets.bottom;
    double paddingBottom = 30.h;
    if (bottom > 0) {
      paddingBottom = bottom;
    }
    final searchController = useTextEditingController();
    final filteredItems = useState<List<T>>(items);
    return SizedBox(
      height: 1.sh,
      child: SingleChildScrollView(
        child: Container(
          padding: EdgeInsets.fromLTRB(16.w, 0.h, 16.w, paddingBottom),
          child: Column(
            children: [
              if (title != null) ...[
                Text(
                  title!,
                  style: TextStyles.ts18w600c353535,
                ),
                Gap(30.h)
              ],
              TextField(
                controller: searchController,
                decoration: InputDecoration(
                  hintStyle: TextStyles.ts14w500cC6C6C6,
                  hintText: searchHintText,
                  prefixIcon: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Gap(15.w),
                      Image.asset(
                        "search".asIconPng(),
                        scale: 2,
                      ),
                      Gap(5.w),
                    ],
                  ),
                ),
                onChanged: (value) {
                  filteredItems.value = items
                      .where((item) => displayBuilder(item)
                          .toLowerCase()
                          .contains(value.toLowerCase()))
                      .toList();
                },
              ),
              SizedBox(height: 20.h),
              if (filteredItems.value.isEmpty) ...[
                Text(
                  tr(context, 'no_results_found'),
                  style: TextStyles.ts14w600c44322D,
                ),
                SizedBox(height: 20.h),
              ],
              ListView.builder(
                shrinkWrap: true,
                itemCount: filteredItems.value.length,
                physics: NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                itemBuilder: (context, index) {
                  final item = filteredItems.value[index];
                  return ListTile(
                    title: Text(displayBuilder(item)),
                    titleTextStyle: TextStyles.ts14w600c44322D,
                    onTap: () {
                      onChanged?.call(item);
                      Navigator.pop(context, item);
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
