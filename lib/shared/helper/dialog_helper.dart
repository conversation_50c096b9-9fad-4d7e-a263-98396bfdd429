import 'package:albalad_operator_app/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';

class DialogHelper {
  static showErrorDialog({
    required BuildContext context,
    required String message,
  }) {
    return showDialog(
      context: context,
      builder: (_) => AlertDialog.adaptive(
        title: Text(tr(context, 'headsup')),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(tr(context, 'okay')),
          ),
        ],
      ),
    );
  }
}
