import 'package:albalad_operator_app/shared/widgets/image_source_sheet.dart';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

class ImagePickerHelper {
  static Future<XFile?> pickImage(BuildContext context) async {
    final imageSource = await showModalBottomSheet(
      context: context,
      builder: (context) => ImageSourceSheet(),
    );
    if (imageSource == null) return null;
    final ImagePicker picker = ImagePicker();
    final XFile? image =
        await picker.pickImage(source: imageSource, maxWidth: 1000);
    if (image != null) {
      final croppedImage = await cropImage(image: image);
      if (croppedImage != null) {
        return XFile(croppedImage.path);
      }
      return null;
    }
    return null;
  }

  static Future<CroppedFile?> cropImage({required XFile image}) async {
    return ImageCropper().cropImage(
      sourcePath: image.path,
      uiSettings: [
        AndroidUiSettings(
          statusBarColor: Colors.black,
          initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: false,
        ),
        IOSUiSettings(),
      ],
    );
  }
}
