Map<String, String> englishToArabicMap = {
  "A": "ا",
  "B": "ب",
  "J": "ح",
  "D": "د",
  "R": "ر",
  "S": "س",
  "X": "ص",
  "T": "ط",
  "E": "ع",
  "G": "ق",
  "K": "ك",
  "L": "ل",
  "Z": "م",
  "N": "ن",
  "U": "و",
  "H": "ه",
  "V": "ى",
  "0": "٠",
  "1": "١",
  "2": "٢",
  "3": "٣",
  "4": "٤",
  "5": "٥",
  "6": "٦",
  "7": "٧",
  "8": "٨",
  "9": "٩"
};

Map<String, String> arabicToEnglishMap = {
  "ا": "A",
  "ب": "B",
  "ح": "J",
  "د": "D",
  "ر": "R",
  "س": "S",
  "ص": "X",
  "ط": "T",
  "ع": "E",
  "ق": "G",
  "ك": "K",
  "ل": "L",
  "م": "Z",
  "ن": "N",
  "و": "U",
  "ه": "H",
  "ى": "V",
  "٠": "0",
  "١": "1",
  "٢": "2",
  "٣": "3",
  "٤": "4",
  "٥": "5",
  "٦": "6",
  "٧": "7",
  "٨": "8",
  "٩": "9"
};

String convertToArabic(String englishText) {
  return englishText.toUpperCase().split("").map((char) {
    return englishToArabicMap[char] ??
        char; // Keep unknown characters unchanged
  }).join();
}

String convertToEnglish(String arabicText) {
  return arabicText.split("").map((char) {
    return arabicToEnglishMap[char] ?? char;
  }).join();
}
