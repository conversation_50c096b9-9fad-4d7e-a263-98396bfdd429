import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorageHelper {
  // Private constructor
  SecureStorageHelper._privateConstructor();

  // Singleton instance
  static final SecureStorageHelper instance =
      SecureStorageHelper._privateConstructor();

  // FlutterSecureStorage instance
  final _storage = const FlutterSecureStorage();

  // Optional: Initialize method (not strictly needed for flutter_secure_storage, but kept for consistency)
  Future<void> init() async {
    // No initialization required for FlutterSecureStorage as it’s ready to use
    // You could add platform-specific options here if needed (e.g., encryption settings)
  }

  // Save data to secure storage
  Future<bool> saveData(String key, dynamic value) async {
    try {
      // FlutterSecureStorage only supports strings, so convert other types to string
      String stringValue;
      if (value is String) {
        stringValue = value;
      } else if (value is int) {
        stringValue = value.toString();
      } else if (value is double) {
        stringValue = value.toString();
      } else if (value is bool) {
        stringValue = value.toString();
      } else if (value is List<String>) {
        stringValue = value.join(','); // Convert list to comma-separated string
      } else {
        return false; // Unsupported type
      }

      await _storage.write(key: key, value: stringValue);
      return true;
    } catch (e) {
      return false; // Handle any errors (e.g., storage unavailable)
    }
  }

  // Get data from secure storage
  Future<String?> getData(String key) async {
    try {
      final value = await _storage.read(key: key);
      return value; // Returns null if key doesn’t exist
    } catch (e) {
      return null; // Handle errors gracefully
    }
  }

  // Remove data from secure storage
  Future<bool> removeData(String key) async {
    try {
      await _storage.delete(key: key);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Clear all data from secure storage
  Future<bool> clearAll() async {
    try {
      await _storage.deleteAll();
      return true;
    } catch (e) {
      return false;
    }
  }
}
