import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferenceHelper {
  // Private constructor
  SharedPreferenceHelper._privateConstructor();

  // Singleton instance
  static final SharedPreferenceHelper instance =
      SharedPreferenceHelper._privateConstructor();

  // SharedPreferences instance
  SharedPreferences? _prefs;

  // Initialize SharedPreferences instance
  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Getter for SharedPreferences
  SharedPreferences? get prefs => _prefs;

  // Save data to SharedPreferences
  Future<bool> saveData(String key, dynamic value) async {
    if (_prefs == null) return false;

    if (value is String) {
      return await _prefs!.setString(key, value);
    } else if (value is int) {
      return await _prefs!.setInt(key, value);
    } else if (value is double) {
      return await _prefs!.setDouble(key, value);
    } else if (value is bool) {
      return await _prefs!.setBool(key, value);
    } else if (value is List<String>) {
      return await _prefs!.setStringList(key, value);
    }

    return false; // Return false for unsupported types
  }

  // Get data from SharedPreferences
  dynamic getData(String key) {
    if (_prefs == null) return null;
    return _prefs!.get(key);
  }

  // Remove data from SharedPreferences
  Future<bool> removeData(String key) async {
    if (_prefs == null) return false;
    return await _prefs!.remove(key);
  }

  // Clear all data from SharedPreferences
  Future<bool> clearAll() async {
    if (_prefs == null) return false;
    return await _prefs!.clear();
  }
}
