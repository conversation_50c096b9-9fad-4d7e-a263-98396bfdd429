// Custom observer class to handle lifecycle changes
import 'package:flutter/material.dart';

class AppLifecycleObserver with WidgetsBindingObserver {
  final void Function(AppLifecycleState) onStateChanged;

  AppLifecycleObserver({required this.onStateChanged});

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    onStateChanged(state); // Call the callback with the new state
  }
}
