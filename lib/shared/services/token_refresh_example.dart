import 'package:albalad_operator_app/shared/services/token_refresh_service.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:dio/dio.dart';

/// Example class showing how to use the refresh token system
/// Note: In most cases, you don't need to manually call refresh
/// as the AuthInterceptor handles it automatically
class TokenRefreshExample {
  final TokenRefreshService _tokenRefreshService = TokenRefreshService();
  final Dio _dio = DioClient().dio;

  /// Example of making an API call that will automatically handle token refresh
  Future<Response?> makeApiCallWithAutoRefresh(String endpoint) async {
    try {
      // This call will automatically refresh the token if it receives a 401
      final response = await _dio.get(endpoint);
      return response;
    } catch (e) {
      print('API call failed: $e');
      return null;
    }
  }

  /// Example of manually checking if token refresh is needed
  /// (This is rarely needed as the interceptor handles it automatically)
  Future<bool> manualTokenRefreshCheck() async {
    try {
      // Check if token is expired (implement your own logic)
      if (_tokenRefreshService.isTokenExpired()) {
        final newToken = await _tokenRefreshService.refreshAccessToken();
        return newToken != null;
      }
      return true; // Token is still valid
    } catch (e) {
      print('Manual token refresh failed: $e');
      return false;
    }
  }

  /// Example of making multiple concurrent API calls
  /// The refresh token service handles concurrent refresh attempts properly
  Future<List<Response?>> makeConcurrentApiCalls(List<String> endpoints) async {
    final futures = endpoints.map((endpoint) => makeApiCallWithAutoRefresh(endpoint));
    return await Future.wait(futures);
  }

  /// Example showing how the system handles different scenarios
  Future<void> demonstrateRefreshTokenScenarios() async {
    print('=== Refresh Token System Demo ===');

    // Scenario 1: Normal API call (token is valid)
    print('\n1. Making normal API call...');
    final response1 = await makeApiCallWithAutoRefresh('/profile');
    print('Response status: ${response1?.statusCode}');

    // Scenario 2: API call with expired token (will auto-refresh)
    print('\n2. Making API call that might need token refresh...');
    final response2 = await makeApiCallWithAutoRefresh('/home-page');
    print('Response status: ${response2?.statusCode}');

    // Scenario 3: Multiple concurrent calls (refresh handled once)
    print('\n3. Making multiple concurrent API calls...');
    final responses = await makeConcurrentApiCalls([
      '/profile',
      '/home-page',
      '/notifications',
    ]);
    print('Concurrent calls completed: ${responses.length}');

    // Scenario 4: Manual refresh check
    print('\n4. Manual token refresh check...');
    final refreshSuccess = await manualTokenRefreshCheck();
    print('Manual refresh result: $refreshSuccess');

    print('\n=== Demo Complete ===');
  }
}

/// Usage example in a widget or service
class ExampleUsage {
  final Dio _dio = DioClient().dio;

  /// Simple example of making API calls
  /// The refresh token system works transparently
  Future<Map<String, dynamic>?> fetchUserProfile() async {
    try {
      final response = await _dio.get('/profile');
      if (response.statusCode == 200) {
        return response.data;
      }
    } catch (e) {
      print('Failed to fetch profile: $e');
    }
    return null;
  }

  /// Example of POST request with form data
  Future<bool> updateProfile(Map<String, dynamic> profileData) async {
    try {
      final response = await _dio.post(
        '/profile-update',
        data: profileData,
      );
      return response.statusCode == 200;
    } catch (e) {
      print('Failed to update profile: $e');
      return false;
    }
  }

  /// Example of handling file upload
  Future<bool> uploadFile(String filePath) async {
    try {
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(filePath),
      });

      final response = await _dio.post(
        '/upload',
        data: formData,
      );
      return response.statusCode == 200;
    } catch (e) {
      print('Failed to upload file: $e');
      return false;
    }
  }
}
