import 'dart:developer';

import 'package:albalad_operator_app/features/valet/models/generate_valet_ticket_model.dart';
import 'package:albalad_operator_app/features/valet/models/valet_location.dart';
import 'package:albalad_operator_app/features/valet/models/valet_operator.dart';
import 'package:albalad_operator_app/features/violation/models/settlement_type.dart';
import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';
import 'package:dio/dio.dart';

class ValetServices {
  final Dio _dio = DioClient().dio;

  Future<List<ValetOperator>> fetchValetOperators() async {
    final response = await _dio.get(
      ApiConstants.valetOperators,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );

    if (response.statusCode == 200) {
      if (response.data['result'] == 'success') {
        List records = response.data['records'] ?? [];
        return records.map((e) => ValetOperator.fromJson(e)).toList();
      }
    }
    return [];
  }

  Future<List<ValetLocation>> fetchValetLocations() async {
    final response = await _dio.get(
      ApiConstants.valetLocations,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );

    if (response.statusCode == 200) {
      if (response.data['result'] == 'success') {
        List records = response.data['records'] ?? [];
        return records.map((e) => ValetLocation.fromJson(e)).toList();
      }
    }
    return [];
  }

  Future<GenerateValetTicketModel> generateValetTicket({
    required String valetUid,
  }) async {
    final response = await _dio.get(
      '${ApiConstants.generateTicket}?valet_uid=$valetUid',
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );

    log(response.data.toString());

    if (response.statusCode == 200) {
      final json = response.data;
      if (json['result'] == 'success') {
        return GenerateValetTicketModel.fromJson(json['data']);
      }
    }
    throw Exception(appLocalization.translate('failed_to_generate_ticket'));
  }

  CancelToken? _cancelToken;

  Future<Response> fetchCurrentValetVehicles(
      {required int page, String query = ''}) async {
    if (_cancelToken != null && !_cancelToken!.isCancelled) {
      _cancelToken!.cancel("Cancelled previous request");
    }
    // Create a new CancelToken for the current request
    _cancelToken = CancelToken();

    String apiURL = '${ApiConstants.valetVehicles}?page=$page';
    if (query.isNotEmpty) {
      apiURL = '$apiURL&search_data=$query';
    }
    final response = await _dio.get(
      apiURL,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
      cancelToken: _cancelToken,
    );
    return response;
  }

  CancelToken? _cancelToken1;

  Future<Response> fetchValetRequestList(
      {required int page, String query = ''}) async {
    if (_cancelToken1 != null && !_cancelToken1!.isCancelled) {
      _cancelToken1!.cancel("Cancelled previous request");
    }
    // Create a new CancelToken for the current request
    _cancelToken1 = CancelToken();

    String apiURL = '${ApiConstants.valetRequest}?page=$page';
    if (query.isNotEmpty) {
      apiURL = '$apiURL&search_data=$query';
    }
    final response = await _dio.get(
      apiURL,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
      cancelToken: _cancelToken1,
    );
    return response;
  }

  Future<List<SettlementType>> fetchValetSettlementStatus() async {
    final response = await _dio.get(
      ApiConstants.valetSettlementStatus,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    if (response.statusCode == 200) {
      Map<String, dynamic> statusMap = response.data;
      return statusMap.entries.map((entry) {
        return SettlementType(id: entry.key, name: entry.value);
      }).toList();
    } else {
      return [];
    }
  }
}
