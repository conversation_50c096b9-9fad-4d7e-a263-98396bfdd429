import 'package:albalad_operator_app/features/clamp_violation/models/clamped_details.dart';
import 'package:albalad_operator_app/features/tow_violation/models/towed_details.dart';
import 'package:albalad_operator_app/features/violation/models/enforcer.dart';
import 'package:albalad_operator_app/features/violation/models/parking_violation_detail.dart';
import 'package:albalad_operator_app/features/violation/models/payment_method.dart';
import 'package:albalad_operator_app/features/violation/models/settlement_type.dart';
import 'package:albalad_operator_app/features/violation/models/violation_type.dart';
import 'package:albalad_operator_app/features/violation/violation_type.dart';
import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:albalad_operator_app/shared/models/violation_details.dart';
import 'package:dio/dio.dart';

class ViolationServices {
  final Dio dio = DioClient().dio;
  Future<List<ViolationType>> fetchViolationTypes(VIOLATIONTYPE type) async {
    final response = await dio.get(
      '${ApiConstants.violationTypes}?action=${type.name}',
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    if (response.statusCode == 200) {
      List records = response.data['records'] ?? [];
      return records.map((e) => ViolationType.fromJson(e)).toList();
    } else {
      return [];
    }
  }

  Future<List<Enforcer>> fetchOperators(String action) async {
    final response = await dio.get(
      '${ApiConstants.listOperators}?action=$action',
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );

    if (response.statusCode == 200) {
      List records = response.data['records'] ?? [];
      return records.map((e) => Enforcer.fromJson(e)).toList();
    } else {
      return [];
    }
  }

  Future<ViolationDetails> fetchViolationDetails(String violationUid) async {
    final response = await dio.get(
      '${ApiConstants.currentViolationDetails}$violationUid/',
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    if (response.statusCode == 200) {
      final data = response.data;
      if (data['result'] == 'success') {
        return ViolationDetails.fromJson(data['records']);
      } else {
        throw Exception('Failed to fetch violation details');
      }
    } else {
      throw Exception('Failed to fetch violation details');
    }
  }

  Future<List<SettlementType>> fetchSettlementTypes() async {
    final response = await dio.get(
      ApiConstants.settlementTypes,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    if (response.statusCode == 200) {
      Map<String, dynamic> statusMap = response.data;
      return statusMap.entries.map((entry) {
        return SettlementType(id: entry.key, name: entry.value);
      }).toList();
    } else {
      return [];
    }
  }

  Future<List<PaymentMethod>> fetchPaymentMethods() async {
    final response = await dio.get(
      ApiConstants.paymentMethods,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    if (response.statusCode == 200) {
      final data = response.data;
      if (data['result'] == 'success') {
        List records = data['records'] ?? [];
        return records.map((e) => PaymentMethod.fromJson(e)).toList();
      } else {
        return [];
      }
    } else {
      return [];
    }
  }

  Future<Response> fetchCurrentViolations(
      {required int page, String? filterData}) async {
    String apiURL = '${ApiConstants.currentViolations}?page=$page';
    if (filterData != null) {
      apiURL += '&filter_data=$filterData';
    }
    final response = await dio.get(
      apiURL,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    return response;
  }

  Future<Response> fetchtViolationsAndNoticesVehicleList(
      {required int page}) async {
    String apiURL = '${ApiConstants.violationNoticesVehicleList}?page=$page';
    final response = await dio.get(
      apiURL,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    return response;
  }

  Future<Response> fetchParkingViolationList({required int page}) async {
    String apiURL = '${ApiConstants.parkingViolationList}?page=$page';
    final response = await dio.get(
      apiURL,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    return response;
  }

  Future<Response> fetchClampingRequestList({required int page}) async {
    String apiURL = '${ApiConstants.clampingRequestList}?page=$page';
    final response = await dio.get(
      apiURL,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    return response;
  }

  Future<Response> fetchAssignedClampingList({required int page}) async {
    String apiURL = '${ApiConstants.assignedClampingViolationList}?page=$page';
    final response = await dio.get(
      apiURL,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    return response;
  }

  Future<Response> fetchAssignedTowingList({required int page}) async {
    String apiURL = '${ApiConstants.assignedTowingViolationList}?page=$page';
    final response = await dio.get(
      apiURL,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    return response;
  }

  Future<Response> fetchTowingRequestList({required int page}) async {
    String apiURL = '${ApiConstants.listTowingRequest}?page=$page';
    final response = await dio.get(
      apiURL,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    return response;
  }

  Future<ParkingViolationDetail> fetchParkingViolationDetails(
      String violationUid) async {
    final response = await dio.get(
      '${ApiConstants.currentViolationDetails}$violationUid/',
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );

    if (response.statusCode == 200) {
      final data = response.data;
      if (data['result'] == 'success') {
        return ParkingViolationDetail.fromJson(data['records']);
      } else {
        throw Exception('Failed to fetch violation details');
      }
    } else {
      throw Exception('Failed to fetch violation details');
    }
  }

  Future<Response> fetchClampedVehicleList({required int page}) async {
    String apiURL = '${ApiConstants.clampedVehicleList}?page=$page';
    final response = await dio.get(
      apiURL,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    return response;
  }

  Future<Response> fetchTowClampedVehicleList({required int page}) async {
    String apiURL =
        '${ApiConstants.towClampedVehiclesList}?page=$page'; //towClampedVehiclesList
    final response = await dio.get(
      apiURL,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    return response;
  }

  Future<ClampedDetails> fetchClampedDetails(String uid) async {
    final response = await dio.get(
      '${ApiConstants.clampedVehicleDetails}$uid/',
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    if (response.statusCode == 200) {
      final data = response.data;
      if (data['result'] == 'success') {
        return ClampedDetails.fromJson(data['records']);
      } else {
        throw Exception('Failed to fetch clamped details');
      }
    } else {
      throw Exception('Failed to fetch clamped details');
    }
  }

  Future<Response> fetchTowedVehicleList({required int page}) async {
    String apiURL = '${ApiConstants.towedVehicleList}?page=$page';
    final response = await dio.get(
      apiURL,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    return response;
  }

  Future<TowedDetails> fetchTowedDetails(String uid) async {
    final response = await dio.get(
      '${ApiConstants.towedVehicleDetails}$uid/',
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    if (response.statusCode == 200) {
      final data = response.data;
      if (data['result'] == 'success') {
        return TowedDetails.fromJson(data['records']);
      } else {
        throw Exception('Failed to fetch clamped details');
      }
    } else {
      throw Exception('Failed to fetch clamped details');
    }
  }
}
