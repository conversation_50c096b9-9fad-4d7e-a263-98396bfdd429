import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/helper/secure_storage_helper.dart';
import 'package:albalad_operator_app/shared/models/logged_in_user.dart';
import 'package:albalad_operator_app/shared/general_functions.dart';

class TokenRefreshService {
  static final TokenRefreshService _instance = TokenRefreshService._internal();
  factory TokenRefreshService() => _instance;
  TokenRefreshService._internal();

  bool _isRefreshing = false;
  final List<Completer<String?>> _refreshCompleters = [];

  /// Refreshes the access token using the refresh token
  /// Returns the new access token if successful, null otherwise
  Future<String?> refreshAccessToken() async {
    // If already refreshing, wait for the current refresh to complete
    if (_isRefreshing) {
      final completer = Completer<String?>();
      _refreshCompleters.add(completer);
      return completer.future;
    }

    _isRefreshing = true;

    try {
      // Get current user data from secure storage
      final userDataString = await SecureStorageHelper.instance.getData('user');
      if (userDataString == null) {
        _completeAllRefreshRequests(null);
        return null;
      }

      final userData = jsonDecode(userDataString);
      final currentToken = Token.fromJson(userData['token'] ?? {});

      if (currentToken.refreshToken == null) {
        _completeAllRefreshRequests(null);
        return null;
      }

      // Create a new Dio instance without interceptors to avoid circular dependency
      final dio = Dio();

      // Prepare form data for refresh token request
      final formData = FormData.fromMap({
        'refresh_token': currentToken.refreshToken!,
      });

      // Make the refresh token request
      final response = await dio.post(
        ApiConstants.refreshToken,
        data: formData,
        options: Options(
          headers: await _getRefreshHeaders(),
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data;

        // Check if the response indicates success
        if (responseData['result'] == 'success' &&
            responseData['token'] != null) {
          // Update the token in the user data
          final newToken = Token.fromJson(responseData['token']);
          userData['token'] = newToken.toJson();

          // Update LoggedInUser singleton
          LoggedInUser.fromJson(userData);

          // Save updated user data to secure storage
          await SecureStorageHelper.instance
              .saveData('user', jsonEncode(userData));

          final newAccessToken = newToken.accessToken;
          _completeAllRefreshRequests(newAccessToken);
          return newAccessToken;
        }
      }

      // If we reach here, refresh failed
      _completeAllRefreshRequests(null);
      return null;
    } catch (e) {
      // Handle any errors during refresh
      _completeAllRefreshRequests(null);
      return null;
    } finally {
      _isRefreshing = false;
    }
  }

  /// Complete all pending refresh requests
  void _completeAllRefreshRequests(String? token) {
    for (final completer in _refreshCompleters) {
      if (!completer.isCompleted) {
        completer.complete(token);
      }
    }
    _refreshCompleters.clear();
  }

  /// Get headers for refresh token request
  Future<Map<String, String>> _getRefreshHeaders() async {
    return {
      'time-zone': await getTimezone(),
      'X-Client-Type': 'mobile',
      'X-Environment':
          ApiConstants.currentEnvironment.toString().split('.').last,
      'Accept-Language': _getLanguageCode(),
      'Content-Type': 'application/x-www-form-urlencoded',
    };
  }

  /// Get language code from shared preferences
  String _getLanguageCode() {
    // We can't use SharedPreferenceHelper here due to potential circular dependency
    // So we'll use a default value
    return 'en';
  }

  /// Check if the current token is expired or about to expire
  bool isTokenExpired() {
    try {
      final token = LoggedInUser.token;
      if (token?.expiresIn == null) return false;

      // Parse expires_in (assuming it's in seconds from token creation)
      // You might need to adjust this based on your token format
      final expiresIn = int.tryParse(token!.expiresIn!);
      if (expiresIn == null) return false;

      // For simplicity, we'll consider token expired if it's close to expiry
      // You might want to store the token creation time and calculate properly
      return false; // Implement based on your token expiry logic
    } catch (e) {
      return false;
    }
  }
}
