import 'dart:io';

import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';

import 'package:dio/dio.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FcmServices {
  static FirebaseMessaging messaging = FirebaseMessaging.instance;

  static Future<String?> token() async {
    return await messaging.getToken();
  }

  static subscribeEnglishTopic() async {
    await messaging.subscribeToTopic('operator_en');
    await messaging.unsubscribeFromTopic('operator_ar');
  }

  static subscribeArabicTopic() async {
    await messaging.subscribeToTopic('operator_ar');
    await messaging.unsubscribeFromTopic('operator_en');
  }

  static Future<void> storeFCMToken(String fcm) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString('fcm', fcm);
  }

  //---------------------------------------------Dio--------------------------------------------------------//
  Dio dio = DioClient().dio;
  //---------------------------------------------APIs--------------------------------------------------------//
  Future<void> refreshFcmToken() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? currentToken = prefs.getString('fcm');
      String? newToken = await token();

      if (newToken != null && currentToken != newToken) {
        Map<String, dynamic> data = {
          'fcm_token': newToken,
        };
        // Create an instance of DeviceInfoPlugin to retrieve device-specific information.
        DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

        // Check if the platform is Android.
        if (Platform.isAndroid) {
          // Retrieve Android-specific device information.
          AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;

          // Add the device ID (unique identifier) to the request body.
          data['primary'] = androidInfo.id;
        } else {
          // Retrieve iOS-specific device information.
          IosDeviceInfo iosInfo = await deviceInfo.iosInfo;

          // Add the unique identifier for the iOS device to the request body.
          data['primary'] = iosInfo.identifierForVendor;
        }
        await dio.post(
          ApiConstants.refreshFcmToken,
          data: data,
          options: Options(
            headers: await ApiConstants.authHeaders(),
          ),
        );
        prefs.setString('fcm', newToken);
      }
    } catch (e) {
      debugPrint('$e');
    }
  }

  static Future<void> requestPermission() async {
    NotificationSettings settings = await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      debugPrint('User granted permission');
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      debugPrint('User granted provisional permission');
    } else {
      debugPrint('User declined or has not accepted permission');
    }
    // debugPrint('User granted permission: ${settings.authorizationStatus} fcm');
  }

  static init() async {
    await requestPermission();
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }

  @pragma('vm:entry-point')
  static Future<void> _firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {
    // If you're going to use other Firebase services in the background, such as Firestore,
    // make sure you call `initializeApp` before using other Firebase services.
    await Firebase.initializeApp();
  }
}
