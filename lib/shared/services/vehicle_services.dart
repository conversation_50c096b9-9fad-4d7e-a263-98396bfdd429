import 'package:albalad_operator_app/features/add_vehicle/models/registered_counrty.dart';
import 'package:albalad_operator_app/features/add_vehicle/models/vehicle_color.dart';
import 'package:albalad_operator_app/features/add_vehicle/models/vehicle_plate_type.dart';
import 'package:albalad_operator_app/features/add_vehicle/models/vehicle_type.dart';
import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:dio/dio.dart';

class VehicleServices {
  CancelToken? _cancelToken;

  final Dio dio = DioClient().dio;
  Future<Response> fetchParkedVehicles(
      {String query = '', int page = 1}) async {
    // Cancel the previous request if it exists
    if (_cancelToken != null && !_cancelToken!.isCancelled) {
      _cancelToken!.cancel("Cancelled previous request");
    }
    // Create a new CancelToken for the current request
    _cancelToken = CancelToken();
    String apiURL = '${ApiConstants.parkingVehicleList}?page=$page';
    if (query.isNotEmpty) {
      apiURL = '$apiURL&search_data=$query';
    }
    return await dio.get(
      apiURL,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
      cancelToken: _cancelToken,
    );
  }

  Future<Response> fetchVehicleDetails(
      {String? uid, String? vehicleNumber}) async {
    String apiURL = ApiConstants.vehicleDetails;
    if (uid != null) {
      apiURL = '$apiURL?vehicle_uid=$uid';
    } else if (vehicleNumber != null) {
      apiURL = '$apiURL?vehicle_number=$vehicleNumber';
    }
    final response = await dio.get(
      apiURL,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    return response;
  }

  Future<List<VehiclePlateType>> fetchVehiclePlateTypes() async {
    final response = await dio.get(
      ApiConstants.vehiclePlateType,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    if (response.statusCode == 200) {
      final json = response.data;
      List records = json['records'] ?? [];
      return records.map((e) {
        return VehiclePlateType.fromJson(e);
      }).toList();
    } else {
      return [];
    }
  }

  Future<List<RegisteredCountry>> fetchRegisteredCountry() async {
    final response = await dio.get(
      ApiConstants.countries,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    if (response.statusCode == 200) {
      final json = response.data;
      List records = json['records'] ?? [];
      return records.map((e) {
        return RegisteredCountry.fromJson(e);
      }).toList();
    } else {
      return [];
    }
  }

  Future<List<VehicleColor>> fetchVehicleColors() async {
    final response = await dio.get(
      ApiConstants.vehicleColor,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    if (response.statusCode == 200) {
      final json = response.data;
      List records = json['records'] ?? [];
      return records.map((e) {
        return VehicleColor.fromJson(e);
      }).toList();
    } else {
      return [];
    }
  }

  Future<List<VehicleType>> fetchVehicleTypes() async {
    final response = await dio.get(
      ApiConstants.vehicleType,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    if (response.statusCode == 200) {
      final json = response.data;
      List records = json['records'] ?? [];
      return records.map((e) {
        return VehicleType.fromJson(e);
      }).toList();
    } else {
      return [];
    }
  }
}
