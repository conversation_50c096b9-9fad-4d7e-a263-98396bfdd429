import 'package:albalad_operator_app/features/support_center/models/contact_admin.dart';
import 'package:albalad_operator_app/features/support_center/models/faq.dart';
import 'package:albalad_operator_app/features/support_center/models/privacy_policy.dart';
import 'package:albalad_operator_app/features/support_center/models/terms_and_conditions.dart';
import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:dio/dio.dart';

class LegalServices {
  final Dio dio = DioClient().dio;
  Future<TermsAndConditions?> fetchTermsAndConditions() async {
    final response = await dio.get(
      ApiConstants.termsAndConditions,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    if (response.statusCode == 200) {
      final data = response.data;
      return TermsAndConditions.fromJson(data['records']);
    }
    return null;
  }

  Future<PrivacyPolicy?> fetchPrivacyPolicy() async {
    final response = await dio.get(
      ApiConstants.privacyPolicy,
      options: Options(
        headers: await ApiConstants.authHeaders(),
      ),
    );
    if (response.statusCode == 200) {
      final data = response.data;
      return PrivacyPolicy.fromJson(data['records']);
    }
    return null;
  }

  Future<List<FAQ>> fetchFAQs() async {
    final response = await dio.get(
      ApiConstants.faqs,
      options: Options(headers: await ApiConstants.authHeaders()),
    );
    if (response.statusCode == 200) {
      final data = response.data;
      List records = data['records'];
      return records.map((e) => FAQ.fromJson(e)).toList();
    } else {
      return [];
    }
  }

  Future<ContactAdmin?> contactUs() async {
    final response = await dio.get(
      ApiConstants.contactUs,
      options: Options(headers: await ApiConstants.authHeaders()),
    );
    if (response.statusCode == 200) {
      final data = response.data;
      if (data['result'] == 'success') {
        return ContactAdmin.fromJson(data['records']);
      }
      return null;
    } else {
      return null;
    }
  }
}
