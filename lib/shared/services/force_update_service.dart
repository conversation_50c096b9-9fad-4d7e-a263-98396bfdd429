// final response = await DioClient().dio.get(
//               ApiConstants.mobileApplicationDetail,
//             );
//         log(response.data.toString());

import 'dart:developer';
import 'dart:io';

import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:pub_semver/pub_semver.dart';

class ForceUpdateService {
  static Future<bool> isUpdateAvailable() async {
    try {
      final dio = DioClient().dio;
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      final response = await dio.get(
        ApiConstants.mobileApplicationDetail,
      );

      if (response.statusCode == 200) {
        final json = response.data;
        if (json['result'] == 'success') {
          final records = json['records'];
          final appVersion = Version.parse(packageInfo.version);
          final serverVersion = Platform.isAndroid
              ? Version.parse(records['android_version'])
              : Version.parse(records['ios_version']);
          return appVersion < serverVersion;
        }
      }

      return false;
    } catch (e) {
      log(e.toString());
      return false;
    }
  }
}
