# Refresh Token Integration

This document explains how the refresh token system has been integrated into the DIO client.

## Overview

The refresh token system automatically handles token refresh when API calls receive a 401 (Unauthorized) response. This ensures seamless user experience by automatically refreshing expired access tokens without requiring the user to log in again.

## Components

### 1. TokenRefreshService (`lib/shared/services/token_refresh_service.dart`)
- Singleton service that handles token refresh logic
- Prevents multiple simultaneous refresh requests
- Manages refresh token API calls
- Updates stored user data with new tokens

### 2. Updated AuthInterceptor (`lib/shared/dio/auth_interceptor.dart`)
- Intercepts 401 errors from API responses
- Attempts to refresh the token automatically
- Retries the original request with the new token
- Falls back to navigation to sign-in if refresh fails

### 3. AuthServices (`lib/features/authentication/services/auth_services.dart`)
- Added `refreshToken()` method to handle the API call
- Uses form data as specified in the API requirements

### 4. API Constants (`lib/shared/constants/api_constants.dart`)
- Added refresh token endpoint: `refresh-token/`

## API Specification

**Endpoint:** `refresh-token/`
**Method:** POST
**Content-Type:** `multipart/form-data`
**Form Data:**
- `refresh_token`: The refresh token string

## How It Works

1. **API Call Made**: Any API call is made through the DIO client
2. **401 Response**: If the server returns 401 (token expired)
3. **Automatic Refresh**: AuthInterceptor catches the error and calls TokenRefreshService
4. **Token Refresh**: Service makes a call to `refresh-token/` endpoint with the current refresh token
5. **Update Storage**: If successful, new tokens are stored in secure storage and LoggedInUser singleton
6. **Retry Original Request**: The original API call is retried with the new access token
7. **Fallback**: If refresh fails, user is navigated to sign-in screen

## Token Storage

Tokens are stored in:
- **Secure Storage**: Using `SecureStorageHelper` for persistent storage
- **LoggedInUser Singleton**: For runtime access throughout the app

## Error Handling

- **Network Errors**: Handled gracefully, falls back to sign-in
- **Invalid Refresh Token**: Navigates to sign-in screen
- **Multiple Refresh Attempts**: Prevented by singleton pattern and refresh state management

## Usage

The refresh token system works automatically. No additional code is required in your API calls. Just use the DIO client as usual:

```dart
// This will automatically handle token refresh if needed
final response = await dio.get('/some-endpoint');
```

## Testing

To test the refresh token functionality:

1. Make an API call with an expired access token
2. Verify that the system automatically refreshes the token
3. Verify that the original request is retried successfully
4. Test with an invalid refresh token to ensure fallback to sign-in

## Security Considerations

- Refresh tokens are stored in secure storage (encrypted)
- Only one refresh attempt is made per request to prevent infinite loops
- Failed refresh attempts immediately redirect to sign-in for security
