import 'dart:convert';

import 'package:albalad_operator_app/language/language_data.dart';
import 'package:albalad_operator_app/language/language_storage.dart';
import 'package:albalad_operator_app/shared/constants/api_constants.dart';
import 'package:albalad_operator_app/shared/dio/dio_client.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class LanguageServices {
  static final _dio = DioClient().dio;
  static Future<void> syncLanguageData() async {
    try {
      final response = await _dio.get(
        ApiConstants.languageTranslation,
        options: Options(
          headers: await ApiConstants.headers(),
        ),
      );

      if (response.statusCode == 200) {
        final apiResponse = response.data;

        final languageData = LanguageData.fromJson(apiResponse);

        // Check if stored data exists and is different
        final storedData = await LanguageStorage.getLanguageData();
        if (storedData == null ||
            jsonEncode(storedData.toJson()) !=
                jsonEncode(languageData.toJson())) {
          await LanguageStorage.saveLanguageData(
              languageData); // Update if different
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print(
          "Network error: $e. Falling back to SharedPreferences or .arb files.",
        );
      }
      // If API fails, we'll rely on SharedPreferences or .arb files
    }
  }
}
