import 'dart:async';
import 'dart:io';

import 'package:albalad_operator_app/app/app.dart';
import 'package:albalad_operator_app/language/app_localizations.dart';
import 'package:albalad_operator_app/shared/constants/countries.dart';
import 'package:albalad_operator_app/shared/constants/phone_lengths.dart';
import 'package:albalad_operator_app/shared/constants/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:android_intent_plus/android_intent.dart';

final appLocalization = getIt<AppLocalizations>();

Future<String> getTimezone() async {
  final String currentTimeZone = await FlutterTimezone.getLocalTimezone();
  return currentTimeZone;
}

Timer? _debounce;

Future<void> makePhoneCall(String phoneNumber) async {
  if (_debounce?.isActive ?? false) _debounce!.cancel();
  _debounce = Timer(const Duration(milliseconds: 500), () {
    openDialer(phoneNumber);
  });
}

Future<void> openDialer(String phoneNumber) async {
  try {
    if (Platform.isAndroid) {
      final intent = AndroidIntent(
        action: 'android.intent.action.DIAL',
        data: Uri.parse('tel:$phoneNumber').toString(),
      );
      await intent.launch();
    } else {
      final Uri launchUri = Uri(
        scheme: 'tel',
        path: phoneNumber,
      );
      if (await canLaunchUrl(launchUri)) {
        await launchUrl(launchUri);
      } else {
        throw 'Could not launch $launchUri';
      }
    }
  } catch (e) {
    debugPrint(e.toString());
  }
}

//The function obfuscateEmail takes an email address as an argument
//The function returns a string
//The function splits the email address into two parts: the username and the domain
//If the username is too short (less than or equal to 2 characters), the function obfuscates the entire username
//Otherwise, the function keeps the first and last characters of the username and obfuscates the rest
//The function then returns the obfuscated email address
String obfuscateEmail(String email) {
  final emailParts = email.split('@');
  if (emailParts.length != 2) {
    throw const FormatException('Invalid email format');
  }

  final username = emailParts[0];
  final domain = emailParts[1];

  if (username.length <= 2) {
    // If the username is too short, obfuscate entirely
    return '***@$domain';
  }

  // Keep the first and last characters of the username, obfuscate the rest
  final obfuscatedUsername =
      '${'.' * (username.length - 2)}${username[username.length - 2]}${username[username.length - 1]}';

  return '$obfuscatedUsername@$domain';
}

//The function maskMobileNumber takes a mobile number as an argument
//The function returns a string
//The function masks all but the last two characters of the mobile number with asterisks
//The function then returns the masked mobile number
String maskMobileNumber(String mobileNumber) {
  if (mobileNumber.length < 2) {
    return mobileNumber; // Return as-is if the number is too short
  }
  return '.' * (mobileNumber.length - 3) +
      mobileNumber.substring(mobileNumber.length - 3);
}

//Write a dart function to convert seconds to hours
//The function takes an integer value as an argument
//The function returns an integer value
//The function divides the seconds by 3600 and returns the result
//The result is the number of hours
int secondsToHours(int seconds) {
  return seconds ~/ 3600;
}

String convertSecondsToTime(int seconds) {
  if (seconds < 3600) {
    int minutes = (seconds / 60).floor(); // Convert to minutes
    return '$minutes Min';
  } else {
    double hours = seconds / 3600; // Convert to hours
    String hoursString = hours % 1 == 0
        ? hours.toInt().toString() // Remove decimal if it's zero
        : hours.toStringAsFixed(1); // Keep one decimal if needed
    return '$hoursString Hr';
  }
}

showPermissionErrorMessage(
    {required BuildContext context, required String message}) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(
        message,
        style: TextStyles.ts16w400cFFFFFF,
      ),
      backgroundColor: Colors.red,
    ),
  );
}

String formatPhoneCode(String phoneNumber) {
  if (phoneNumber.isEmpty || phoneNumber == 'None') {
    return '--';
  }
  Map<String, String> foundedCountry = {};
  for (var country in Countries.allCountries) {
    String dialCode = country["dial_code"].toString();
    if (phoneNumber.contains(dialCode)) {
      foundedCountry = country;
    }
  }

  if (foundedCountry.isNotEmpty) {
    var dialCode = phoneNumber.substring(
      0,
      foundedCountry["dial_code"]!.length,
    );
    var newPhoneNumber = phoneNumber.substring(
      foundedCountry["dial_code"]!.length,
    );
    return '$dialCode $newPhoneNumber';
  }
  return phoneNumber;
}

/// Get the phone number length based on country ISO code
int getPhoneNumberLength(String isoCode) {
  return countryLengths[isoCode] ?? 10; // Default to 10 if not found
}

class CalculatedTime {
  int totalSeconds;
  int remainingSeconds;
  CalculatedTime({required this.totalSeconds, required this.remainingSeconds});
}

CalculatedTime calculateSecondsFromNow(String startTime, String endTime) {
  // Parse the start and end times
  DateTime start = DateTime.parse(startTime);
  DateTime end = DateTime.parse(endTime);

  // Get the current time
  DateTime now = DateTime.now();

  // Calculate the total duration between start and end times
  Duration totalDuration = end.difference(start);

  // Calculate the remaining duration from now to the end time
  Duration remainingDuration = end.difference(now);

  // Return the total and remaining seconds
  if (remainingDuration.isNegative) {
    return CalculatedTime(
      totalSeconds: totalDuration.inSeconds,
      remainingSeconds: 0,
    );
  }
  return CalculatedTime(
    totalSeconds: totalDuration.inSeconds,
    remainingSeconds: remainingDuration.inSeconds,
  );
}

Color hexToColor(String hex) {
  try {
    hex = hex.replaceAll("#", ""); // Remove #
    if (hex.length == 6) {
      hex = "FF$hex"; // Add alpha (FF for full opacity)
    }
    return Color(int.parse(hex, radix: 16));
  } catch (e) {
    return Colors.white;
  }
}
