import 'package:albalad_operator_app/language/language_data.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class LanguageStorage {
  static const String _languageKey = 'language_data';

  // Save language data to SharedPreferences
  static Future<void> saveLanguageData(LanguageData data) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = jsonEncode(data.toJson());
    await prefs.setString(_languageKey, jsonString);
  }

  // Retrieve language data from SharedPreferences
  static Future<LanguageData?> getLanguageData() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = prefs.getString(_languageKey);
    if (jsonString != null) {
      final jsonMap = jsonDecode(jsonString);
      return LanguageData.fromJson(jsonMap);
    }
    return null;
  }

  // Clear language data (optional)
  static Future<void> clearLanguageData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_languageKey);
  }
}
