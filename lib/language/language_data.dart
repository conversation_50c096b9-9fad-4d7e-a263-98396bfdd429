class LanguageData {
  final Map<String, String> en;
  final Map<String, String> ar;

  LanguageData({required this.en, required this.ar});

  factory LanguageData.fromJson(Map<String, dynamic> json) {
    return LanguageData(
      en: Map<String, String>.from(json['en']),
      ar: Map<String, String>.from(json['ar']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'en': en,
      'ar': ar,
    };
  }
}
