import 'package:albalad_operator_app/language/language_data.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class AppLocalizations {
  final Map<String, String>? apiTranslations; // From API/SharedPreferences
  final Locale locale;

  AppLocalizations(this.apiTranslations, this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  String translate(String key) {
    // Use API translations if available, otherwise fallback to .arb
    return apiTranslations?[key] ??
        Intl.message(key, name: key, locale: locale.languageCode);
  }
}

class AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  final LanguageData? languageData; // Nullable to handle missing data
  final Locale locale;

  AppLocalizationsDelegate(this.languageData, this.locale);

  @override
  bool isSupported(Locale locale) => ['en', 'ar'].contains(locale.languageCode);

  @override
  Future<AppLocalizations> load(Locale locale) async {
    // Load translations from languageData if available
    final translations = languageData != null
        ? (locale.languageCode == 'en' ? languageData!.en : languageData!.ar)
        : null;

    // Initialize Intl with .arb files
    Intl.defaultLocale = locale.languageCode;
    return AppLocalizations(translations, locale);
  }

  @override
  bool shouldReload(covariant LocalizationsDelegate<AppLocalizations> old) =>
      true;
}
