name: albalad_operator_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_localizations:
    sdk: flutter
  intl: any
  flutter_riverpod: ^2.6.1
  dio: ^5.7.0
  shared_preferences: ^2.3.3
  flutter_screenutil: ^5.9.3
  flutter_svg: ^2.0.16
  firebase_core: ^3.9.0
  firebase_messaging: ^15.1.6
  circular_gradient_spinner: ^0.0.3
  hooks_riverpod: ^2.6.1
  flutter_hooks: ^0.20.5
  riverpod_annotation: ^2.6.1
  gap: ^3.0.1
  encrypt: ^5.0.3
  device_info_plus: ^11.2.0
  flutter_timezone: ^4.0.0
  easy_date_formatter: ^0.0.1
  pinput: ^5.0.0
  url_launcher: ^6.3.1
  get_it: ^8.0.3
  flutter_easyloading: ^3.0.5
  skeletonizer: ^2.0.1
  cached_network_image: ^3.4.1
  flutter_scalable_ocr:
    path: flutter_scalable_ocr
  flutter_html: ^3.0.0-beta.2
  image_picker: ^1.1.2
  image_cropper: ^8.1.0
  dotted_line: ^3.2.3
  photo_view: ^0.15.0
  qr_flutter: ^4.1.0
  qr_code_scanner_plus: ^2.0.9+1
  geolocator: ^13.0.2
  country_picker: ^2.0.27
  phone_numbers_parser: ^9.0.3
  flutter_local_notifications: ^18.0.1
  android_intent_plus: ^5.3.0
  get_time_ago: ^2.3.0
  # bx_btprinter: ^0.0.8
  print_bluetooth_thermal: ^1.1.6
  esc_pos_utils_plus: ^2.0.4
  app_settings: ^5.2.0
  flutter_secure_storage: ^9.2.4
  package_info_plus: ^8.3.0
  pub_semver: ^2.2.0
  store_redirect: ^2.0.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  riverpod_generator: ^2.6.3
  build_runner: ^2.4.14
  custom_lint: ^0.7.0
  riverpod_lint: ^2.6.3

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/svg/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: SF-Pro-Display
      fonts:
        - asset: assets/fonts/SF-Pro-Display/FontsFree-Net-SFProDisplay-Regular.ttf
        - asset: assets/fonts/SF-Pro-Display/SF-Pro-Display-Black.otf
          weight: 900
        - asset: assets/fonts/SF-Pro-Display/SF-Pro-Display-BlackItalic.otf
          weight: 900
          style: italic
        - asset: assets/fonts/SF-Pro-Display/SF-Pro-Display-BoldItalic.otf
          weight: 700
          style: italic
        - asset: assets/fonts/SF-Pro-Display/SF-Pro-Display-Heavy.otf
          weight: 800
        - asset: assets/fonts/SF-Pro-Display/SF-Pro-Display-HeavyItalic.otf
          weight: 800
          style: italic
        - asset: assets/fonts/SF-Pro-Display/SF-Pro-Display-Light.otf
          weight: 300
        - asset: assets/fonts/SF-Pro-Display/SF-Pro-Display-LightItalic.otf
          weight: 300
          style: italic
        - asset: assets/fonts/SF-Pro-Display/SF-Pro-Display-Medium.otf
          weight: 500
        - asset: assets/fonts/SF-Pro-Display/SF-Pro-Display-MediumItalic.otf
          weight: 500
          style: italic
        - asset: assets/fonts/SF-Pro-Display/SF-Pro-Display-RegularItalic.otf
          weight: 400
          style: italic
        - asset: assets/fonts/SF-Pro-Display/SF-Pro-Display-Semibold.otf
          weight: 600
        - asset: assets/fonts/SF-Pro-Display/SF-Pro-Display-Thin.otf
          weight: 100
        - asset: assets/fonts/SF-Pro-Display/SF-Pro-Display-ThinItalic.otf
          weight: 100
          style: italic
        - asset: assets/fonts/SF-Pro-Display/SF-Pro-Display-Ultralight.otf
          weight: 200
        - asset: assets/fonts/SF-Pro-Display/SF-Pro-Display-UltralightItalic.otf
          weight: 200
          style: italic
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
