<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Makani Operators</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>albalad_operator_app</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>sms</string>
		<string>tel</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Bluetooth access is required to connect to the printer.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>Bluetooth access is required to connect to the printer.</string>
	<key>NSCameraUsageDescription</key>
	<string>Camera permission is required for scanning vehicle number</string>
	<key>NSLocationTemporaryUsageDescriptionDictionary</key>
	<dict>
		<key>UpdateTowedVehicleLocation</key>
		<string>This app temporarily requires precise location access to update the current location of the towed vehicle.</string>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app requires access to your location to update the towed vehicle's current position accurately.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>This app requires access to your location to update the towed vehicle's current position accurately.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Microphone permission is required for scanning vehicle number</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>fetch</string>
		<!-- <string>bluetooth-central</string>
    	<string>bluetooth-peripheral</string> -->
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
</dict>
</plist>
