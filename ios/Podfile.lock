PODS:
  - app_settings (5.1.1):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/CoreOnly (11.6.0):
    - FirebaseCore (~> 11.6.0)
  - Firebase/Messaging (11.6.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.6.0)
  - firebase_core (3.10.1):
    - Firebase/CoreOnly (= 11.6.0)
    - Flutter
  - firebase_messaging (15.2.1):
    - Firebase/Messaging (= 11.6.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.6.0):
    - FirebaseCoreInternal (~> 11.6.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreInternal (11.6.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.6.0):
    - FirebaseCore (~> 11.6.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - flutter_timezone (0.0.1):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - google_mlkit_commons (0.9.0):
    - Flutter
    - MLKitVision
  - google_mlkit_text_recognition (0.14.0):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/TextRecognition (~> 7.0.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMLKit/MLKitCore (7.0.0):
    - MLKitCommon (~> 12.0.0)
  - GoogleMLKit/TextRecognition (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitTextRecognition (~> 5.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (3.5.0)
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - image_picker_ios (0.0.1):
    - Flutter
  - MLImage (1.0.0-beta6)
  - MLKitCommon (12.0.0):
    - GoogleDataTransport (~> 10.0)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/Logger (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitTextRecognition (5.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitTextRecognitionCommon (= 4.0.0)
    - MLKitVision (~> 8.0)
  - MLKitTextRecognitionCommon (4.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitVision (8.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta6)
    - MLKitCommon (~> 12.0)
  - MTBBarcodeScanner (5.0.11)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - print_bluetooth_thermal (0.0.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - qr_code_scanner_plus (0.2.6):
    - Flutter
    - MTBBarcodeScanner
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - store_redirect (0.0.1):
    - Flutter
  - TOCropViewController (2.7.4)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_timezone (from `.symlinks/plugins/flutter_timezone/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - google_mlkit_commons (from `.symlinks/plugins/google_mlkit_commons/ios`)
  - google_mlkit_text_recognition (from `.symlinks/plugins/google_mlkit_text_recognition/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - print_bluetooth_thermal (from `.symlinks/plugins/print_bluetooth_thermal/ios`)
  - qr_code_scanner_plus (from `.symlinks/plugins/qr_code_scanner_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - store_redirect (from `.symlinks/plugins/store_redirect/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GTMSessionFetcher
    - MLImage
    - MLKitCommon
    - MLKitTextRecognition
    - MLKitTextRecognitionCommon
    - MLKitVision
    - MTBBarcodeScanner
    - nanopb
    - PromisesObjC
    - TOCropViewController

EXTERNAL SOURCES:
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_timezone:
    :path: ".symlinks/plugins/flutter_timezone/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  google_mlkit_commons:
    :path: ".symlinks/plugins/google_mlkit_commons/ios"
  google_mlkit_text_recognition:
    :path: ".symlinks/plugins/google_mlkit_text_recognition/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  print_bluetooth_thermal:
    :path: ".symlinks/plugins/print_bluetooth_thermal/ios"
  qr_code_scanner_plus:
    :path: ".symlinks/plugins/qr_code_scanner_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  store_redirect:
    :path: ".symlinks/plugins/store_redirect/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  app_settings: 58017cd26b604ae98c3e65acbdd8ba173703cc82
  camera_avfoundation: dd002b0330f4981e1bbcb46ae9b62829237459a4
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  Firebase: 374a441a91ead896215703a674d58cdb3e9d772b
  firebase_core: e2aa06dbd854d961f8ce46c2e20933bee1bf2d2b
  firebase_messaging: 96cf6d67121b3f39746b2a4f29a26c0eee4af70e
  FirebaseCore: 48b0dd707581cf9c1a1220da68223fb0a562afaa
  FirebaseCoreInternal: d98ab91e2d80a56d7b246856a8885443b302c0c2
  FirebaseInstallations: efc0946fc756e4d22d8113f7c761948120322e8c
  FirebaseMessaging: e1aca1fcc23e8b9eddb0e33f375ff90944623021
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_local_notifications: df98d66e515e1ca797af436137b4459b160ad8c9
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  flutter_timezone: ac3da59ac941ff1c98a2e1f0293420e020120282
  geolocator_apple: 9bcea1918ff7f0062d98345d238ae12718acfbc1
  google_mlkit_commons: 384e4e206e122b6dad430d3158205e0b2fac6789
  google_mlkit_text_recognition: e540f2aff997f2b0daaa1b4fd5ead3b0b7030adc
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMLKit: eff9e23ec1d90ea4157a1ee2e32a4f610c5b3318
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_cropper: 37d40f62177c101ff4c164906d259ea2c3aa70cf
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  MLImage: 0ad1c5f50edd027672d8b26b0fee78a8b4a0fc56
  MLKitCommon: 07c2c33ae5640e5380beaaa6e4b9c249a205542d
  MLKitTextRecognition: 3b41f3ff084a79afb214408d25d2068d77ab322c
  MLKitTextRecognitionCommon: cd44577a8c506fc6bba065096de03bec0d01a213
  MLKitVision: 45e79d68845a2de77e2dd4d7f07947f0ed157b0e
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  print_bluetooth_thermal: 54a9ba9436479dd633d18f393669ee793ee498fa
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  qr_code_scanner_plus: 3bfe4deb7f28996a63a2a580819d49dae80d5ed3
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  store_redirect: 2977747cf81689a39bd62c248c2deacb7a0d131e
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: f2095d9946016fd9e5adc1075579dbb37c2bbacc

COCOAPODS: 1.16.2
